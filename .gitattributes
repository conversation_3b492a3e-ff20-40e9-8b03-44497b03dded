# Auto detect text files and perform LF normalization
* text=auto eol=lf

# Explicitly declare text files you want to always be normalized and converted
# to native line endings on checkout.
*.js text
*.ts text
*.tsx text
*.json text
*.md text
*.yml text
*.yaml text
*.html text
*.css text
*.scss text
*.rs text
*.toml text

# Denote all files that are truly binary and should not be modified.
*.png binary
*.jpg binary
*.jpeg binary
*.gif binary
*.ico binary
*.woff binary
*.woff2 binary
*.ttf binary
*.eot binary
*.pdf binary
*.zip binary
*.gz binary
