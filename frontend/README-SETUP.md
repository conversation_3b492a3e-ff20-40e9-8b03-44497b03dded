# ADC Multi-Languages Web Application

This is a Next.js web application for the ADC Multi-Languages platform, built with:

- Next.js 15.3.2
- React 19
- Redux Toolkit with RTK Query for API integration
- NextAuth.js for authentication
- Tail<PERSON> CSS for styling
- shadcn/ui for UI components

## Getting Started

### Prerequisites

- Node.js 18+ or Bun 1.0+
- Backend API running (see the `adc-muti-languages-service` directory)

### Installation

1. Install dependencies:

```bash
# Using npm
npm install

# Using Bun (recommended)
bun install
```

2. Create a `.env.local` file in the root directory with the following variables:

```
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-key-change-this-in-production

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:8001
```

3. Start the development server:

```bash
# Using npm
npm run dev

# Using Bun
bun dev
```

4. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Project Structure

- `/src/app`: Next.js app router pages
- `/src/components`: React components
  - `/ui`: UI components from shadcn/ui
  - `/auth`: Authentication-related components
  - `/shared`: Shared components used across features
- `/src/lib`: Utility functions and libraries
  - `/redux`: Redux store and slices
  - `/fetch`: Fetch client for API requests
  - `/utils.ts`: Utility functions
- `/src/types`: TypeScript type definitions

## Authentication

This project uses NextAuth.js for authentication. The authentication flow is as follows:

1. User signs in with email and password
2. Credentials are sent to the backend API
3. If valid, the API returns access and refresh tokens
4. NextAuth stores the tokens in a JWT
5. The JWT is used to authenticate API requests
6. When the access token expires, the refresh token is used to get a new one

Protected routes are handled by the middleware in `src/middleware.ts`.

## API Integration

API requests are handled using Redux Toolkit Query (RTK Query). The API configuration is in `/src/lib/redux/api/apiSlice.ts`, and endpoints are defined in `/src/lib/redux/api/endpoints/`.

Example usage:

```tsx
import { useGetProfileQuery } from '@/lib/redux/api/endpoints/userApi';

function Profile() {
  const { data, isLoading, error } = useGetProfileQuery();
  
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading profile</div>;
  
  return <div>Hello, {data.data.username}</div>;
}
```

## UI Components

This project uses shadcn/ui for UI components. These components are built on top of Radix UI and styled with Tailwind CSS.

Example usage:

```tsx
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

function LoginForm() {
  return (
    <form>
      <Input type="email" placeholder="Email" />
      <Input type="password" placeholder="Password" />
      <Button type="submit">Sign In</Button>
    </form>
  );
}
```

## Adding New Features

When adding new features:

1. Create API endpoints in `/src/lib/redux/api/endpoints/`
2. Create UI components in `/src/components/`
3. Create pages in `/src/app/`
4. Update types as needed in `/src/types/`

## Deployment

To build the application for production:

```bash
# Using npm
npm run build

# Using Bun
bun run build
```

The build output will be in the `.next` directory.
