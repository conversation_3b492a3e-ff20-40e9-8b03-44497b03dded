import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { ApiResponse } from '@/types/api';
import { HYDRATE } from 'next-redux-wrapper';

export interface PermissionGroup {
  id: string;
  name: string;
  description: string;
  permissions: string[];
  created_at: string;
  updated_at: string;
}

export interface CreatePermissionGroupRequest {
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdatePermissionGroupRequest {
  name?: string;
  description?: string;
  permissions?: string[];
}

export const permissionGroupApi = createApi({
  reducerPath: 'permissionGroupApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  extractRehydrationInfo(action, { reducerPath }) {
    if (action.type === HYDRATE) {
      return action.payload[reducerPath];
    }
  },
  tagTypes: ['PermissionGroup'],
  endpoints: (builder) => ({
    // List permission groups
    listPermissionGroups: builder.query<
      ApiResponse<PermissionGroup[]>,
      { organizationId: string }
    >({
      query: ({ organizationId }) => `/organizations/${organizationId}/permission-groups`,
      providesTags: (result, error, { organizationId }) => [
        { type: 'PermissionGroup', id: 'LIST' },
      ],
    }),

    // Get a permission group
    getPermissionGroup: builder.query<
      ApiResponse<PermissionGroup>,
      { organizationId: string; groupId: string }
    >({
      query: ({ organizationId, groupId }) =>
        `/organizations/${organizationId}/permission-groups/${groupId}`,
      providesTags: (result, error, { groupId }) => [
        { type: 'PermissionGroup', id: groupId },
      ],
    }),

    // Create a permission group
    createPermissionGroup: builder.mutation<
      ApiResponse<PermissionGroup>,
      { organizationId: string; data: CreatePermissionGroupRequest }
    >({
      query: ({ organizationId, data }) => ({
        url: `/organizations/${organizationId}/permission-groups`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'PermissionGroup', id: 'LIST' }],
    }),

    // Update a permission group
    updatePermissionGroup: builder.mutation<
      ApiResponse<PermissionGroup>,
      { organizationId: string; groupId: string; data: UpdatePermissionGroupRequest }
    >({
      query: ({ organizationId, groupId, data }) => ({
        url: `/organizations/${organizationId}/permission-groups/${groupId}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { groupId }) => [
        { type: 'PermissionGroup', id: groupId },
        { type: 'PermissionGroup', id: 'LIST' },
      ],
    }),

    // Delete a permission group
    deletePermissionGroup: builder.mutation<
      ApiResponse<{ id: string }>,
      { organizationId: string; groupId: string }
    >({
      query: ({ organizationId, groupId }) => ({
        url: `/organizations/${organizationId}/permission-groups/${groupId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { groupId }) => [
        { type: 'PermissionGroup', id: groupId },
        { type: 'PermissionGroup', id: 'LIST' },
      ],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListPermissionGroupsQuery,
  useGetPermissionGroupQuery,
  useCreatePermissionGroupMutation,
  useUpdatePermissionGroupMutation,
  useDeletePermissionGroupMutation,
} = permissionGroupApi;
