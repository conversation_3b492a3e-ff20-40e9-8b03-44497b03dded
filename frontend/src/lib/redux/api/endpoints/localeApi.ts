import { apiSlice } from '../apiSlice';

// Types for locale requests and responses
export interface Locale {
  id: string;
  code: string;
  name: string;
  native_name: string;
  text_direction?: string;
  is_active?: boolean;
  created_at?: string;
}

export interface CreateLocaleRequest {
  code: string;
  name: string;
  native_name: string;
  text_direction?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Locale API endpoints using RTK Query
 */
export const localeApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // List locales endpoint
    listLocales: builder.query<ApiResponse<Locale[]>, void>({
      query: () => '/locales',
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map((locale) => ({ type: 'Locale' as const, id: locale.id })),
              { type: 'Locale', id: 'LIST' },
            ]
          : [{ type: 'Locale', id: 'LIST' }],
    }),

    // Get locale by code endpoint
    getLocaleByCode: builder.query<ApiResponse<Locale>, string>({
      query: (code) => `/locales/${code}`,
      providesTags: (result, error, code) => [{ type: 'Locale', id: code }],
    }),

    // Create locale endpoint
    createLocale: builder.mutation<ApiResponse<Locale>, CreateLocaleRequest>({
      query: (data) => ({
        url: '/locales',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Locale', id: 'LIST' }],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListLocalesQuery,
  useGetLocaleByCodeQuery,
  useCreateLocaleMutation,
} = localeApi;
