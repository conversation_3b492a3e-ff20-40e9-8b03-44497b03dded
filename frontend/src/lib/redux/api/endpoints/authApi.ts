import { apiSlice } from '../apiSlice';

// Types for auth requests and responses
export interface SignUpRequest {
  email: string;
  username: string;
  password: string;
  full_name?: string;
}

export interface SignInRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  data: {
    access_token: string;
    refresh_token: string;
    user: {
      id: string;
      email: string;
      username: string;
      full_name?: string;
      email_verified: boolean;
      created_at: string;
    };
  };
  message: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface PasswordResetRequest {
  email: string;
}

export interface ConfirmPasswordResetRequest {
  token: string;
  password: string;
}

export interface EmailVerificationRequest {
  token: string;
}

/**
 * Auth API endpoints using RTK Query
 */
export const authApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Sign up endpoint
    signUp: builder.mutation<AuthResponse, SignUpRequest>({
      query: (credentials) => ({
        url: '/auth/signup',
        method: 'POST',
        body: credentials,
      }),
    }),

    // Sign in endpoint
    signIn: builder.mutation<AuthResponse, SignInRequest>({
      query: (credentials) => ({
        url: '/auth/signin',
        method: 'POST',
        body: credentials,
      }),
    }),

    // Refresh token endpoint
    refreshToken: builder.mutation<AuthResponse, RefreshTokenRequest>({
      query: (refreshData) => ({
        url: '/auth/refresh-token',
        method: 'POST',
        body: refreshData,
      }),
    }),

    // Request password reset endpoint
    requestPasswordReset: builder.mutation<{ success: boolean; message: string }, PasswordResetRequest>({
      query: (data) => ({
        url: '/auth/request-password-reset',
        method: 'POST',
        body: data,
      }),
    }),

    // Confirm password reset endpoint
    confirmPasswordReset: builder.mutation<{ success: boolean; message: string }, ConfirmPasswordResetRequest>({
      query: (data) => ({
        url: '/auth/confirm-password-reset',
        method: 'POST',
        body: data,
      }),
    }),

    // Request email verification endpoint
    requestEmailVerification: builder.mutation<{ success: boolean; message: string }, { email: string }>({
      query: (data) => ({
        url: '/auth/request-email-verification',
        method: 'POST',
        body: data,
      }),
    }),

    // Verify email endpoint
    verifyEmail: builder.mutation<{ success: boolean; message: string }, EmailVerificationRequest>({
      query: (data) => ({
        url: '/auth/verify-email',
        method: 'POST',
        body: data,
      }),
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useSignUpMutation,
  useSignInMutation,
  useRefreshTokenMutation,
  useRequestPasswordResetMutation,
  useConfirmPasswordResetMutation,
  useRequestEmailVerificationMutation,
  useVerifyEmailMutation,
} = authApi;
