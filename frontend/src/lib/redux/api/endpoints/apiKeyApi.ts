import { apiSlice } from '../apiSlice';
import { ApiResponse } from './types';

export interface ApiKey {
  id: string;
  name: string;
  prefix: string;
  permissions: string[];
  rate_limit?: number;
  rate_limit_period?: string;
  expires_at?: string;
  last_used_at?: string;
  created_at?: string;
  revoked_at?: string;
  is_active: boolean;
}

export interface ApiKeyCreated extends ApiKey {
  key: string;
}

export interface CreateApiKeyRequest {
  name: string;
  permissions?: string[];
  rate_limit?: number;
  rate_limit_period?: string;
  expires_at?: string;
}

export interface ApiKeyUsage {
  id: string;
  usage_count: number;
  days: number;
}

export interface EndpointUsage {
  endpoint: string;
  count: number;
}

export interface ApiKeyEndpointUsage {
  id: string;
  days: number;
  endpoints: EndpointUsage[];
}

export interface DailyUsage {
  date: string;
  count: number;
}

export interface ApiKeyDailyUsage {
  id: string;
  days: number;
  daily: DailyUsage[];
}

export interface Permission {
  key: string;
  type: 'global' | 'resource';
  action: 'read' | 'write' | 'admin' | 'custom';
  resource?: string;
  description: string;
}

/**
 * API Key API endpoints using RTK Query
 */
export const apiKeyApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // List API keys for an organization
    listApiKeys: builder.query<ApiResponse<ApiKey[]>, string>({
      query: (organizationId) => `/organizations/${organizationId}/api-keys`,
      providesTags: (result, error, organizationId) => [
        { type: 'ApiKey', id: `list-${organizationId}` },
      ],
    }),

    // Create a new API key
    createApiKey: builder.mutation<
      ApiResponse<ApiKeyCreated>,
      { organizationId: string; data: CreateApiKeyRequest }
    >({
      query: ({ organizationId, data }) => ({
        url: `/organizations/${organizationId}/api-keys`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'ApiKey', id: `list-${organizationId}` },
      ],
    }),

    // Revoke an API key
    revokeApiKey: builder.mutation<
      ApiResponse<{ id: string }>,
      { organizationId: string; keyId: string }
    >({
      query: ({ organizationId, keyId }) => ({
        url: `/organizations/${organizationId}/api-keys/${keyId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'ApiKey', id: `list-${organizationId}` },
      ],
    }),

    // Get API key usage statistics
    getApiKeyUsage: builder.query<
      ApiResponse<ApiKeyUsage>,
      { organizationId: string; keyId: string; days?: number }
    >({
      query: ({ organizationId, keyId, days }) =>
        `/organizations/${organizationId}/api-keys/${keyId}/usage${days ? `?days=${days}` : ''}`,
      providesTags: (result, error, { organizationId, keyId }) => [
        { type: 'ApiKey', id: `usage-${keyId}` },
      ],
    }),

    // Get API key usage statistics by endpoint
    getApiKeyUsageByEndpoint: builder.query<
      ApiResponse<ApiKeyEndpointUsage>,
      { organizationId: string; keyId: string; days?: number }
    >({
      query: ({ organizationId, keyId, days }) =>
        `/organizations/${organizationId}/api-keys/${keyId}/usage/endpoints${days ? `?days=${days}` : ''}`,
      providesTags: (result, error, { organizationId, keyId }) => [
        { type: 'ApiKey', id: `usage-endpoints-${keyId}` },
      ],
    }),

    // Get API key usage statistics by day
    getApiKeyUsageByDay: builder.query<
      ApiResponse<ApiKeyDailyUsage>,
      { organizationId: string; keyId: string; days?: number }
    >({
      query: ({ organizationId, keyId, days }) =>
        `/organizations/${organizationId}/api-keys/${keyId}/usage/daily${days ? `?days=${days}` : ''}`,
      providesTags: (result, error, { organizationId, keyId }) => [
        { type: 'ApiKey', id: `usage-daily-${keyId}` },
      ],
    }),

    // Get available permissions
    getPermissions: builder.query<
      ApiResponse<{ permissions: Permission[] }>,
      void
    >({
      query: () => `/permissions`,
      providesTags: ['ApiKey'],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListApiKeysQuery,
  useCreateApiKeyMutation,
  useRevokeApiKeyMutation,
  useGetApiKeyUsageQuery,
  useGetApiKeyUsageByEndpointQuery,
  useGetApiKeyUsageByDayQuery,
  useGetPermissionsQuery,
} = apiKeyApi;
