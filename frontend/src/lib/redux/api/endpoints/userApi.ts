import { apiSlice } from '../apiSlice';

// Types for user requests and responses
export interface UserProfile {
  id: string;
  email: string;
  username: string;
  full_name?: string;
  email_verified: boolean;
  created_at: string;
  updated_at: string;
  avatar_url?: string;
  bio?: string;
  preferences?: Record<string, any>;
}

export interface UpdateProfileRequest {
  username?: string;
  full_name?: string;
  bio?: string;
  preferences?: Record<string, any>;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * User API endpoints using RTK Query
 */
export const userApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // Get user profile endpoint
    getProfile: builder.query<ApiResponse<UserProfile>, void>({
      query: () => '/users/profile',
      providesTags: ['User'],
    }),

    // Update user profile endpoint
    updateProfile: builder.mutation<ApiResponse<UserProfile>, UpdateProfileRequest>({
      query: (data) => ({
        url: '/users/profile',
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['User'],
    }),

    // Get user by ID endpoint
    getUser: builder.query<ApiResponse<UserProfile>, string>({
      query: (userId) => `/users/${userId}`,
      providesTags: (result, error, userId) => [{ type: 'User', id: userId }],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useGetProfileQuery,
  useUpdateProfileMutation,
  useGetUserQuery,
} = userApi;
