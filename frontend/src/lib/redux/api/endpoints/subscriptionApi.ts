import { apiSlice } from '../apiSlice';

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price_monthly: number;
  price_yearly: number;
  features: string[];
  is_popular: boolean;
  stripe_price_monthly?: string;
  stripe_price_yearly?: string;
  created_at: string;
  updated_at: string;
}

export interface Subscription {
  id: string;
  organization_id: string;
  plan_id: string;
  status: string;
  current_period_start: string;
  current_period_end: string;
  cancel_at_period_end: boolean;
  created_at: string;
  updated_at?: string;
  stripe_subscription_id?: string;
  trial_end?: string;
  plan?: {
    id: string;
    name: string;
    description: string;
    price: number;
    interval: string;
    features: string[];
  };
}

export interface SubscriptionEvent {
  id: string;
  organization_id: string;
  event_type: string;
  details: {
    plan_name?: string;
    amount?: number;
    currency?: string;
    status?: string;
    subscription_id?: string;
    error?: string;
  };
  created_at: string;
}

export interface SubscriptionHistory {
  events: SubscriptionEvent[];
}

export interface AICreditUsage {
  total: number;
  used: number;
  remaining: number;
  reset_date: string;
}

export interface CreateSubscriptionRequest {
  organization_id: string;
  tier_name: string;
  is_yearly: boolean;
  payment_method_id: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Subscription API endpoints
 */
export const subscriptionApi = apiSlice.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    // List subscription plans
    listSubscriptionPlans: builder.query<ApiResponse<SubscriptionPlan[]>, void>({
      query: () => '/subscription-plans',
    }),

    // Get organization subscription
    getOrganizationSubscription: builder.query<ApiResponse<Subscription>, string>({
      query: (organizationId) => `/organizations/${organizationId}/subscription`,
      providesTags: (result, error, organizationId) => [
        { type: 'Subscription', id: organizationId },
      ],
    }),

    // Create organization subscription
    createOrganizationSubscription: builder.mutation<
      ApiResponse<Subscription>,
      CreateSubscriptionRequest
    >({
      query: (request) => ({
        url: '/payments/subscriptions',
        method: 'POST',
        body: request,
      }),
      invalidatesTags: (result, error, { organization_id }) => [
        { type: 'Subscription', id: organization_id },
        { type: 'Organization', id: organization_id },
      ],
    }),

    // Cancel organization subscription
    cancelOrganizationSubscription: builder.mutation<
      ApiResponse<void>,
      { organizationId: string; atPeriodEnd?: boolean }
    >({
      query: ({ organizationId, atPeriodEnd = true }) => ({
        url: `/organizations/${organizationId}/subscription`,
        method: 'DELETE',
        body: { cancel_at_period_end: atPeriodEnd },
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'Subscription', id: organizationId },
        { type: 'Organization', id: organizationId },
      ],
    }),

    // Get subscription history
    getSubscriptionHistory: builder.query<
      ApiResponse<SubscriptionHistory>,
      { organizationId: string; limit?: number }
    >({
      query: ({ organizationId, limit = 10 }) =>
        `/organizations/${organizationId}/subscription/history?limit=${limit}`,
      providesTags: (result, error, { organizationId }) => [
        { type: 'SubscriptionHistory', id: organizationId },
      ],
    }),

    // Get AI credit usage
    getAICreditUsage: builder.query<
      ApiResponse<AICreditUsage>,
      string
    >({
      query: (organizationId) => `/organizations/${organizationId}/ai-credits`,
      providesTags: (result, error, organizationId) => [
        { type: 'AICreditUsage', id: organizationId },
      ],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListSubscriptionPlansQuery,
  useGetOrganizationSubscriptionQuery,
  useCreateOrganizationSubscriptionMutation,
  useCancelOrganizationSubscriptionMutation,
  useGetSubscriptionHistoryQuery,
  useGetAICreditUsageQuery,
} = subscriptionApi;
