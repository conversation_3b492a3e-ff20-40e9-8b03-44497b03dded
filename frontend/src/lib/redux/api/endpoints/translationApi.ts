import { apiSlice } from '../apiSlice';
import { ApiResponse } from '../types';

// Types for translation requests and responses
export interface TranslationKey {
  id: string;
  resource_id?: string;
  resource_name?: string;
  key_name: string;
  description?: string;
  context?: string;
  is_plural?: boolean;
  max_length?: number;
  image_url?: string;
  screenshot_url?: string;
  created_at?: string;
  updated_at?: string;
  project_id?: string;
}

export interface Translation {
  id: string;
  key_id: string;
  locale_id: string;
  content: string;
  is_reviewed?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface TranslationHistory {
  id: string;
  translation_id: string;
  content: string;
  created_at: string;
  created_by?: string;
}

export interface CreateTranslationKeyRequest {
  resource_id?: string;
  resource_name?: string;
  project_id?: string;
  key_name: string;
  description?: string;
  context?: string;
  is_plural?: boolean;
  max_length?: number;
  image_url?: string;
  screenshot_url?: string;
}

export interface UpdateTranslationKeyRequest {
  key_name?: string;
  description?: string;
  context?: string;
  is_plural?: boolean;
  max_length?: number;
  image_url?: string;
  screenshot_url?: string;
}

export interface CreateTranslationKeyWithProjectRequest {
  projectSlug: string;
  key: CreateTranslationKeyRequest;
}

export interface CreateTranslationRequest {
  key_id: string;
  locale_id: string;
  content: string;
}

export interface UpdateTranslationRequest {
  content: string;
  is_reviewed?: boolean;
}

export interface AITranslateRequest {
  text: string;
  source_locale: string;
  target_locale: string;
  context?: string;
  provider?: string;
  model?: string;
}

export interface AITranslateResponse {
  translated_text: string;
  model_used: string;
  credits_used: number;
  credits_remaining: number;
}



/**
 * Translation API endpoints using RTK Query
 */
export const translationApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // List all translation keys for a project by slug
    listProjectTranslationKeys: builder.query<ApiResponse<TranslationKey[]>, { projectSlug: string; resourceId?: string }>({
      // This is a two-step process:
      // 1. First, we need to get the project ID from the slug
      // 2. Then, we can get the translation keys for that project ID
      async queryFn(arg, _queryApi, _extraOptions, fetchWithBQ) {
        const { projectSlug, resourceId } = arg;

        try {
          // Step 1: Get the organizations to find the organization ID
          const orgsResult = await fetchWithBQ('/organizations');
          if (orgsResult.error) return { error: orgsResult.error };

          const orgsData = orgsResult.data as ApiResponse<any[]>;
          if (!orgsData.success || !orgsData.data || orgsData.data.length === 0) {
            return { error: { status: 404, data: { message: 'No organizations found' } } };
          }

          const organizationId = orgsData.data[0].id;

          // Step 2: Get the project by slug
          const projectResult = await fetchWithBQ(`/projects/slug/${projectSlug}?organization_id=${organizationId}`);
          if (projectResult.error) return { error: projectResult.error };

          const projectData = projectResult.data as ApiResponse<any>;
          if (!projectData.success || !projectData.data) {
            return { error: { status: 404, data: { message: 'Project not found' } } };
          }

          const projectId = projectData.data.id;

          // Step 3: Get the translation keys for the project
          let url = `/translations/keys?project_id=${projectId}`;
          if (resourceId) url += `&resource_id=${resourceId}`;

          const keysResult = await fetchWithBQ(url);
          if (keysResult.error) return { error: keysResult.error };

          return { data: keysResult.data as ApiResponse<TranslationKey[]> };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Failed to fetch translation keys' }
            }
          };
        }
      },
      providesTags: (result, error, { projectSlug, resourceId }) => [
        { type: 'Translation', id: `project-${projectSlug}-keys` },
        ...(resourceId ? [{ type: 'Translation', id: `${resourceId}-keys` }] : [])
      ],
    }),

    // List translation keys by resource
    listTranslationKeys: builder.query<ApiResponse<TranslationKey[]>, string>({
      query: (resourceId) => `/translations/keys?resource_id=${resourceId}`,
      providesTags: (result, error, resourceId) => [{ type: 'Translation', id: `${resourceId}-keys` }],
    }),

    // Create translation key
    createTranslationKey: builder.mutation<ApiResponse<TranslationKey>, CreateTranslationKeyWithProjectRequest>({
      // This is a multi-step process:
      // 1. Get the project ID from the slug
      // 2. Create the translation key with either resource_id or project_id
      async queryFn(arg, _queryApi, _extraOptions, fetchWithBQ) {
        const { projectSlug, key } = arg;

        try {
          // Step 1: Get the organizations to find the organization ID
          const orgsResult = await fetchWithBQ('/organizations');
          if (orgsResult.error) return { error: orgsResult.error };

          const orgsData = orgsResult.data as ApiResponse<any[]>;
          if (!orgsData.success || !orgsData.data || orgsData.data.length === 0) {
            return { error: { status: 404, data: { message: 'No organizations found' } } };
          }

          const organizationId = orgsData.data[0].id;

          // Step 2: Get the project by slug
          const projectResult = await fetchWithBQ(`/projects/slug/${projectSlug}?organization_id=${organizationId}`);
          if (projectResult.error) return { error: projectResult.error };

          const projectData = projectResult.data as ApiResponse<any>;
          if (!projectData.success || !projectData.data) {
            return { error: { status: 404, data: { message: 'Project not found' } } };
          }

          const projectId = projectData.data.id;

          // Step 3: Create the translation key directly with the project ID
          const createKeyResult = await fetchWithBQ({
            url: `/translations/keys`,
            method: 'POST',
            body: {
              ...key,
              project_id: projectId,
              // Keep resource_id if it was provided, otherwise it will be null
            }
          });

          if (createKeyResult.error) return { error: createKeyResult.error };

          return { data: createKeyResult.data as ApiResponse<TranslationKey> };
        } catch (error) {
          return {
            error: {
              status: 500,
              data: { message: 'Failed to create translation key' }
            }
          };
        }
      },
      invalidatesTags: (result, error, { projectSlug, key }) => [
        { type: 'Translation', id: `project-${projectSlug}-keys` },
        ...(key.resource_id ? [{ type: 'Translation', id: `${key.resource_id}-keys` }] : []),
      ],
    }),

    // Get translation key by ID
    getTranslationKey: builder.query<ApiResponse<TranslationKey>, string>({
      query: (keyId) => `/translations/keys/${keyId}`,
      providesTags: (result, error, keyId) => [{ type: 'Translation', id: `key-${keyId}` }],
    }),

    // List translations by key
    listTranslationsByKey: builder.query<ApiResponse<Translation[]>, string>({
      query: (keyId) => `/translations?key_id=${keyId}`,
      providesTags: (result, error, keyId) => [{ type: 'Translation', id: `${keyId}-translations` }],
    }),

    // Create translation
    createTranslation: builder.mutation<ApiResponse<Translation>, CreateTranslationRequest>({
      query: (data) => ({
        url: '/translations',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { key_id }) => [{ type: 'Translation', id: `${key_id}-translations` }],
    }),

    // Update translation
    updateTranslation: builder.mutation<ApiResponse<Translation>, { id: string; data: UpdateTranslationRequest }>({
      query: ({ id, data }) => ({
        url: `/translations/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result) => [{ type: 'Translation', id: `${result?.data.key_id}-translations` }],
    }),

    // Get translation history
    getTranslationHistory: builder.query<ApiResponse<TranslationHistory[]>, string>({
      query: (translationId) => `/translations/${translationId}/history`,
      providesTags: (result, error, translationId) => [{ type: 'Translation', id: `${translationId}-history` }],
    }),

    // AI translate
    aiTranslate: builder.mutation<ApiResponse<AITranslateResponse>, AITranslateRequest>({
      query: (data) => ({
        url: '/translations/ai-translate',
        method: 'POST',
        body: data,
      }),
    }),

    // Update translation key
    updateTranslationKey: builder.mutation<ApiResponse<TranslationKey>, { id: string; data: UpdateTranslationKeyRequest }>({
      query: ({ id, data }) => ({
        url: `/translations/keys/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [
        { type: 'Translation', id: `key-${id}` },
        { type: 'Translation', id: `${id}-translations` },
      ],
    }),

    // Delete translation key
    deleteTranslationKey: builder.mutation<ApiResponse<void>, string>({
      query: (id) => ({
        url: `/translations/keys/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, id) => [
        { type: 'Translation', id: `key-${id}` },
        { type: 'Translation', id: `${id}-translations` },
      ],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListProjectTranslationKeysQuery,
  useListTranslationKeysQuery,
  useCreateTranslationKeyMutation,
  useGetTranslationKeyQuery,
  useListTranslationsByKeyQuery,
  useCreateTranslationMutation,
  useUpdateTranslationMutation,
  useGetTranslationHistoryQuery,
  useAiTranslateMutation,
  useUpdateTranslationKeyMutation,
  useDeleteTranslationKeyMutation,
} = translationApi;
