import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { ApiResponse } from '@/types/api';
import { HYDRATE } from 'next-redux-wrapper';

export interface AuditLog {
  id: string;
  api_key_id: string;
  endpoint: string;
  method: string;
  permission_key: string;
  granted: boolean;
  created_at: string;
}

export interface AuditLogListResponse {
  logs: AuditLog[];
  total: number;
  limit: number;
  offset: number;
}

export interface AuditLogQueryParams {
  limit?: number;
  offset?: number;
  start_date?: string;
  end_date?: string;
  permission_key?: string;
  granted?: boolean;
}

export const auditLogApi = createApi({
  reducerPath: 'auditLogApi',
  baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
  extractRehydrationInfo(action, { reducerPath }) {
    if (action.type === HYDRATE) {
      return action.payload[reducerPath];
    }
  },
  tagTypes: ['AuditLog'],
  endpoints: (builder) => ({
    // List audit logs for an organization
    listAuditLogs: builder.query<
      ApiResponse<AuditLogListResponse>,
      { organizationId: string; params?: AuditLogQueryParams }
    >({
      query: ({ organizationId, params }) => {
        // Build query string
        let queryString = '';
        if (params) {
          const queryParams = new URLSearchParams();
          if (params.limit) queryParams.append('limit', params.limit.toString());
          if (params.offset) queryParams.append('offset', params.offset.toString());
          if (params.start_date) queryParams.append('start_date', params.start_date);
          if (params.end_date) queryParams.append('end_date', params.end_date);
          if (params.permission_key) queryParams.append('permission_key', params.permission_key);
          if (params.granted !== undefined) queryParams.append('granted', params.granted.toString());
          queryString = `?${queryParams.toString()}`;
        }
        return `/organizations/${organizationId}/audit-logs${queryString}`;
      },
      providesTags: (result, error, { organizationId }) => [
        { type: 'AuditLog', id: `org-${organizationId}` },
      ],
    }),

    // List audit logs for an API key
    listApiKeyAuditLogs: builder.query<
      ApiResponse<AuditLog[]>,
      { organizationId: string; keyId: string; limit?: number; offset?: number }
    >({
      query: ({ organizationId, keyId, limit, offset }) => {
        // Build query string
        let queryString = '';
        if (limit || offset) {
          const queryParams = new URLSearchParams();
          if (limit) queryParams.append('limit', limit.toString());
          if (offset) queryParams.append('offset', offset.toString());
          queryString = `?${queryParams.toString()}`;
        }
        return `/organizations/${organizationId}/api-keys/${keyId}/audit-logs${queryString}`;
      },
      providesTags: (result, error, { keyId }) => [
        { type: 'AuditLog', id: `key-${keyId}` },
      ],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListAuditLogsQuery,
  useListApiKeyAuditLogsQuery,
} = auditLogApi;
