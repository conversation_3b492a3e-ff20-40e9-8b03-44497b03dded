import { apiSlice } from '../apiSlice';

export interface PaymentMethod {
  id: string;
  organization_id: string;
  stripe_payment_method_id: string;
  payment_type: string;
  last_four?: string;
  card_brand?: string;
  expiry_month?: number;
  expiry_year?: number;
  is_default: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface PaymentIntent {
  id: string;
  client_secret: string;
  amount: number;
  currency: string;
  status: string;
}

export interface CreatePaymentIntentRequest {
  amount: number;
  currency: string;
  description?: string;
  metadata?: Record<string, string>;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Payment API endpoints
 */
export const paymentApi = apiSlice.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    // List organization payment methods
    listOrganizationPaymentMethods: builder.query<ApiResponse<PaymentMethod[]>, string>({
      query: (organizationId) => `/organizations/${organizationId}/payment-methods`,
      providesTags: (result, error, organizationId) =>
        result
          ? [
              ...result.data.map(({ id }) => ({ type: 'PaymentMethod' as const, id })),
              { type: 'PaymentMethod', id: `${organizationId}-LIST` },
            ]
          : [{ type: 'PaymentMethod', id: `${organizationId}-LIST` }],
    }),

    // Add organization payment method
    addOrganizationPaymentMethod: builder.mutation<
      ApiResponse<PaymentMethod>,
      { organizationId: string; paymentMethodId: string; setAsDefault?: boolean }
    >({
      query: ({ organizationId, paymentMethodId, setAsDefault }) => ({
        url: `/payments/payment-methods`,
        method: 'POST',
        body: {
          organization_id: organizationId,
          stripe_payment_method_id: paymentMethodId,
          set_as_default: setAsDefault
        },
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'PaymentMethod', id: `${organizationId}-LIST` },
      ],
    }),

    // Remove organization payment method
    removeOrganizationPaymentMethod: builder.mutation<
      ApiResponse<void>,
      { organizationId: string; paymentMethodId: string }
    >({
      query: ({ organizationId, paymentMethodId }) => ({
        url: `/organizations/${organizationId}/payment-methods/${paymentMethodId}`,
        method: 'DELETE',
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'PaymentMethod', id: `${organizationId}-LIST` },
      ],
    }),

    // Set default organization payment method
    setDefaultOrganizationPaymentMethod: builder.mutation<
      ApiResponse<PaymentMethod>,
      { organizationId: string; paymentMethodId: string }
    >({
      query: ({ organizationId, paymentMethodId }) => ({
        url: `/organizations/${organizationId}/payment-methods/${paymentMethodId}/default`,
        method: 'PUT',
        body: { is_default: true },
      }),
      invalidatesTags: (result, error, { organizationId }) => [
        { type: 'PaymentMethod', id: `${organizationId}-LIST` },
      ],
    }),

    // Create payment intent
    createPaymentIntent: builder.mutation<
      ApiResponse<PaymentIntent>,
      { organizationId: string; request: CreatePaymentIntentRequest }
    >({
      query: ({ organizationId, request }) => ({
        url: `/organizations/${organizationId}/payment-intents`,
        method: 'POST',
        body: request,
      }),
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListOrganizationPaymentMethodsQuery,
  useAddOrganizationPaymentMethodMutation,
  useRemoveOrganizationPaymentMethodMutation,
  useSetDefaultOrganizationPaymentMethodMutation,
  useCreatePaymentIntentMutation,
} = paymentApi;
