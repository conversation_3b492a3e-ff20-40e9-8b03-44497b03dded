import { apiSlice } from '../apiSlice';

// Types for project requests and responses
export interface Project {
  id: string;
  organization_id: string;
  name: string;
  slug: string;
  description?: string;
  default_locale?: string;
  is_public?: boolean;
  created_at?: string;
  updated_at?: string;
}

export interface CreateProjectRequest {
  organization_id: string;
  name: string;
  slug: string;
  description?: string;
  default_locale?: string;
  is_public?: boolean;
}

export interface UpdateProjectRequest {
  name?: string;
  description?: string;
  default_locale?: string;
  is_public?: boolean;
}

export interface ProjectLocale {
  id: string;
  project_id: string;
  locale_id: string;
  is_source?: boolean;
  locale_code?: string;
  locale_name?: string;
  locale_native_name?: string;
  text_direction?: string;
}

export interface AddProjectLocaleRequest {
  locale_id: string;
  is_source?: boolean;
}

export interface Resource {
  id: string;
  project_id: string;
  name: string;
  type_: string;
  path?: string;
  description?: string;
}

export interface CreateResourceRequest {
  name: string;
  type_: string;
  path?: string;
  description?: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

/**
 * Project API endpoints using RTK Query
 */
export const projectApi = apiSlice.injectEndpoints({
  endpoints: (builder) => ({
    // List projects endpoint
    listProjects: builder.query<ApiResponse<Project[]>, { organization_id?: string }>({
      query: (params) => ({
        url: '/projects',
        params: params.organization_id ? { organization_id: params.organization_id } : undefined,
      }),
      providesTags: (result) =>
        result?.data
          ? [
              ...result.data.map((project) => ({ type: 'Project' as const, id: project.id })),
              { type: 'Project', id: 'LIST' },
            ]
          : [{ type: 'Project', id: 'LIST' }],
    }),

    // Get project by ID endpoint
    getProject: builder.query<ApiResponse<Project>, string>({
      query: (id) => `/projects/${id}`,
      providesTags: (result, error, id) => [{ type: 'Project', id }],
    }),

    // Get project by slug endpoint
    getProjectBySlug: builder.query<
      ApiResponse<Project>,
      { slug: string; organization_id: string }
    >({
      query: ({ slug, organization_id }) => ({
        url: `/projects/slug/${slug}`,
        params: { organization_id },
      }),
      providesTags: (result, error, { slug }) =>
        result ? [{ type: 'Project', id: result.data.id }] : [],
    }),

    // Create project endpoint
    createProject: builder.mutation<ApiResponse<Project>, CreateProjectRequest>({
      query: (data) => ({
        url: '/projects',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Project', id: 'LIST' }],
    }),

    // Update project endpoint
    updateProject: builder.mutation<ApiResponse<Project>, { id: string; data: UpdateProjectRequest }>({
      query: ({ id, data }) => ({
        url: `/projects/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Project', id }],
    }),

    // Delete project endpoint
    deleteProject: builder.mutation<ApiResponse<Project>, string>({
      query: (id) => ({
        url: `/projects/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: [{ type: 'Project', id: 'LIST' }],
    }),

    // List project locales endpoint
    listProjectLocales: builder.query<ApiResponse<ProjectLocale[]>, string>({
      query: (projectId) => `/projects/${projectId}/locales`,
      providesTags: (result, error, projectId) => [{ type: 'Project', id: `${projectId}-locales` }],
    }),

    // Add locale to project endpoint
    addProjectLocale: builder.mutation<
      ApiResponse<ProjectLocale>,
      { projectId: string; data: AddProjectLocaleRequest }
    >({
      query: ({ projectId, data }) => ({
        url: `/projects/${projectId}/locales`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: `${projectId}-locales` }],
    }),

    // Create resource endpoint
    createResource: builder.mutation<
      Resource,
      { projectId: string; data: CreateResourceRequest }
    >({
      query: ({ projectId, data }) => ({
        url: `/projects/${projectId}/resources`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { projectId }) => [{ type: 'Project', id: `${projectId}-resources` }],
    }),

    // List resources endpoint
    listResources: builder.query<ApiResponse<Resource[]>, string>({
      query: (projectId) => `/projects/${projectId}/resources`,
      providesTags: (result, error, projectId) => [{ type: 'Project', id: `${projectId}-resources` }],
    }),
  }),
});

// Export hooks for usage in functional components
export const {
  useListProjectsQuery,
  useGetProjectQuery,
  useGetProjectBySlugQuery,
  useCreateProjectMutation,
  useUpdateProjectMutation,
  useDeleteProjectMutation,
  useListProjectLocalesQuery,
  useAddProjectLocaleMutation,
  useCreateResourceMutation,
  useListResourcesQuery,
} = projectApi;
