import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import { getSession } from 'next-auth/react';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * Base API configuration for RTK Query
 * This sets up the base URL and authentication headers
 */
export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl: `${API_BASE_URL}/api`,
    prepareHeaders: async (headers) => {
      // Get token from NextAuth session
      if (typeof window !== 'undefined') {
        const session = await getSession();

        // If we have a session with an access token, add it to the headers
        console.log('Session in apiSlice:', session);
        if (session?.accessToken) {
          headers.set('Authorization', `Bearer ${session.accessToken}`);
          console.log('Added Authorization header in apiSlice:', `Bearer ${session.accessToken}`);
        } else {
          console.log('No accessToken found in session in apiSlice');
        }
      }

      return headers;
    },
  }),
  // Global configuration for all endpoints
  tagTypes: [
    'User',
    'Organization',
    'Project',
    'Translation',
    'Locale',
    'OrganizationMember',
    'PaymentMethod',
    'Subscription',
    'ApiKey',
    'PermissionGroup',
    'AuditLog'
  ],
  endpoints: () => ({}),
});

// Export hooks for usage in functional components
export const {
  util: { getRunningQueriesThunk },
} = apiSlice;
