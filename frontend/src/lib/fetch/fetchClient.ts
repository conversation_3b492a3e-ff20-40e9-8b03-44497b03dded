/**
 * Fetch client for making HTTP requests
 * Handles authentication, error handling, and request/response formatting
 */
import { getSession } from 'next-auth/react';

type FetchOptions = RequestInit & {
  baseUrl?: string;
  params?: Record<string, string>;
  withAuth?: boolean;
};

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

/**
 * Custom fetch client with error handling and authentication
 */
export async function fetchClient<T>(
  url: string,
  options: FetchOptions = {}
): Promise<T> {
  const {
    baseUrl = API_BASE_URL,
    params,
    withAuth = true,
    headers = {},
    ...restOptions
  } = options;

  // Build URL with query parameters
  const queryParams = params
    ? `?${new URLSearchParams(params).toString()}`
    : '';

  const fullUrl = `${baseUrl}${url}${queryParams}`;

  // Add authentication header if required
  const requestHeaders = new Headers(headers);

  if (withAuth && typeof window !== 'undefined') {
    // Get token from NextAuth session
    const session = await getSession();
    console.log('Session in fetchClient:', session);
    if (session?.accessToken) {
      requestHeaders.set('Authorization', `Bearer ${session.accessToken}`);
      console.log('Added Authorization header:', `Bearer ${session.accessToken}`);
    } else {
      console.log('No accessToken found in session');
    }
  }

  // Add content type header if not present
  if (!requestHeaders.has('Content-Type') && options.method !== 'GET') {
    requestHeaders.set('Content-Type', 'application/json');
  }

  // Make the request
  const response = await fetch(fullUrl, {
    ...restOptions,
    headers: requestHeaders,
  });

  // Handle HTTP errors
  if (!response.ok) {
    let errorData;
    try {
      errorData = await response.json();
    } catch (e) {
      errorData = { message: 'An unknown error occurred' };
    }

    const error = new Error(
      errorData.message || `HTTP error ${response.status}`
    ) as Error & { status?: number; data?: any };

    error.status = response.status;
    error.data = errorData;

    throw error;
  }

  // Parse response
  if (response.status === 204) {
    return {} as T; // No content
  }

  // Check if response is JSON
  const contentType = response.headers.get('content-type');
  if (contentType && contentType.includes('application/json')) {
    return response.json();
  }

  return response.text() as unknown as T;
}

// Export convenience methods
export const get = <T>(url: string, options?: FetchOptions) =>
  fetchClient<T>(url, { ...options, method: 'GET' });

export const post = <T>(url: string, data?: any, options?: FetchOptions) =>
  fetchClient<T>(url, {
    ...options,
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  });

export const put = <T>(url: string, data?: any, options?: FetchOptions) =>
  fetchClient<T>(url, {
    ...options,
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  });

export const del = <T>(url: string, options?: FetchOptions) =>
  fetchClient<T>(url, { ...options, method: 'DELETE' });
