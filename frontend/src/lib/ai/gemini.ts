import { GoogleGenAI } from '@google/genai';

export interface GeminiTranslateOptions {
  text: string;
  sourceLocale: string;
  targetLocale: string;
  context?: string;
  model?: string;
}

export interface GeminiTranslateResult {
  translatedText: string;
  modelUsed: string;
  creditsUsed: number;
}

export class GeminiTranslationService {
  private ai: GoogleGenAI;
  private defaultModel: string;

  constructor(apiKey: string, defaultModel?: string) {
    this.ai = new GoogleGenAI({ apiKey });
    this.defaultModel = defaultModel || 'gemini-2.5-flash-preview-04-17';
  }

  async translate(options: GeminiTranslateOptions): Promise<GeminiTranslateResult> {
    const model = this.ai.getGenerativeModel({
      model: options.model || this.defaultModel,
    });

    // Create a prompt for translation
    let prompt = `You are a professional translator. Translate the following text from ${options.sourceLocale} to ${options.targetLocale}. 
Maintain the original meaning, tone, and formatting. 
Only return the translated text without any additional comments or explanations.`;

    // Add context if provided
    if (options.context) {
      prompt += `\n\nContext: ${options.context}`;
    }

    prompt += `\n\nText to translate: ${options.text}`;

    // Generate the translation
    const result = await model.generateContent(prompt);
    const response = await result.response;
    const translatedText = response.text().trim();

    // Estimate credits used based on text length (since token count isn't directly available)
    const textLength = options.text.length;
    const creditsUsed = Math.max(1, Math.ceil(textLength / 100));

    return {
      translatedText,
      modelUsed: options.model || this.defaultModel,
      creditsUsed,
    };
  }
}

// Create a singleton instance
let geminiService: GeminiTranslationService | null = null;

export function getGeminiService(): GeminiTranslationService | null {
  if (!geminiService) {
    const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY || '';
    if (apiKey) {
      geminiService = new GeminiTranslationService(apiKey);
    }
  }
  return geminiService;
}
