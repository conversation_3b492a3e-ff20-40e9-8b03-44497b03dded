import NextAuth, { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";

declare module "next-auth" {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    user: {
      /** The user's id */
      id: string;
    } & DefaultSession["user"];
    /** The user's access token */
    accessToken?: string;
    /** The user's refresh token */
    refreshToken?: string;
  }

  interface User {
    /** The user's access token */
    accessToken?: string;
    /** The user's refresh token */
    refreshToken?: string;
  }
}

declare module "next-auth/jwt" {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    /** The user's id */
    id?: string;
    /** The user's access token */
    accessToken?: string;
    /** The user's refresh token */
    refreshToken?: string;
  }
}
