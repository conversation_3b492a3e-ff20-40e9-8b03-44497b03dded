'use client';

import { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';

export interface AIProvider {
  id: string;
  name: string;
  description: string;
  models: AIModel[];
}

export interface AIModel {
  id: string;
  name: string;
  description: string;
}

interface AIProviderSelectorProps {
  onProviderChange: (provider: string) => void;
  onModelChange: (model: string) => void;
  defaultProvider?: string;
  defaultModel?: string;
}

export const AIProviderSelector: React.FC<AIProviderSelectorProps> = ({
  onProviderChange,
  onModelChange,
  defaultProvider = 'openai',
  defaultModel,
}) => {
  const [selectedProvider, setSelectedProvider] = useState<string>(defaultProvider);
  const [selectedModel, setSelectedModel] = useState<string | undefined>(defaultModel);

  // Define available providers and models
  const providers: <PERSON>Provider[] = [
    {
      id: 'openai',
      name: 'OpenAI',
      description: 'OpenAI GPT models',
      models: [
        { id: 'gpt-4', name: 'GPT-4', description: 'Most capable OpenAI model' },
        { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo', description: 'Fast and efficient model' },
      ],
    },
    {
      id: 'gemini',
      name: 'Google Gemini',
      description: 'Google Gemini AI models',
      models: [
        { id: 'gemini-2.5-flash-preview-04-17', name: 'Gemini 2.5 Flash', description: 'Fast Gemini model' },
        { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro', description: 'Powerful Gemini model' },
      ],
    },
  ];

  // Get the current provider's models
  const currentProvider = providers.find(p => p.id === selectedProvider);
  const availableModels = currentProvider?.models || [];

  // Set default model if not provided
  useEffect(() => {
    if (!selectedModel && availableModels.length > 0) {
      const defaultModel = availableModels[0].id;
      setSelectedModel(defaultModel);
      onModelChange(defaultModel);
    }
  }, [selectedProvider, availableModels, selectedModel, onModelChange]);

  // Handle provider change
  const handleProviderChange = (value: string) => {
    setSelectedProvider(value);
    onProviderChange(value);
    
    // Reset model when provider changes
    const newProvider = providers.find(p => p.id === value);
    if (newProvider && newProvider.models.length > 0) {
      const newModel = newProvider.models[0].id;
      setSelectedModel(newModel);
      onModelChange(newModel);
    }
  };

  // Handle model change
  const handleModelChange = (value: string) => {
    setSelectedModel(value);
    onModelChange(value);
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="ai-provider">AI Provider</Label>
        <Select value={selectedProvider} onValueChange={handleProviderChange}>
          <SelectTrigger id="ai-provider">
            <SelectValue placeholder="Select AI provider" />
          </SelectTrigger>
          <SelectContent>
            {providers.map((provider) => (
              <SelectItem key={provider.id} value={provider.id}>
                {provider.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-2">
        <Label htmlFor="ai-model">AI Model</Label>
        <Select value={selectedModel} onValueChange={handleModelChange}>
          <SelectTrigger id="ai-model">
            <SelectValue placeholder="Select AI model" />
          </SelectTrigger>
          <SelectContent>
            {availableModels.map((model) => (
              <SelectItem key={model.id} value={model.id}>
                {model.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};
