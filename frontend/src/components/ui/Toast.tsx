'use client';

import { createContext, useContext, useState, useEffect } from 'react';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider as ShadcnToastProvider,
  ToastTitle,
  ToastViewport
} from '@/components/ui/shadcn-toast';
import { useToast as useShadcnToast } from '@/components/ui/use-toast';
import {
  CheckCircle,
  AlertCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

// Toast types
export type ToastType = 'success' | 'error' | 'info' | 'warning';

// Toast interface
export interface ToastItem {
  id: string;
  message: string;
  type: ToastType;
  duration?: number;
}

// Toast context interface
interface ToastContextType {
  toasts: ToastItem[];
  addToast: (message: string, type: ToastType, duration?: number) => void;
  removeToast: (id: string) => void;
}

// Create context
const ToastContext = createContext<ToastContextType | undefined>(undefined);

/**
 * Toast provider component
 */
export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toasts, setToasts] = useState<ToastItem[]>([]);
  const { toast } = useShadcnToast();

  // Add a new toast
  const addToast = (message: string, type: ToastType, duration = 5000) => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast: ToastItem = {
      id,
      message,
      type,
      duration,
    };

    setToasts(prevToasts => [newToast, ...prevToasts]);

    // Show the toast using shadcn toast
    toast({
      variant: type === 'error' ? 'destructive' : type,
      title: getToastTitle(type),
      description: message,
      duration: duration,
    });
  };

  // Get toast title based on type
  const getToastTitle = (type: ToastType): string => {
    switch (type) {
      case 'success':
        return 'Success';
      case 'error':
        return 'Error';
      case 'warning':
        return 'Warning';
      case 'info':
        return 'Information';
      default:
        return '';
    }
  };

  // Remove a toast by ID
  const removeToast = (id: string) => {
    setToasts(prevToasts => prevToasts.filter(toast => toast.id !== id));
  };

  return (
    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>
      <ShadcnToastProvider>
        {children}
        <ToastViewport />
      </ShadcnToastProvider>
    </ToastContext.Provider>
  );
}

/**
 * Hook to use the toast context
 */
export function useToast() {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
}
