'use client';

import { useContext } from 'react';
import { useTheme } from './ThemeProvider';
import { Button } from '@/components/ui/button';
import { Sun, Moon, Laptop } from 'lucide-react';

/**
 * Theme switcher component
 */
export default function ThemeSwitcher() {
  // Try to use the theme context, but don't throw an error if it's not available
  let theme = 'system';
  let setTheme = (newTheme: string) => {
    // Fallback implementation when ThemeProvider is not available
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme);

      if (newTheme === 'dark' || (newTheme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
      }
    }
  };

  try {
    const themeContext = useTheme();
    theme = themeContext.theme;
    setTheme = themeContext.setTheme;
  } catch (error) {
    // If ThemeProvider is not available, use the fallback implementation
    console.warn('ThemeSwitcher: ThemeProvider not found, using fallback implementation');
  }

  return (
    <div className="flex items-center space-x-1">
      <Button
        variant={theme === 'light' ? 'default' : 'ghost'}
        size="icon"
        onClick={() => setTheme('light')}
        title="Light mode"
      >
        <Sun className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">Light mode</span>
      </Button>

      <Button
        variant={theme === 'dark' ? 'default' : 'ghost'}
        size="icon"
        onClick={() => setTheme('dark')}
        title="Dark mode"
      >
        <Moon className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">Dark mode</span>
      </Button>

      <Button
        variant={theme === 'system' ? 'default' : 'ghost'}
        size="icon"
        onClick={() => setTheme('system')}
        title="System preference"
      >
        <Laptop className="h-[1.2rem] w-[1.2rem]" />
        <span className="sr-only">System preference</span>
      </Button>
    </div>
  );
}
