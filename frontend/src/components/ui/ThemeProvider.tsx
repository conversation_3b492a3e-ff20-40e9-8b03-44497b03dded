'use client';

import { createContext, useContext, useState, useEffect } from 'react';

// Theme types
type Theme = 'light' | 'dark' | 'system';

// Theme context interface
interface ThemeContextType {
  theme: Theme;
  setTheme: (theme: Theme) => void;
}

// Create context
const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

/**
 * Theme provider component
 */
export function ThemeProvider({ children }: { children: React.ReactNode }) {
  const [theme, setTheme] = useState<Theme>('system');
  const [mounted, setMounted] = useState(false);

  // Update the theme
  const updateTheme = (newTheme: Theme) => {
    // Update state
    setTheme(newTheme);

    // Only run browser-specific code on client-side
    if (typeof window !== 'undefined') {
      try {
        // Update localStorage
        localStorage.setItem('theme', newTheme);

        // Update document class
        if (newTheme === 'dark' || (newTheme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
          document.documentElement.classList.add('dark');
        } else {
          document.documentElement.classList.remove('dark');
        }
      } catch (error) {
        console.warn('ThemeProvider: Error updating theme', error);
      }
    }
  };

  // Initialize theme from localStorage or system preference
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    setMounted(true);

    try {
      const storedTheme = localStorage.getItem('theme') as Theme | null;
      if (storedTheme) {
        updateTheme(storedTheme);
      } else {
        updateTheme('system');
      }
    } catch (error) {
      console.warn('ThemeProvider: Error accessing localStorage', error);
      updateTheme('system');
    }
  }, []);

  // Listen for system preference changes
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;

    if (theme === 'system') {
      try {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');

        const handleChange = () => {
          if (mediaQuery.matches) {
            document.documentElement.classList.add('dark');
          } else {
            document.documentElement.classList.remove('dark');
          }
        };

        mediaQuery.addEventListener('change', handleChange);
        return () => mediaQuery.removeEventListener('change', handleChange);
      } catch (error) {
        console.warn('ThemeProvider: Error setting up media query listener', error);
      }
    }
  }, [theme]);

  // Avoid rendering with incorrect theme
  if (!mounted) {
    return <>{children}</>;
  }

  return (
    <ThemeContext.Provider value={{ theme, setTheme: updateTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}

/**
 * Hook to use the theme context
 */
export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
