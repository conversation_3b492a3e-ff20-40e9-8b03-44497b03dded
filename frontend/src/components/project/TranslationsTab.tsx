'use client';

import { useState, useEffect } from 'react';
import {
  useListProjectTranslationKeysQuery,
  useListTranslationsByKeyQuery,
  useUpdateTranslationMutation,
  useCreateTranslationMutation,
  useCreateTranslationKeyMutation,
  useUpdateTranslationKeyMutation,
  useDeleteTranslationKeyMutation,
  useAiTranslateMutation,
  useListProjectLocalesQuery,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Languages,
  Search,
  Plus,
  Save,
  Loader2,
  Sparkles,
  FileText,
  RefreshCw,
  ChevronRight,
  ChevronLeft,
  Pencil,
  Trash2,
  AlertCircle,
  Settings,
} from 'lucide-react';
import { ImageUploader } from '@/components/shared/ImageUploader';
import { AIProviderSelector } from '@/components/translation/AIProviderSelector';

interface TranslationsTabProps {
  projectId: string;
  projectSlug: string;
}



export function TranslationsTab({ projectId, projectSlug }: TranslationsTabProps) {
  const { toast } = useToast();
  const [selectedKey, setSelectedKey] = useState<string>('');
  const [selectedLocale, setSelectedLocale] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [translationContent, setTranslationContent] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [currentKeyIndex, setCurrentKeyIndex] = useState<number>(0);
  const [translationView, setTranslationView] = useState<'single' | 'all'>('single');
  // State for tracking translation in progress
  const [isTranslatingInProgress, setIsTranslatingInProgress] = useState<boolean>(false);

  // AI translation provider and model state
  const [aiProvider, setAiProvider] = useState<string>('openai');
  const [aiModel, setAiModel] = useState<string>('gpt-4');
  const [isAiSettingsDialogOpen, setIsAiSettingsDialogOpen] = useState<boolean>(false);

  // Add Translation Key dialog state
  const [isAddKeyDialogOpen, setIsAddKeyDialogOpen] = useState<boolean>(false);
  const [newKeyName, setNewKeyName] = useState<string>('');
  const [newKeyDescription, setNewKeyDescription] = useState<string>('');
  const [newKeyContext, setNewKeyContext] = useState<string>('');
  const [newKeyImageUrl, setNewKeyImageUrl] = useState<string>('');

  // Edit Translation Key dialog state
  const [isEditKeyDialogOpen, setIsEditKeyDialogOpen] = useState<boolean>(false);
  const [editKeyName, setEditKeyName] = useState<string>('');
  const [editKeyDescription, setEditKeyDescription] = useState<string>('');
  const [editKeyContext, setEditKeyContext] = useState<string>('');
  const [editKeyImageUrl, setEditKeyImageUrl] = useState<string>('');

  // Delete Translation Key dialog state
  const [isDeleteKeyDialogOpen, setIsDeleteKeyDialogOpen] = useState<boolean>(false);

  // Fetch project locales
  const {
    data: localesData,
    isLoading: isLoadingLocales,
    error: localesError
  } = useListProjectLocalesQuery(projectId);

  // Fetch all translation keys for the project
  const {
    data: keysData,
    isLoading: isLoadingKeys,
    error: keysError,
    refetch: refetchKeys
  } = useListProjectTranslationKeysQuery({
    projectSlug,
    resourceId: undefined
  });

  // Fetch translations for the selected key
  const {
    data: translationsData,
    isLoading: isLoadingTranslations,
    error: translationsError,
    refetch: refetchTranslations
  } = useListTranslationsByKeyQuery(selectedKey, {
    skip: !selectedKey
  });

  // Mutations
  const [updateTranslation, { isLoading: isUpdating }] = useUpdateTranslationMutation();
  const [createTranslation, { isLoading: isCreating }] = useCreateTranslationMutation();
  const [createTranslationKey, { isLoading: isCreatingKey }] = useCreateTranslationKeyMutation();
  const [updateTranslationKey, { isLoading: isUpdatingKey }] = useUpdateTranslationKeyMutation();
  const [deleteTranslationKey, { isLoading: isDeletingKey }] = useDeleteTranslationKeyMutation();
  const [aiTranslate, { isLoading: isTranslating }] = useAiTranslateMutation();

  // Filter keys by search query
  const filteredKeys = keysData?.data?.filter(key =>
    key.key_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (key.description && key.description.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Find the current translation for the selected locale
  const currentTranslation = translationsData?.data?.find(
    t => t.locale_id === selectedLocale
  );

  // Find the source translation
  const sourceLocale = localesData?.data?.find(locale => locale.is_source);
  const sourceTranslation = translationsData?.data?.find(
    t => sourceLocale && t.locale_id === sourceLocale.locale_id
  );

  // Group translations by locale for the all languages view
  const translationsByLocale = translationsData?.data?.reduce((acc, translation) => {
    acc[translation.locale_id] = translation;
    return acc;
  }, {} as Record<string, any>);

  // Find the translation status - this would be implemented in a real app
  // This is just a placeholder for the concept

  // Set translation content when a translation is selected
  useEffect(() => {
    if (currentTranslation) {
      setTranslationContent(currentTranslation.content);
      setIsEditing(true);
    } else {
      setTranslationContent('');
      setIsEditing(false);
    }
  }, [currentTranslation]);

  // Reset translation view when key changes
  useEffect(() => {
    if (selectedKey) {
      // Keep the current view mode when changing keys
    }
  }, [selectedKey]);

  // Set current key index when filtered keys change
  useEffect(() => {
    if (filteredKeys && selectedKey) {
      const index = filteredKeys.findIndex(key => key.id === selectedKey);
      if (index !== -1) {
        setCurrentKeyIndex(index);
      }
    }
  }, [filteredKeys, selectedKey]);

  // Handle key selection
  const handleKeySelect = (keyId: string) => {
    setSelectedKey(keyId);
  };

  // Handle locale selection
  const handleLocaleChange = (value: string) => {
    setSelectedLocale(value);
  };

  // Handle search input
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle translation content change
  const handleContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTranslationContent(e.target.value);
  };

  // Navigate to previous key
  const handlePreviousKey = () => {
    if (filteredKeys && currentKeyIndex > 0) {
      const prevIndex = currentKeyIndex - 1;
      setSelectedKey(filteredKeys[prevIndex].id);
      setCurrentKeyIndex(prevIndex);
    }
  };

  // Navigate to next key
  const handleNextKey = () => {
    if (filteredKeys && currentKeyIndex < filteredKeys.length - 1) {
      const nextIndex = currentKeyIndex + 1;
      setSelectedKey(filteredKeys[nextIndex].id);
      setCurrentKeyIndex(nextIndex);
    }
  };

  // Handle save translation
  const handleSaveTranslation = async () => {
    if (!selectedKey || !selectedLocale || !translationContent.trim()) {
      toast({
        title: "Error",
        description: "Please select a key, locale, and enter translation content",
        variant: "destructive"
      });
      return;
    }

    try {
      if (currentTranslation) {
        // Update existing translation
        await updateTranslation({
          id: currentTranslation.id,
          data: {
            content: translationContent,
            is_reviewed: true
          }
        }).unwrap();

        toast({
          title: "Success",
          description: "Translation updated successfully",
        });
      } else {
        // Create new translation
        await createTranslation({
          key_id: selectedKey,
          locale_id: selectedLocale,
          content: translationContent
        }).unwrap();

        toast({
          title: "Success",
          description: "Translation created successfully",
        });
      }

      refetchTranslations();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save translation",
        variant: "destructive"
      });
    }
  };



  // Handle creating a new translation key
  const handleCreateTranslationKey = async () => {
    if (!newKeyName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a key name",
        variant: "destructive"
      });
      return;
    }

    try {
      // Create the translation key directly with the project ID
      await createTranslationKey({
        projectSlug,
        key: {
          key_name: newKeyName.trim(),
          description: newKeyDescription.trim() || undefined,
          context: newKeyContext.trim() || undefined,
          image_url: newKeyImageUrl.trim() || undefined,
          screenshot_url: newKeyImageUrl.trim() || undefined
        }
      }).unwrap();

      // Reset form and close dialog
      setNewKeyName('');
      setNewKeyDescription('');
      setNewKeyContext('');
      setNewKeyImageUrl('');
      setIsAddKeyDialogOpen(false);

      // Refetch keys
      refetchKeys();

      toast({
        title: "Success",
        description: "Translation key created successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create translation key",
        variant: "destructive"
      });
    }
  };

  // Handle opening the edit dialog
  const handleOpenEditDialog = (key: any) => {
    setEditKeyName(key.key_name);
    setEditKeyDescription(key.description || '');
    setEditKeyContext(key.context || '');
    setEditKeyImageUrl(key.image_url || key.screenshot_url || '');
    setIsEditKeyDialogOpen(true);
  };

  // Handle updating a translation key
  const handleUpdateTranslationKey = async () => {
    if (!selectedKey || !editKeyName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a key name",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateTranslationKey({
        id: selectedKey,
        data: {
          key_name: editKeyName.trim(),
          description: editKeyDescription.trim() || undefined,
          context: editKeyContext.trim() || undefined,
          image_url: editKeyImageUrl.trim() || undefined,
          screenshot_url: editKeyImageUrl.trim() || undefined
        }
      }).unwrap();

      // Close dialog
      setIsEditKeyDialogOpen(false);

      // Refetch keys
      refetchKeys();

      toast({
        title: "Success",
        description: "Translation key updated successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update translation key",
        variant: "destructive"
      });
    }
  };

  // Handle deleting a translation key
  const handleDeleteTranslationKey = async () => {
    if (!selectedKey) {
      return;
    }

    try {
      await deleteTranslationKey(selectedKey).unwrap();

      // Close dialog
      setIsDeleteKeyDialogOpen(false);

      // Reset selected key
      setSelectedKey('');

      // Refetch keys
      refetchKeys();

      toast({
        title: "Success",
        description: "Translation key deleted successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete translation key",
        variant: "destructive"
      });
    }
  };

  // Handle AI translation
  const handleAiTranslate = async (targetLocaleId?: string) => {
    // Use provided target locale ID or fall back to selected locale
    const localeToUse = targetLocaleId || selectedLocale;

    // Find source locale (the one marked as is_source)
    const sourceLocale = localesData?.data?.find(locale => locale.is_source);

    if (!sourceLocale) {
      toast({
        title: "Error",
        description: "No source locale found. Please set a source locale first.",
        variant: "destructive"
      });
      return;
    }

    // Find source translation
    const sourceTranslation = translationsData?.data?.find(
      t => t.locale_id === sourceLocale.locale_id
    );

    if (!sourceTranslation) {
      toast({
        title: "Error",
        description: "No source translation found. Please add a translation in the source locale first.",
        variant: "destructive"
      });
      return;
    }

    // Get target locale code
    const targetLocale = localesData?.data?.find(locale => locale.id === localeToUse);

    if (!targetLocale || !targetLocale.locale_code) {
      toast({
        title: "Error",
        description: "Target locale information is incomplete.",
        variant: "destructive"
      });
      return;
    }

    try {
      // If we're switching locales, update the selected locale first
      if (targetLocaleId && targetLocaleId !== selectedLocale) {
        setSelectedLocale(targetLocaleId);
      }

      const result = await aiTranslate({
        text: sourceTranslation.content,
        source_locale: sourceLocale.locale_code || 'en',
        target_locale: targetLocale.locale_code,
        context: keysData?.data?.find(k => k.id === selectedKey)?.context,
        provider: aiProvider,
        model: aiModel
      }).unwrap();

      setTranslationContent(result.data.translated_text);

      toast({
        title: "AI Translation Generated",
        description: `Used ${result.data.credits_used} credits. ${result.data.credits_remaining} credits remaining.`,
      });

      // If we're in the all languages view, we need to save the translation immediately
      if (translationView === 'all' && targetLocaleId && targetLocaleId !== selectedLocale) {
        // Create a new translation or update existing one
        const existingTranslation = translationsData?.data?.find(
          t => t.locale_id === targetLocaleId
        );

        if (existingTranslation) {
          await updateTranslation({
            id: existingTranslation.id,
            data: {
              content: result.data.translated_text,
              is_reviewed: false // Mark as not reviewed since it's AI-generated
            }
          }).unwrap();
        } else {
          await createTranslation({
            key_id: selectedKey,
            locale_id: targetLocaleId,
            content: result.data.translated_text
          }).unwrap();
        }

        // Refetch translations to update the UI
        refetchTranslations();
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to generate AI translation",
        variant: "destructive"
      });
    }
  };

  // Handle translating all languages at once
  const handleTranslateAllLanguages = async () => {
    if (!selectedKey) {
      toast({
        title: "Error",
        description: "Please select a translation key first",
        variant: "destructive"
      });
      return;
    }

    // Find source locale and translation
    const sourceLocale = localesData?.data?.find(locale => locale.is_source);
    if (!sourceLocale) {
      toast({
        title: "Error",
        description: "No source locale found",
        variant: "destructive"
      });
      return;
    }

    const sourceTranslation = translationsData?.data?.find(
      t => t.locale_id === sourceLocale.locale_id
    );
    if (!sourceTranslation) {
      toast({
        title: "Error",
        description: "No source translation found. Please add a translation in the source locale first.",
        variant: "destructive"
      });
      return;
    }

    // Get all target locales (non-source locales)
    const targetLocales = localesData?.data?.filter(locale => !locale.is_source);
    if (!targetLocales || targetLocales.length === 0) {
      toast({
        title: "Error",
        description: "No target locales found",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsTranslatingInProgress(true);

      // Process each target locale
      for (const targetLocale of targetLocales) {
        // Skip if translation already exists
        const existingTranslation = translationsData?.data?.find(
          t => t.locale_id === targetLocale.locale_id
        );
        if (existingTranslation) continue;

        // Generate AI translation
        const result = await aiTranslate({
          text: sourceTranslation.content,
          source_locale: sourceLocale.locale_code || 'en',
          target_locale: targetLocale.locale_code || 'en',
          context: keysData?.data?.find(k => k.id === selectedKey)?.context,
          provider: aiProvider,
          model: aiModel
        }).unwrap();

        // Create new translation
        await createTranslation({
          key_id: selectedKey,
          locale_id: targetLocale.locale_id,
          content: result.data.translated_text
        }).unwrap();
      }

      // Refetch translations to update the UI
      await refetchTranslations();

      toast({
        title: "Success",
        description: "All languages have been translated",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to translate all languages",
        variant: "destructive"
      });
    } finally {
      setIsTranslatingInProgress(false);
    }
  };

  // Loading state
  if (isLoadingLocales) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (localesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error</CardTitle>
          <CardDescription>Failed to load translation resources</CardDescription>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the translation resources. Please try again.</p>
          <Button onClick={() => window.location.reload()} className="mt-4">Retry</Button>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (isLoadingLocales) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-10 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    );
  }

  // Error state
  if (localesError) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Error</CardTitle>
          <CardDescription>Failed to load translation resources</CardDescription>
        </CardHeader>
        <CardContent>
          <p>There was an error loading the translation resources. Please try again.</p>
          <Button onClick={() => window.location.reload()} className="mt-4">Retry</Button>
        </CardContent>
      </Card>
    );
  }

  // Get the selected key details
  const selectedKeyDetails = keysData?.data?.find(k => k.id === selectedKey);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Translations</h2>
          <p className="text-muted-foreground">Manage your project translations</p>
        </div>
        <div className="flex space-x-2">
          {/* Add Translation Key Dialog */}
          <Dialog open={isAddKeyDialogOpen} onOpenChange={setIsAddKeyDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Add Translation Key
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Translation Key</DialogTitle>
                <DialogDescription>
                  Create a new translation key for your project
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="keyName">Key Name *</Label>
                  <Input
                    id="keyName"
                    value={newKeyName}
                    onChange={(e) => setNewKeyName(e.target.value)}
                    placeholder="Enter key name (e.g. common.buttons.save)"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="description">Description</Label>
                  <Input
                    id="description"
                    value={newKeyDescription}
                    onChange={(e) => setNewKeyDescription(e.target.value)}
                    placeholder="Enter description (optional)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="context">Context</Label>
                  <Textarea
                    id="context"
                    value={newKeyContext}
                    onChange={(e) => setNewKeyContext(e.target.value)}
                    placeholder="Enter context for translators (optional)"
                    className="min-h-[80px]"
                  />
                </div>

                <ImageUploader
                  onImageUploaded={(imageUrl) => setNewKeyImageUrl(imageUrl)}
                  className="space-y-2"
                />

                {newKeyImageUrl && (
                  <div className="mt-4">
                    <Label className="mb-2 block">Current Screenshot</Label>
                    <div className="border rounded-md overflow-hidden">
                      <img
                        src={newKeyImageUrl}
                        alt="Translation context screenshot"
                        className="w-full h-auto max-h-[200px] object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleCreateTranslationKey}
                  disabled={isCreatingKey || !newKeyName.trim()}
                >
                  {isCreatingKey ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    'Create Key'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Edit Translation Key Dialog */}
          <Dialog open={isEditKeyDialogOpen} onOpenChange={setIsEditKeyDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit Translation Key</DialogTitle>
                <DialogDescription>
                  Update the translation key details
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="editKeyName">Key Name *</Label>
                  <Input
                    id="editKeyName"
                    value={editKeyName}
                    onChange={(e) => setEditKeyName(e.target.value)}
                    placeholder="Enter key name (e.g. common.buttons.save)"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="editDescription">Description</Label>
                  <Input
                    id="editDescription"
                    value={editKeyDescription}
                    onChange={(e) => setEditKeyDescription(e.target.value)}
                    placeholder="Enter description (optional)"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="editContext">Context</Label>
                  <Textarea
                    id="editContext"
                    value={editKeyContext}
                    onChange={(e) => setEditKeyContext(e.target.value)}
                    placeholder="Enter context for translators (optional)"
                    className="min-h-[80px]"
                  />
                </div>

                <ImageUploader
                  onImageUploaded={(imageUrl) => setEditKeyImageUrl(imageUrl)}
                  className="space-y-2"
                />

                {editKeyImageUrl && (
                  <div className="mt-4">
                    <Label className="mb-2 block">Current Screenshot</Label>
                    <div className="border rounded-md overflow-hidden">
                      <img
                        src={editKeyImageUrl}
                        alt="Translation context screenshot"
                        className="w-full h-auto max-h-[200px] object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button
                  type="submit"
                  onClick={handleUpdateTranslationKey}
                  disabled={isUpdatingKey || !editKeyName.trim()}
                >
                  {isUpdatingKey ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    'Update Key'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Translation Key Dialog */}
          <Dialog open={isDeleteKeyDialogOpen} onOpenChange={setIsDeleteKeyDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Translation Key</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this translation key? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <div className="flex items-center p-4 border rounded-md bg-muted">
                  <AlertCircle className="h-5 w-5 text-destructive mr-2" />
                  <p className="text-sm">All translations associated with this key will also be deleted.</p>
                </div>
              </div>
              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setIsDeleteKeyDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteTranslationKey}
                  disabled={isDeletingKey}
                >
                  {isDeletingKey ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Deleting...
                    </>
                  ) : (
                    'Delete Key'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs defaultValue="editor" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="editor" className="flex items-center">
            <Languages className="mr-2 h-4 w-4" />
            Translation Editor
          </TabsTrigger>
          <TabsTrigger value="keys" className="flex items-center">
            <FileText className="mr-2 h-4 w-4" />
            Translation Keys
          </TabsTrigger>
        </TabsList>

        {/* Translation Editor Tab */}
        <TabsContent value="editor" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {/* Locale Selection */}
            <Card className="md:col-span-1">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg">Target Locale</CardTitle>
              </CardHeader>
              <CardContent>
                <Select value={selectedLocale} onValueChange={handleLocaleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a locale" />
                  </SelectTrigger>
                  <SelectContent>
                    {localesData?.data?.map(locale => (
                      <SelectItem key={locale.id} value={locale.locale_id}>
                        {locale.locale_name} ({locale.locale_code})
                        {locale.is_source && " - Source"}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <div className="mt-4 space-y-2">
                  <Label className="text-sm font-medium">Quick Language Switch</Label>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {localesData?.data?.filter(locale => !locale.is_source).map(locale => (
                      <Button
                        key={locale.id}
                        variant={selectedLocale === locale.locale_id ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedLocale(locale.locale_id)}
                        className="text-xs"
                      >
                        {locale.locale_code}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Translation Keys List */}
            <Card className="md:col-span-3">
              <CardHeader className="pb-2">
                <CardTitle className="text-lg flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  Translation Keys
                </CardTitle>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search keys..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="pl-10"
                  />
                </div>
              </CardHeader>
              <CardContent className="p-0">
                {isLoadingKeys ? (
                  <div className="p-4 space-y-2">
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                    <Skeleton className="h-8 w-full" />
                  </div>
                ) : keysError ? (
                  <div className="p-4">
                    <p className="text-destructive">Failed to load translation keys</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => refetchKeys()}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Retry
                    </Button>
                  </div>
                ) : keysData?.data?.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">
                    No translation keys found. Click "Add Translation Key" to create one.
                  </div>
                ) : filteredKeys?.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">
                    No translation keys found matching your search
                  </div>
                ) : (
                  <div className="max-h-[300px] overflow-y-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Key</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Image</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredKeys?.map(key => (
                          <TableRow
                            key={key.id}
                            className={`cursor-pointer ${selectedKey === key.id ? 'bg-muted' : ''}`}
                            onClick={() => handleKeySelect(key.id)}
                          >
                            <TableCell className="font-medium">{key.key_name}</TableCell>
                            <TableCell>{key.description || '-'}</TableCell>
                            <TableCell>
                              {key.image_url || key.screenshot_url ? (
                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                  <img src="/icons/image.svg" alt="Has image" className="w-4 h-4 mr-1" />
                                  Available
                                </Badge>
                              ) : (
                                <span className="text-muted-foreground text-sm">None</span>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                )}
              </CardContent>
              {filteredKeys && filteredKeys.length > 0 && (
                <div className="flex justify-between items-center p-4 border-t">
                  <div className="text-sm text-muted-foreground">
                    {filteredKeys.length} keys found
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handlePreviousKey}
                      disabled={!filteredKeys || currentKeyIndex <= 0}
                    >
                      <ChevronLeft className="h-4 w-4 mr-1" />
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleNextKey}
                      disabled={!filteredKeys || currentKeyIndex >= filteredKeys.length - 1}
                    >
                      Next
                      <ChevronRight className="h-4 w-4 ml-1" />
                    </Button>
                  </div>
                </div>
              )}
            </Card>
          </div>

          {/* Translation Editor */}
          {selectedKey && (
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-center">
                  <div>
                    <CardTitle className="flex items-center">
                      <Languages className="mr-2 h-5 w-5 text-primary" />
                      Translation Editor
                    </CardTitle>
                    <CardDescription>
                      {selectedKeyDetails?.key_name}
                      {selectedKeyDetails?.description && ` - ${selectedKeyDetails.description}`}
                    </CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <div className="flex space-x-2 mr-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          if (selectedKeyDetails) {
                            handleOpenEditDialog(selectedKeyDetails);
                          }
                        }}
                        title="Edit Translation Key"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsDeleteKeyDialogOpen(true)}
                        title="Delete Translation Key"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>
                    <Button
                      variant={translationView === 'single' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTranslationView('single')}
                    >
                      Single Language
                    </Button>
                    <Button
                      variant={translationView === 'all' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTranslationView('all')}
                    >
                      All Languages
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Display image if available */}
                {(selectedKeyDetails?.image_url || selectedKeyDetails?.screenshot_url) && (
                  <div className="mb-4">
                    <Label className="mb-2 block">Screenshot Reference</Label>
                    <div className="border rounded-md overflow-hidden">
                      <img
                        src={selectedKeyDetails?.image_url || selectedKeyDetails?.screenshot_url}
                        alt="Translation context screenshot"
                        className="w-full h-auto max-h-[200px] object-contain"
                      />
                    </div>
                  </div>
                )}

                {/* Context information */}
                {selectedKeyDetails?.context && (
                  <div className="mb-4 p-3 bg-muted rounded-md">
                    <Label className="mb-1 block font-medium">Context for Translators:</Label>
                    <p className="text-sm">{selectedKeyDetails.context}</p>
                  </div>
                )}

                {isLoadingTranslations ? (
                  <Skeleton className="h-32 w-full" />
                ) : translationsError ? (
                  <div>
                    <p className="text-destructive">Failed to load translations</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-2"
                      onClick={() => refetchTranslations()}
                    >
                      <RefreshCw className="mr-2 h-4 w-4" />
                      Retry
                    </Button>
                  </div>
                ) : translationView === 'single' && !selectedLocale ? (
                  <div className="text-center text-muted-foreground p-6 bg-muted rounded-md">
                    Please select a target locale from the dropdown above to start translating
                  </div>
                ) : translationView === 'single' ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Source translation (if available) */}
                    {sourceLocale && (
                      <div className="space-y-2">
                        <Label className="flex items-center">
                          Source: {sourceLocale.locale_name} ({sourceLocale.locale_code})
                        </Label>
                        <div className="border rounded-md p-3 bg-muted min-h-[100px]">
                          {sourceTranslation ? (
                            <p>{sourceTranslation.content}</p>
                          ) : (
                            <p className="text-muted-foreground italic">No source translation available</p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Target translation */}
                    <div className="space-y-2">
                      <Label className="flex items-center">
                        Target: {localesData?.data?.find(l => l.id === selectedLocale)?.locale_name}
                        ({localesData?.data?.find(l => l.id === selectedLocale)?.locale_code})
                      </Label>
                      <Textarea
                        value={translationContent}
                        onChange={handleContentChange}
                        placeholder="Enter translation..."
                        className="min-h-[100px]"
                      />
                    </div>
                  </div>
                ) : (
                  // All languages view
                  <div className="space-y-6">
                    {/* Source translation at the top */}
                    {sourceLocale && (
                      <div className="space-y-2 border-b pb-4">
                        <Label className="flex items-center font-bold">
                          Source: {sourceLocale.locale_name} ({sourceLocale.locale_code})
                        </Label>
                        <div className="border rounded-md p-3 bg-muted">
                          {sourceTranslation ? (
                            <p>{sourceTranslation.content}</p>
                          ) : (
                            <p className="text-muted-foreground italic">No source translation available</p>
                          )}
                        </div>
                      </div>
                    )}

                    {/* All target languages */}
                    <div className="space-y-6">
                      <div className="flex justify-between items-center">
                        <h3 className="text-lg font-medium">Target Languages</h3>
                        {sourceTranslation && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleTranslateAllLanguages}
                            disabled={isTranslating || isTranslatingInProgress}
                          >
                            {isTranslatingInProgress ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Translating All...
                              </>
                            ) : (
                              <>
                                <Sparkles className="mr-2 h-4 w-4" />
                                Translate All Languages
                              </>
                            )}
                          </Button>
                        )}
                      </div>
                      {localesData?.data?.filter(locale => !locale.is_source).map(locale => {
                        const localeTranslation = translationsByLocale?.[locale.locale_id];
                        const isCurrentLocale = selectedLocale === locale.locale_id;

                        return (
                          <div key={locale.id} className={`p-4 border rounded-lg ${isCurrentLocale ? 'border-primary' : ''}`}>
                            <div className="flex justify-between items-center mb-2">
                              <Label className="flex items-center font-medium">
                                {locale.locale_name} ({locale.locale_code})
                              </Label>
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setSelectedLocale(locale.locale_id)}
                                >
                                  {isCurrentLocale ? 'Currently Editing' : 'Edit'}
                                </Button>
                                {!localeTranslation && sourceTranslation && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleAiTranslate(locale.locale_id)}
                                    disabled={isTranslating || isTranslatingInProgress}
                                  >
                                    <Sparkles className="mr-1 h-3 w-3" />
                                    AI Translate
                                  </Button>
                                )}
                              </div>
                            </div>

                            {isCurrentLocale ? (
                              <Textarea
                                value={translationContent}
                                onChange={handleContentChange}
                                placeholder="Enter translation..."
                                className="min-h-[100px] mt-2"
                              />
                            ) : (
                              <div className="border rounded-md p-3 bg-muted min-h-[100px]">
                                {localeTranslation ? (
                                  <p>{localeTranslation.content}</p>
                                ) : (
                                  <p className="text-muted-foreground italic">No translation available</p>
                                )}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {selectedLocale && (
                  <div className="flex space-x-2 pt-2">
                    <Button
                      onClick={handleSaveTranslation}
                      disabled={isUpdating || isCreating || !translationContent.trim()}
                    >
                      {(isUpdating || isCreating) ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        <>
                          <Save className="mr-2 h-4 w-4" />
                          {isEditing ? 'Update' : 'Create'} Translation
                        </>
                      )}
                    </Button>

                    <div className="flex space-x-2">
                      <Button
                        variant="outline"
                        onClick={() => handleAiTranslate()}
                        disabled={isTranslating || isTranslatingInProgress || !selectedLocale || !sourceTranslation}
                      >
                        {isTranslating ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Translating...
                          </>
                        ) : (
                          <>
                            <Sparkles className="mr-2 h-4 w-4" />
                            AI Translate
                          </>
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => setIsAiSettingsDialogOpen(true)}
                        title="AI Translation Settings"
                      >
                        <Settings className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Translation Keys Management Tab */}
        <TabsContent value="keys" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Translation Keys</CardTitle>
              <CardDescription>Manage all translation keys in your project</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="relative mb-4">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search keys..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  className="pl-10"
                />
              </div>

              {isLoadingKeys ? (
                <div className="space-y-2">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ) : keysError ? (
                <div>
                  <p className="text-destructive">Failed to load translation keys</p>
                  <Button
                    variant="outline"
                    size="sm"
                    className="mt-2"
                    onClick={() => refetchKeys()}
                  >
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Retry
                  </Button>
                </div>
              ) : keysData?.data?.length === 0 ? (
                <div className="text-center text-muted-foreground p-6">
                  No translation keys found. Click "Add Translation Key" to create one.
                </div>
              ) : filteredKeys?.length === 0 ? (
                <div className="text-center text-muted-foreground p-6">
                  No translation keys found matching your search
                </div>
              ) : (
                <div className="max-h-[500px] overflow-y-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Key</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Context</TableHead>
                        <TableHead>Image</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredKeys?.map(key => (
                        <TableRow key={key.id}>
                          <TableCell className="font-medium">{key.key_name}</TableCell>
                          <TableCell>{key.description || '-'}</TableCell>
                          <TableCell>{key.context ? (
                            <span className="text-sm">{key.context.substring(0, 50)}{key.context.length > 50 ? '...' : ''}</span>
                          ) : '-'}</TableCell>
                          <TableCell>
                            {key.image_url || key.screenshot_url ? (
                              <Badge variant="secondary" className="bg-green-100 text-green-800">
                                <img src="/icons/image.svg" alt="Has image" className="w-4 h-4 mr-1" />
                                Available
                              </Badge>
                            ) : (
                              <span className="text-muted-foreground text-sm">None</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedKey(key.id);
                                  handleOpenEditDialog(key);
                                }}
                                title="Edit Translation Key"
                              >
                                <Pencil className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  setSelectedKey(key.id);
                                  setIsDeleteKeyDialogOpen(true);
                                }}
                                title="Delete Translation Key"
                              >
                                <Trash2 className="h-4 w-4 text-destructive" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
            <div className="p-4 border-t">
              <Button onClick={() => setIsAddKeyDialogOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add Translation Key
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* AI Settings Dialog */}
      <Dialog open={isAiSettingsDialogOpen} onOpenChange={setIsAiSettingsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>AI Translation Settings</DialogTitle>
            <DialogDescription>
              Configure the AI provider and model for translations
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <AIProviderSelector
              onProviderChange={setAiProvider}
              onModelChange={setAiModel}
              defaultProvider={aiProvider}
              defaultModel={aiModel}
            />
          </div>
          <DialogFooter>
            <Button onClick={() => setIsAiSettingsDialogOpen(false)}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
