'use client';

import { useState, useEffect } from 'react';
import { 
  useCreateApiKeyMutation,
  useGetPermissionsQuery
} from '@/lib/redux/api/endpoints/apiKeyApi';
import {
  useListPermissionGroupsQuery
} from '@/lib/redux/api/endpoints/permissionGroupApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { AlertCircle, Copy, Check } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';

interface CreateApiKeyFormProps {
  organizationId: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

export default function CreateApiKeyForm({
  organizationId,
  isOpen,
  onClose,
  onSuccess
}: CreateApiKeyFormProps) {
  const { toast } = useToast();
  
  // State for API key creation
  const [newKeyName, setNewKeyName] = useState('');
  const [newApiKey, setNewApiKey] = useState('');
  const [permissionTab, setPermissionTab] = useState('basic');
  const [copied, setCopied] = useState(false);
  
  // Basic permissions
  const [readPermission, setReadPermission] = useState(true);
  const [writePermission, setWritePermission] = useState(false);
  const [adminPermission, setAdminPermission] = useState(false);
  
  // Resource-specific permissions
  const [projectsRead, setProjectsRead] = useState(true);
  const [projectsWrite, setProjectsWrite] = useState(false);
  const [translationsRead, setTranslationsRead] = useState(true);
  const [translationsWrite, setTranslationsWrite] = useState(false);
  const [localesRead, setLocalesRead] = useState(true);
  const [localesWrite, setLocalesWrite] = useState(false);
  const [organizationsRead, setOrganizationsRead] = useState(true);
  const [organizationsWrite, setOrganizationsWrite] = useState(false);
  const [aiCreditsRead, setAiCreditsRead] = useState(true);
  const [aiCreditsWrite, setAiCreditsWrite] = useState(false);
  
  // Rate limiting
  const [rateLimit, setRateLimit] = useState<number | undefined>(undefined);
  const [rateLimitPeriod, setRateLimitPeriod] = useState('minute');
  
  // Permission group
  const [selectedGroupId, setSelectedGroupId] = useState<string | undefined>(undefined);
  
  // Queries and mutations
  const [createApiKey, { isLoading: isCreatingKey }] = useCreateApiKeyMutation();
  const { data: permissionsData } = useGetPermissionsQuery();
  const { data: permissionGroupsData } = useListPermissionGroupsQuery({ organizationId });
  
  // Reset form when dialog is closed
  useEffect(() => {
    if (!isOpen) {
      resetForm();
    }
  }, [isOpen]);
  
  // Reset form
  const resetForm = () => {
    setNewKeyName('');
    setNewApiKey('');
    setPermissionTab('basic');
    
    // Reset basic permissions
    setReadPermission(true);
    setWritePermission(false);
    setAdminPermission(false);
    
    // Reset resource-specific permissions
    setProjectsRead(true);
    setProjectsWrite(false);
    setTranslationsRead(true);
    setTranslationsWrite(false);
    setLocalesRead(true);
    setLocalesWrite(false);
    setOrganizationsRead(true);
    setOrganizationsWrite(false);
    setAiCreditsRead(true);
    setAiCreditsWrite(false);
    
    // Reset rate limiting
    setRateLimit(undefined);
    setRateLimitPeriod('minute');
    
    // Reset permission group
    setSelectedGroupId(undefined);
    
    // Reset copied state
    setCopied(false);
  };
  
  // Handle close
  const handleClose = () => {
    if (newApiKey) {
      onSuccess();
    }
    onClose();
  };
  
  // Copy API key to clipboard
  const copyToClipboard = () => {
    navigator.clipboard.writeText(newApiKey);
    setCopied(true);
    toast({
      title: "Copied to clipboard",
      description: "API key has been copied to clipboard",
    });
    
    // Reset copied state after 3 seconds
    setTimeout(() => {
      setCopied(false);
    }, 3000);
  };
  
  // Get permissions based on selected options
  const getSelectedPermissions = (): string[] => {
    const permissions: string[] = [];
    
    if (permissionTab === 'basic') {
      if (readPermission) permissions.push('read');
      if (writePermission) permissions.push('write');
      if (adminPermission) permissions.push('admin');
    } else if (permissionTab === 'resource') {
      if (projectsRead) permissions.push('projects:read');
      if (projectsWrite) permissions.push('projects:write');
      if (translationsRead) permissions.push('translations:read');
      if (translationsWrite) permissions.push('translations:write');
      if (localesRead) permissions.push('locales:read');
      if (localesWrite) permissions.push('locales:write');
      if (organizationsRead) permissions.push('organizations:read');
      if (organizationsWrite) permissions.push('organizations:write');
      if (aiCreditsRead) permissions.push('ai-credits:read');
      if (aiCreditsWrite) permissions.push('ai-credits:write');
    }
    
    return permissions;
  };
  
  // Handle create API key
  const handleCreateApiKey = async () => {
    if (!organizationId) return;
    if (!newKeyName.trim()) {
      toast({
        title: "Error",
        description: "API key name is required",
        variant: "destructive"
      });
      return;
    }
    
    try {
      // Get permissions based on selected tab
      const permissions = getSelectedPermissions();
      
      // Create request data
      const requestData: any = {
        name: newKeyName.trim(),
        permissions,
      };
      
      // Add rate limit if provided
      if (rateLimit) {
        requestData.rate_limit = rateLimit;
        requestData.rate_limit_period = rateLimitPeriod;
      }
      
      // Add permission group if selected
      if (selectedGroupId) {
        requestData.permission_group_id = selectedGroupId;
      }
      
      // Create API key
      const result = await createApiKey({
        organizationId,
        data: requestData
      }).unwrap();
      
      if (result.success && result.data) {
        setNewApiKey(result.data.key);
        toast({
          title: "Success",
          description: "API key created successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to create API key",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create API key",
        variant: "destructive"
      });
    }
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{newApiKey ? 'API Key Created' : 'Create API Key'}</DialogTitle>
          <DialogDescription>
            {newApiKey
              ? 'Copy your API key now. You won\'t be able to see it again.'
              : 'Create a new API key for programmatic access to the API.'}
          </DialogDescription>
        </DialogHeader>
        
        {newApiKey ? (
          <div className="space-y-4">
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md flex items-start">
              <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
              <p className="text-sm text-yellow-700">
                This API key will only be displayed once. Please copy it and store it securely.
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Input
                value={newApiKey}
                readOnly
                className="font-mono text-sm"
              />
              <Button
                variant="outline"
                size="icon"
                onClick={copyToClipboard}
                className="flex-shrink-0"
              >
                {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        ) : (
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="key-name">Name</Label>
              <Input
                id="key-name"
                placeholder="Enter a name for this API key"
                value={newKeyName}
                onChange={(e) => setNewKeyName(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label>Permissions</Label>
              <Tabs value={permissionTab} onValueChange={setPermissionTab}>
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="basic">Basic</TabsTrigger>
                  <TabsTrigger value="resource">Resource-specific</TabsTrigger>
                  <TabsTrigger value="group">Permission Group</TabsTrigger>
                </TabsList>
                
                <TabsContent value="basic" className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="read"
                        checked={readPermission}
                        onCheckedChange={(checked) => setReadPermission(checked as boolean)}
                      />
                      <Label htmlFor="read">Read (GET requests)</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="write"
                        checked={writePermission}
                        onCheckedChange={(checked) => setWritePermission(checked as boolean)}
                      />
                      <Label htmlFor="write">Write (POST, PUT, DELETE requests)</Label>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="admin"
                        checked={adminPermission}
                        onCheckedChange={(checked) => setAdminPermission(checked as boolean)}
                      />
                      <Label htmlFor="admin">Admin (full access)</Label>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="resource" className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Projects</h3>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="projects-read"
                          checked={projectsRead}
                          onCheckedChange={(checked) => setProjectsRead(checked as boolean)}
                        />
                        <Label htmlFor="projects-read">Read</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="projects-write"
                          checked={projectsWrite}
                          onCheckedChange={(checked) => setProjectsWrite(checked as boolean)}
                        />
                        <Label htmlFor="projects-write">Write</Label>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Translations</h3>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="translations-read"
                          checked={translationsRead}
                          onCheckedChange={(checked) => setTranslationsRead(checked as boolean)}
                        />
                        <Label htmlFor="translations-read">Read</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="translations-write"
                          checked={translationsWrite}
                          onCheckedChange={(checked) => setTranslationsWrite(checked as boolean)}
                        />
                        <Label htmlFor="translations-write">Write</Label>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Locales</h3>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="locales-read"
                          checked={localesRead}
                          onCheckedChange={(checked) => setLocalesRead(checked as boolean)}
                        />
                        <Label htmlFor="locales-read">Read</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="locales-write"
                          checked={localesWrite}
                          onCheckedChange={(checked) => setLocalesWrite(checked as boolean)}
                        />
                        <Label htmlFor="locales-write">Write</Label>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">Organizations</h3>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="organizations-read"
                          checked={organizationsRead}
                          onCheckedChange={(checked) => setOrganizationsRead(checked as boolean)}
                        />
                        <Label htmlFor="organizations-read">Read</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="organizations-write"
                          checked={organizationsWrite}
                          onCheckedChange={(checked) => setOrganizationsWrite(checked as boolean)}
                        />
                        <Label htmlFor="organizations-write">Write</Label>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <h3 className="text-sm font-medium">AI Credits</h3>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="ai-credits-read"
                          checked={aiCreditsRead}
                          onCheckedChange={(checked) => setAiCreditsRead(checked as boolean)}
                        />
                        <Label htmlFor="ai-credits-read">Read</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="ai-credits-write"
                          checked={aiCreditsWrite}
                          onCheckedChange={(checked) => setAiCreditsWrite(checked as boolean)}
                        />
                        <Label htmlFor="ai-credits-write">Write</Label>
                      </div>
                    </div>
                  </div>
                </TabsContent>
                
                <TabsContent value="group" className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="permission-group">Permission Group</Label>
                    <Select
                      value={selectedGroupId}
                      onValueChange={setSelectedGroupId}
                    >
                      <SelectTrigger id="permission-group">
                        <SelectValue placeholder="Select a permission group" />
                      </SelectTrigger>
                      <SelectContent>
                        {permissionGroupsData?.data?.map((group) => (
                          <SelectItem key={group.id} value={group.id}>
                            {group.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <p className="text-sm text-muted-foreground">
                      The API key will inherit all permissions from the selected group.
                    </p>
                  </div>
                </TabsContent>
              </Tabs>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="rate-limit">Rate Limit (optional)</Label>
              <div className="flex space-x-2">
                <Input
                  id="rate-limit"
                  type="number"
                  placeholder="Requests"
                  value={rateLimit || ''}
                  onChange={(e) => setRateLimit(e.target.value ? parseInt(e.target.value) : undefined)}
                  className="flex-1"
                />
                <Select
                  value={rateLimitPeriod}
                  onValueChange={setRateLimitPeriod}
                >
                  <SelectTrigger className="w-[120px]">
                    <SelectValue placeholder="Period" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="minute">Per Minute</SelectItem>
                    <SelectItem value="hour">Per Hour</SelectItem>
                    <SelectItem value="day">Per Day</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        )}
        
        <DialogFooter>
          {newApiKey ? (
            <Button onClick={handleClose}>
              Close
            </Button>
          ) : (
            <Button onClick={handleCreateApiKey} disabled={isCreatingKey || !newKeyName.trim()}>
              {isCreatingKey ? 'Creating...' : 'Create API Key'}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
