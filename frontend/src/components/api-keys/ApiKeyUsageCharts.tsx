'use client';

import { useState } from 'react';
import { 
  useGetApiKeyUsageByEndpointQuery, 
  useGetApiKeyUsageByDayQuery 
} from '@/lib/redux/api/endpoints/apiKeyApi';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';

interface ApiKeyUsageChartsProps {
  organizationId: string;
  keyId: string;
  days: number;
}

export default function ApiKeyUsageCharts({ organizationId, keyId, days }: ApiKeyUsageChartsProps) {
  const [activeTab, setActiveTab] = useState('daily');

  // Fetch usage data by endpoint
  const { 
    data: endpointData, 
    isLoading: isLoadingEndpoints,
    refetch: refetchEndpoints
  } = useGetApiKeyUsageByEndpointQuery({
    organizationId,
    keyId,
    days
  });

  // Fetch usage data by day
  const { 
    data: dailyData, 
    isLoading: isLoadingDaily,
    refetch: refetchDaily
  } = useGetApiKeyUsageByDayQuery({
    organizationId,
    keyId,
    days
  });

  // Handle refresh
  const handleRefresh = () => {
    if (activeTab === 'daily') {
      refetchDaily();
    } else {
      refetchEndpoints();
    }
  };

  // Format data for charts
  const endpointChartData = endpointData?.data?.endpoints || [];
  const dailyChartData = dailyData?.data?.daily || [];

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>API Key Usage Analytics</CardTitle>
            <CardDescription>
              Usage statistics for the last {days} days
            </CardDescription>
          </div>
          <Button 
            variant="outline" 
            size="icon" 
            onClick={handleRefresh}
            disabled={isLoadingDaily || isLoadingEndpoints}
          >
            <RefreshCw className={`h-4 w-4 ${isLoadingDaily || isLoadingEndpoints ? 'animate-spin' : ''}`} />
          </Button>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="daily">Daily Usage</TabsTrigger>
            <TabsTrigger value="endpoints">Usage by Endpoint</TabsTrigger>
          </TabsList>
          
          <TabsContent value="daily">
            {isLoadingDaily ? (
              <div className="h-[300px] flex items-center justify-center">
                <Skeleton className="h-[250px] w-full" />
              </div>
            ) : dailyChartData.length > 0 ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={dailyChartData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="date" 
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => {
                        const date = new Date(value);
                        return `${date.getMonth() + 1}/${date.getDate()}`;
                      }}
                    />
                    <YAxis />
                    <Tooltip 
                      formatter={(value: number) => [`${value} calls`, 'API Calls']}
                      labelFormatter={(label) => `Date: ${label}`}
                    />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="count" 
                      name="API Calls" 
                      stroke="#8884d8" 
                      activeDot={{ r: 8 }} 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                No usage data available for this period
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="endpoints">
            {isLoadingEndpoints ? (
              <div className="h-[300px] flex items-center justify-center">
                <Skeleton className="h-[250px] w-full" />
              </div>
            ) : endpointChartData.length > 0 ? (
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={endpointChartData}
                    margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    layout="vertical"
                  >
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis type="number" />
                    <YAxis 
                      dataKey="endpoint" 
                      type="category" 
                      width={150}
                      tick={{ fontSize: 12 }}
                    />
                    <Tooltip 
                      formatter={(value: number) => [`${value} calls`, 'API Calls']}
                      labelFormatter={(label) => `Endpoint: ${label}`}
                    />
                    <Legend />
                    <Bar 
                      dataKey="count" 
                      name="API Calls" 
                      fill="#82ca9d" 
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            ) : (
              <div className="h-[300px] flex items-center justify-center text-muted-foreground">
                No endpoint data available for this period
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
