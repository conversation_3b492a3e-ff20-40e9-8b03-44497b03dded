'use client';

import { useState } from 'react';
import { CardElement as StripeCardElement } from '@stripe/react-stripe-js';
import { Label } from '@/components/ui/label';

interface CardElementProps {
  onChange?: (event: any) => void;
  className?: string;
}

export function CardElement({ onChange, className = '' }: CardElementProps) {
  const [error, setError] = useState<string | null>(null);

  const handleChange = (event: any) => {
    if (event.error) {
      setError(event.error.message);
    } else {
      setError(null);
    }

    if (onChange) {
      onChange(event);
    }
  };

  const cardStyle = {
    style: {
      base: {
        color: '#32325d',
        fontFamily: 'Arial, sans-serif',
        fontSmoothing: 'antialiased',
        fontSize: '16px',
        '::placeholder': {
          color: '#aab7c4',
        },
      },
      invalid: {
        color: '#fa755a',
        iconColor: '#fa755a',
      },
    },
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor="card-element">Card Details</Label>
      <div className="p-3 border rounded-md">
        <StripeCardElement
          id="card-element"
          options={cardStyle}
          onChange={handleChange}
        />
      </div>
      {error && <p className="text-sm text-destructive">{error}</p>}
    </div>
  );
}
