'use client';

import { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { SubscriptionStatusIndicator } from '@/components/subscription/SubscriptionStatusIndicator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Building,
  ChevronDown,
  CreditCard,
  FolderOpen,
  Globe,
  Home,
  Languages,
  LogOut,
  Menu,
  Settings,
  User,
} from 'lucide-react';

interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
}

const navItems: NavItem[] = [
  {
    title: 'Dashboard',
    href: '/dashboard',
    icon: <Home className="h-4 w-4 mr-2" />,
  },
  {
    title: 'Organizations',
    href: '/organizations',
    icon: <Building className="h-4 w-4 mr-2" />,
  },
  {
    title: 'Projects',
    href: '/projects',
    icon: <FolderOpen className="h-4 w-4 mr-2" />,
  },
  {
    title: 'Translations',
    href: '/translations',
    icon: <Languages className="h-4 w-4 mr-2" />,
  },
  {
    title: 'Subscription',
    href: '/subscription',
    icon: <CreditCard className="h-4 w-4 mr-2" />,
  },
];

export default function MainNav() {
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const [isOpen, setIsOpen] = useState(false);

  const handleSignOut = async () => {
    await signOut({ redirect: true, callbackUrl: '/' });
  };

  // Don't render navigation if not authenticated
  if (status !== 'authenticated') {
    return null;
  }

  return (
    <nav className="border-b bg-background">
      <div className="container flex h-16 items-center px-4">
        <div className="hidden md:flex">
          <Link href="/dashboard" className="mr-6 flex items-center space-x-2">
            <Globe className="h-6 w-6" />
            <span className="font-bold">ADC Multi-Languages</span>
          </Link>
          <div className="flex gap-6">
            {navItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className={cn(
                  'flex items-center text-sm font-medium transition-colors hover:text-primary',
                  pathname === item.href || pathname.startsWith(`${item.href}/`)
                    ? 'text-foreground'
                    : 'text-muted-foreground'
                )}
              >
                {item.icon}
                {item.title}
              </Link>
            ))}
          </div>
        </div>

        <Sheet open={isOpen} onOpenChange={setIsOpen}>
          <SheetTrigger asChild className="md:hidden">
            <Button variant="ghost" size="icon" className="mr-2">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left">
            <SheetHeader className="mb-4">
              <SheetTitle className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                ADC Multi-Languages
              </SheetTitle>
            </SheetHeader>
            <div className="flex flex-col gap-3 py-4">
              {navItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={cn(
                    'flex items-center py-2 text-sm font-medium transition-colors hover:text-primary',
                    pathname === item.href || pathname.startsWith(`${item.href}/`)
                      ? 'text-foreground'
                      : 'text-muted-foreground'
                  )}
                  onClick={() => setIsOpen(false)}
                >
                  {item.icon}
                  {item.title}
                </Link>
              ))}
            </div>
          </SheetContent>
        </Sheet>

        <div className="flex-1 md:hidden">
          <Link href="/dashboard" className="flex items-center space-x-2">
            <span className="font-bold">ADC Multi-Languages</span>
          </Link>
        </div>

        <div className="ml-auto flex items-center space-x-4">
          {/* Subscription Status Indicator */}
          <Link href="/subscription" className="hidden md:flex items-center">
            <SubscriptionStatusIndicator />
          </Link>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-8 flex items-center gap-1">
                <User className="h-4 w-4" />
                <span className="hidden md:inline-block">
                  {session.user.name || session.user.email}
                </span>
                <ChevronDown className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link href="/profile">
                  <User className="h-4 w-4 mr-2" />
                  Profile
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/settings">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link href="/subscription">
                  <CreditCard className="h-4 w-4 mr-2" />
                  Subscription
                </Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <LogOut className="h-4 w-4 mr-2" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </nav>
  );
}
