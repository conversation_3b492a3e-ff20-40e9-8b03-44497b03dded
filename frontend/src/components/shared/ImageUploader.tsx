'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Loader2, Upload } from 'lucide-react';

interface ImageUploaderProps {
  onImageUploaded: (imageUrl: string) => void;
  className?: string;
}

export function ImageUploader({ onImageUploaded, className }: ImageUploaderProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      setFile(selectedFile);
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewUrl(reader.result as string);
      };
      reader.readAsDataURL(selectedFile);
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    try {
      setIsUploading(true);

      // Step 1: Get a signed URL for uploading
      const response = await fetch('/api/storage/upload-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fileName: file.name,
          contentType: file.type,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to get upload URL');
      }

      const { uploadUrl, fileUrl } = await response.json();

      // Step 2: Upload the file to the signed URL
      // In a real implementation, you would upload the file to the signed URL
      // For this mock implementation, we'll just use the fileUrl directly
      
      // Simulate upload delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Step 3: Return the public URL of the uploaded file
      onImageUploaded(fileUrl);
      
      // Clear the file input
      setFile(null);
      setPreviewUrl(null);
    } catch (error) {
      console.error('Error uploading image:', error);
      alert('Failed to upload image. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="space-y-2">
        <Label htmlFor="image">Screenshot</Label>
        <Input
          id="image"
          type="file"
          accept="image/*"
          onChange={handleFileChange}
          disabled={isUploading}
        />
        <p className="text-xs text-muted-foreground">
          Upload a screenshot to help translators understand where this text appears.
        </p>
      </div>

      {previewUrl && (
        <div className="mt-4 border rounded-md overflow-hidden">
          <img 
            src={previewUrl} 
            alt="Preview" 
            className="w-full h-auto max-h-[200px] object-contain"
          />
        </div>
      )}

      {file && (
        <Button
          type="button"
          onClick={handleUpload}
          disabled={isUploading}
          className="mt-2"
        >
          {isUploading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Uploading...
            </>
          ) : (
            <>
              <Upload className="mr-2 h-4 w-4" />
              Upload Screenshot
            </>
          )}
        </Button>
      )}
    </div>
  );
}
