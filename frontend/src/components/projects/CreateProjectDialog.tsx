'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useCreateProjectMutation } from '@/lib/redux/api/endpoints/projectApi';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';

// Form validation schema
const createProjectSchema = z.object({
  name: z.string().min(3, { message: 'Project name must be at least 3 characters' }),
  slug: z.string().min(3, { message: 'Slug must be at least 3 characters' })
    .regex(/^[a-z0-9-]+$/, { message: 'Slug can only contain lowercase letters, numbers, and hyphens' }),
  description: z.string().optional(),
  default_locale: z.string().min(2, { message: 'Default locale must be at least 2 characters' }).optional(),
  is_public: z.boolean().optional(),
});

type CreateProjectFormValues = z.infer<typeof createProjectSchema>;

interface CreateProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  organizationId: string;
}

export default function CreateProjectDialog({
  open,
  onOpenChange,
  organizationId,
}: CreateProjectDialogProps) {
  const router = useRouter();
  const { toast } = useToast();
  const [createProject, { isLoading }] = useCreateProjectMutation();

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    watch,
    setValue,
  } = useForm<CreateProjectFormValues>({
    resolver: zodResolver(createProjectSchema),
    defaultValues: {
      name: '',
      slug: '',
      description: '',
      default_locale: 'en',
      is_public: false,
    },
  });

  // Watch the name field to auto-generate slug
  const name = watch('name');

  // Auto-generate slug from name
  const handleNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const nameValue = e.target.value;
    const slugValue = nameValue
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^a-z0-9-]/g, '');

    setValue('name', nameValue);
    setValue('slug', slugValue);
  };

  const onSubmit = async (data: CreateProjectFormValues) => {
    if (!organizationId) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No organization selected. Please select an organization first.',
      });
      return;
    }

    try {
      const result = await createProject({
        organization_id: organizationId,
        name: data.name,
        slug: data.slug,
        description: data.description,
        default_locale: data.default_locale,
        is_public: data.is_public,
      }).unwrap();

      if (result.success) {
        toast({
          variant: 'success',
          title: 'Project Created',
          description: `Project "${data.name}" has been created successfully.`,
        });

        reset();
        onOpenChange(false);

        // Navigate to the new project using slug
        router.push(`/projects/${result.data.slug}`);
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.message || 'Failed to create project. Please try again.',
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.data?.message || 'An unexpected error occurred. Please try again.',
      });
    }
  };

  const handleDialogClose = () => {
    if (!isLoading) {
      reset();
      onOpenChange(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create New Project</DialogTitle>
          <DialogDescription>
            Create a new translation project to manage your content across multiple languages.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Project Name</Label>
              <Input
                id="name"
                {...register('name')}
                onChange={handleNameChange}
                placeholder="My Translation Project"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="slug">Project Slug</Label>
              <Input
                id="slug"
                {...register('slug')}
                placeholder="my-translation-project"
              />
              {errors.slug && (
                <p className="text-sm text-destructive">{errors.slug.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                The slug is used in URLs and API endpoints. Use only lowercase letters, numbers, and hyphens.
              </p>
            </div>

            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="A brief description of your project"
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
            </div>

            <div className="grid gap-2">
              <Label htmlFor="default_locale">Default Locale</Label>
              <Input
                id="default_locale"
                {...register('default_locale')}
                placeholder="en"
              />
              {errors.default_locale && (
                <p className="text-sm text-destructive">{errors.default_locale.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                The default language for your project (e.g., en, fr, es).
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_public"
                {...register('is_public')}
              />
              <Label htmlFor="is_public">Make this project public</Label>
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleDialogClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Project'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
