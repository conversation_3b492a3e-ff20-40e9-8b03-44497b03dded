'use client';

import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useUpdateProjectMutation } from '@/lib/redux/api/endpoints/projectApi';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { Project } from '@/lib/redux/api/endpoints/projectApi';

// Form validation schema
const editProjectSchema = z.object({
  name: z.string().min(3, { message: 'Project name must be at least 3 characters' }),
  description: z.string().optional(),
  default_locale: z.string().min(2, { message: 'Default locale must be at least 2 characters' }).optional(),
  is_public: z.boolean().optional(),
});

type EditProjectFormValues = z.infer<typeof editProjectSchema>;

interface EditProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  project?: Project;
  onSuccess?: () => void;
}

export default function EditProjectDialog({ 
  open, 
  onOpenChange,
  project,
  onSuccess,
}: EditProjectDialogProps) {
  const { toast } = useToast();
  const [updateProject, { isLoading }] = useUpdateProjectMutation();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<EditProjectFormValues>({
    resolver: zodResolver(editProjectSchema),
    defaultValues: {
      name: project?.name || '',
      description: project?.description || '',
      default_locale: project?.default_locale || '',
      is_public: project?.is_public || false,
    },
  });
  
  // Watch form values
  const isPublic = watch('is_public');
  
  // Handle checkbox change
  const handleIsPublicChange = (checked: boolean) => {
    setValue('is_public', checked);
  };
  
  // Update form values when project changes
  useEffect(() => {
    if (project && open) {
      reset({
        name: project.name,
        description: project.description || '',
        default_locale: project.default_locale || '',
        is_public: project.is_public || false,
      });
    }
  }, [project, open, reset]);
  
  const onSubmit = async (data: EditProjectFormValues) => {
    if (!project?.id) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No project selected. Please select a project first.',
      });
      return;
    }
    
    try {
      const result = await updateProject({
        id: project.id,
        data: {
          name: data.name,
          description: data.description,
          default_locale: data.default_locale,
          is_public: data.is_public,
        },
      }).unwrap();
      
      if (result.success) {
        toast({
          variant: 'success',
          title: 'Project Updated',
          description: 'The project has been updated successfully.',
        });
        
        onOpenChange(false);
        
        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.message || 'Failed to update project. Please try again.',
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.data?.message || 'An unexpected error occurred. Please try again.',
      });
    }
  };
  
  const handleDialogClose = () => {
    if (!isLoading) {
      onOpenChange(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Edit Project</DialogTitle>
          <DialogDescription>
            Update your project details.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Project Name</Label>
              <Input
                id="name"
                {...register('name')}
                placeholder="My Translation Project"
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                {...register('description')}
                placeholder="A brief description of your project"
                rows={3}
              />
              {errors.description && (
                <p className="text-sm text-destructive">{errors.description.message}</p>
              )}
            </div>
            
            <div className="grid gap-2">
              <Label htmlFor="default_locale">Default Locale</Label>
              <Input
                id="default_locale"
                {...register('default_locale')}
                placeholder="en"
              />
              {errors.default_locale && (
                <p className="text-sm text-destructive">{errors.default_locale.message}</p>
              )}
              <p className="text-xs text-muted-foreground">
                The default language for your project (e.g., en, fr, es).
              </p>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_public"
                checked={isPublic}
                onCheckedChange={handleIsPublicChange}
              />
              <Label htmlFor="is_public">Make this project public</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleDialogClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Project'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
