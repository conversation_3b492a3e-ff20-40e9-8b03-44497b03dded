'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  useAddProjectLocaleMutation,
  useListLocalesQuery,
} from '@/lib/redux/api/endpoints';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';
import { Loader2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Form validation schema
const addLocaleSchema = z.object({
  locale_id: z.string().uuid({ message: 'Please select a locale' }),
  is_source: z.boolean().optional(),
});

type AddLocaleFormValues = z.infer<typeof addLocaleSchema>;

interface AddLocaleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  onSuccess?: () => void;
}

export default function AddLocaleDialog({ 
  open, 
  onOpenChange,
  projectId,
  onSuccess,
}: AddLocaleDialogProps) {
  const { toast } = useToast();
  const [addProjectLocale, { isLoading }] = useAddProjectLocaleMutation();
  
  // Fetch available locales
  const { 
    data: localesData, 
    isLoading: isLoadingLocales,
    error: localesError,
  } = useListLocalesQuery();
  
  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
    setValue,
    watch,
  } = useForm<AddLocaleFormValues>({
    resolver: zodResolver(addLocaleSchema),
    defaultValues: {
      locale_id: '',
      is_source: false,
    },
  });
  
  // Watch form values
  const selectedLocaleId = watch('locale_id');
  const isSource = watch('is_source');
  
  // Handle locale selection
  const handleLocaleChange = (value: string) => {
    setValue('locale_id', value);
  };
  
  // Handle source checkbox change
  const handleSourceChange = (checked: boolean) => {
    setValue('is_source', checked);
  };
  
  const onSubmit = async (data: AddLocaleFormValues) => {
    if (!projectId) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No project selected. Please select a project first.',
      });
      return;
    }
    
    try {
      const result = await addProjectLocale({
        projectId,
        data: {
          locale_id: data.locale_id,
          is_source: data.is_source,
        },
      }).unwrap();
      
      if (result.success) {
        toast({
          variant: 'success',
          title: 'Locale Added',
          description: 'The locale has been added to the project successfully.',
        });
        
        reset();
        onOpenChange(false);
        
        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.message || 'Failed to add locale. Please try again.',
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.data?.message || 'An unexpected error occurred. Please try again.',
      });
    }
  };
  
  const handleDialogClose = () => {
    if (!isLoading) {
      reset();
      onOpenChange(false);
    }
  };
  
  // Reset form when dialog opens
  useEffect(() => {
    if (open) {
      reset();
    }
  }, [open, reset]);
  
  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Add Locale to Project</DialogTitle>
          <DialogDescription>
            Add a new language locale to your translation project.
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="locale_id">Select Locale</Label>
              {isLoadingLocales ? (
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span>Loading locales...</span>
                </div>
              ) : localesError ? (
                <div className="text-destructive">Failed to load locales</div>
              ) : (
                <Select value={selectedLocaleId} onValueChange={handleLocaleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a locale" />
                  </SelectTrigger>
                  <SelectContent>
                    {localesData?.data?.map((locale) => (
                      <SelectItem key={locale.id} value={locale.id}>
                        {locale.name} ({locale.code}) - {locale.native_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
              {errors.locale_id && (
                <p className="text-sm text-destructive">{errors.locale_id.message}</p>
              )}
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_source"
                checked={isSource}
                onCheckedChange={handleSourceChange}
              />
              <Label htmlFor="is_source">Set as source locale</Label>
            </div>
            <p className="text-xs text-muted-foreground">
              The source locale is the original language from which other translations are derived.
            </p>
          </div>
          
          <DialogFooter>
            <Button type="button" variant="outline" onClick={handleDialogClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading || isLoadingLocales}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Adding...
                </>
              ) : (
                'Add Locale'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
