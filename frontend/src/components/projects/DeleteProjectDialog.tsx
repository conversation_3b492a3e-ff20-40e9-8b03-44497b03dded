'use client';

import { useState } from 'react';
import { useDeleteProjectMutation } from '@/lib/redux/api/endpoints/projectApi';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, AlertTriangle } from 'lucide-react';

interface DeleteProjectDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  projectId: string;
  projectName: string;
  onSuccess?: () => void;
}

export default function DeleteProjectDialog({ 
  open, 
  onOpenChange,
  projectId,
  projectName,
  onSuccess,
}: DeleteProjectDialogProps) {
  const { toast } = useToast();
  const [deleteProject, { isLoading }] = useDeleteProjectMutation();
  const [confirmText, setConfirmText] = useState('');
  
  const handleConfirmTextChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setConfirmText(e.target.value);
  };
  
  const handleDelete = async () => {
    if (confirmText !== projectName) {
      toast({
        variant: 'destructive',
        title: 'Confirmation Failed',
        description: 'Please type the project name exactly to confirm deletion.',
      });
      return;
    }
    
    try {
      const result = await deleteProject(projectId).unwrap();
      
      if (result.success) {
        toast({
          variant: 'success',
          title: 'Project Deleted',
          description: 'The project has been deleted successfully.',
        });
        
        setConfirmText('');
        onOpenChange(false);
        
        // Call success callback
        if (onSuccess) {
          onSuccess();
        }
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.message || 'Failed to delete project. Please try again.',
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.data?.message || 'An unexpected error occurred. Please try again.',
      });
    }
  };
  
  const handleDialogClose = () => {
    if (!isLoading) {
      setConfirmText('');
      onOpenChange(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={handleDialogClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center text-destructive">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Delete Project
          </DialogTitle>
          <DialogDescription>
            This action cannot be undone. This will permanently delete the project and all associated translations.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <p>
            Please type <strong>{projectName}</strong> to confirm.
          </p>
          
          <div className="grid gap-2">
            <Label htmlFor="confirm">Confirmation</Label>
            <Input
              id="confirm"
              value={confirmText}
              onChange={handleConfirmTextChange}
              placeholder={`Type "${projectName}" to confirm`}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button type="button" variant="outline" onClick={handleDialogClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            type="button" 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={isLoading || confirmText !== projectName}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              'Delete Project'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
