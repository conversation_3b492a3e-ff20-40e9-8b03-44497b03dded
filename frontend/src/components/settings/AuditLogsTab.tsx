'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { format } from 'date-fns';
import { 
  useListAuditLogsQuery,
  AuditLogQueryParams
} from '@/lib/redux/api/endpoints/auditLogApi';
import { useGetPermissionsQuery } from '@/lib/redux/api/endpoints/apiKeyApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Loader2, Calendar as CalendarIcon, Check, X, RefreshCcw } from 'lucide-react';
import { cn } from '@/lib/utils';

export default function AuditLogsTab() {
  const params = useParams<{ organizationId: string }>();
  const organizationId = params.organizationId;

  // Pagination state
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  // Filter state
  const [filters, setFilters] = useState<AuditLogQueryParams>({
    limit: 10,
    offset: 0,
  });

  // Date range state
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isStartDateOpen, setIsStartDateOpen] = useState(false);
  const [isEndDateOpen, setIsEndDateOpen] = useState(false);

  // Queries
  const { data: auditLogsData, isLoading: isLoadingLogs, refetch } = useListAuditLogsQuery({
    organizationId,
    params: filters,
  });
  const { data: permissionsData, isLoading: isLoadingPermissions } = useGetPermissionsQuery();

  // Get total pages
  const totalLogs = auditLogsData?.data?.total || 0;
  const totalPages = Math.ceil(totalLogs / limit);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    
    setPage(newPage);
    setFilters({
      ...filters,
      offset: (newPage - 1) * limit,
    });
  };

  // Handle filter change
  const handleFilterChange = (key: keyof AuditLogQueryParams, value: any) => {
    setFilters({
      ...filters,
      [key]: value,
    });
    
    // Reset to first page when filters change
    setPage(1);
  };

  // Apply date filters
  const applyDateFilters = () => {
    const newFilters = { ...filters };
    
    if (startDate) {
      newFilters.start_date = startDate.toISOString();
    } else {
      delete newFilters.start_date;
    }
    
    if (endDate) {
      newFilters.end_date = endDate.toISOString();
    } else {
      delete newFilters.end_date;
    }
    
    setFilters(newFilters);
    setPage(1);
  };

  // Reset filters
  const resetFilters = () => {
    setFilters({
      limit,
      offset: 0,
    });
    setStartDate(undefined);
    setEndDate(undefined);
    setPage(1);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Permission Audit Logs</h2>
        <Button variant="outline" onClick={() => refetch()}>
          <RefreshCcw className="mr-2 h-4 w-4" /> Refresh
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filters</CardTitle>
          <CardDescription>Filter audit logs by various criteria</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Permission filter */}
            <div className="space-y-2">
              <Label htmlFor="permission-filter">Permission</Label>
              <Select
                value={filters.permission_key || ''}
                onValueChange={(value) => 
                  handleFilterChange('permission_key', value || undefined)
                }
              >
                <SelectTrigger id="permission-filter">
                  <SelectValue placeholder="All permissions" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All permissions</SelectItem>
                  {permissionsData?.data?.permissions?.map((permission) => (
                    <SelectItem key={permission.key} value={permission.key}>
                      {permission.key}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Status filter */}
            <div className="space-y-2">
              <Label htmlFor="status-filter">Status</Label>
              <Select
                value={filters.granted !== undefined ? filters.granted.toString() : ''}
                onValueChange={(value) => 
                  handleFilterChange('granted', value === '' ? undefined : value === 'true')
                }
              >
                <SelectTrigger id="status-filter">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All statuses</SelectItem>
                  <SelectItem value="true">Granted</SelectItem>
                  <SelectItem value="false">Denied</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Date range filter */}
            <div className="space-y-2">
              <Label>Date Range</Label>
              <div className="flex space-x-2">
                <div className="flex-1">
                  <Popover open={isStartDateOpen} onOpenChange={setIsStartDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {startDate ? format(startDate, 'PPP') : <span>Start date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={(date) => {
                          setStartDate(date);
                          setIsStartDateOpen(false);
                          if (date) applyDateFilters();
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
                <div className="flex-1">
                  <Popover open={isEndDateOpen} onOpenChange={setIsEndDateOpen}>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {endDate ? format(endDate, 'PPP') : <span>End date</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={endDate}
                        onSelect={(date) => {
                          setEndDate(date);
                          setIsEndDateOpen(false);
                          if (date) applyDateFilters();
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end mt-4">
            <Button variant="outline" onClick={resetFilters} className="mr-2">
              Reset Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Audit Logs Table */}
      {isLoadingLogs ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : auditLogsData?.data?.logs?.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">No audit logs found</p>
            </div>
          </CardContent>
        </Card>
      ) : (
        <>
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Time</TableHead>
                    <TableHead>API Key</TableHead>
                    <TableHead>Endpoint</TableHead>
                    <TableHead>Method</TableHead>
                    <TableHead>Permission</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auditLogsData?.data?.logs?.map((log) => (
                    <TableRow key={log.id}>
                      <TableCell>
                        {format(new Date(log.created_at), 'yyyy-MM-dd HH:mm:ss')}
                      </TableCell>
                      <TableCell>{log.api_key_id}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {log.endpoint}
                      </TableCell>
                      <TableCell>
                        <span
                          className={cn(
                            'px-2 py-1 rounded-full text-xs font-medium',
                            {
                              'bg-blue-100 text-blue-800': log.method === 'GET',
                              'bg-green-100 text-green-800': log.method === 'POST',
                              'bg-yellow-100 text-yellow-800': log.method === 'PUT',
                              'bg-red-100 text-red-800': log.method === 'DELETE',
                            }
                          )}
                        >
                          {log.method}
                        </span>
                      </TableCell>
                      <TableCell>{log.permission_key}</TableCell>
                      <TableCell>
                        {log.granted ? (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <Check className="mr-1 h-3 w-3" /> Granted
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <X className="mr-1 h-3 w-3" /> Denied
                          </span>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Pagination */}
          {totalPages > 1 && (
            <Pagination className="mt-4">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(page - 1)}
                    className={cn(page === 1 && 'pointer-events-none opacity-50')}
                  />
                </PaginationItem>
                
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNum;
                  
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (page <= 3) {
                    pageNum = i + 1;
                  } else if (page >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = page - 2 + i;
                  }
                  
                  return (
                    <PaginationItem key={pageNum}>
                      <PaginationLink
                        onClick={() => handlePageChange(pageNum)}
                        isActive={page === pageNum}
                      >
                        {pageNum}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}
                
                {totalPages > 5 && page < totalPages - 2 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
                
                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(page + 1)}
                    className={cn(page === totalPages && 'pointer-events-none opacity-50')}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
        </>
      )}
    </div>
  );
}
