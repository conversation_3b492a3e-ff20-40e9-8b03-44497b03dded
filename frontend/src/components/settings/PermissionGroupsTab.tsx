'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { 
  useListPermissionGroupsQuery, 
  useCreatePermissionGroupMutation,
  useUpdatePermissionGroupMutation,
  useDeletePermissionGroupMutation,
  PermissionGroup
} from '@/lib/redux/api/endpoints/permissionGroupApi';
import { useGetPermissionsQuery } from '@/lib/redux/api/endpoints/apiKeyApi';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Loader2, Plus, Trash, Edit, Check } from 'lucide-react';

export default function PermissionGroupsTab() {
  const params = useParams<{ organizationId: string }>();
  const organizationId = params.organizationId;
  const { toast } = useToast();

  // State for dialogs
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<PermissionGroup | null>(null);

  // Form state
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);

  // Queries and mutations
  const { data: permissionGroupsData, isLoading: isLoadingGroups } = useListPermissionGroupsQuery({ organizationId });
  const { data: permissionsData, isLoading: isLoadingPermissions } = useGetPermissionsQuery();
  const [createPermissionGroup, { isLoading: isCreating }] = useCreatePermissionGroupMutation();
  const [updatePermissionGroup, { isLoading: isUpdating }] = useUpdatePermissionGroupMutation();
  const [deletePermissionGroup, { isLoading: isDeleting }] = useDeletePermissionGroupMutation();

  // Group permissions by type
  const groupedPermissions = permissionsData?.data?.permissions?.reduce((acc, permission) => {
    const type = permission.type === 'global' ? 'Global' : permission.resource || 'Other';
    if (!acc[type]) {
      acc[type] = [];
    }
    acc[type].push(permission);
    return acc;
  }, {} as Record<string, any[]>) || {};

  // Reset form
  const resetForm = () => {
    setName('');
    setDescription('');
    setSelectedPermissions([]);
    setSelectedGroup(null);
  };

  // Open edit dialog
  const handleEditClick = (group: PermissionGroup) => {
    setSelectedGroup(group);
    setName(group.name);
    setDescription(group.description);
    setSelectedPermissions(group.permissions);
    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const handleDeleteClick = (group: PermissionGroup) => {
    setSelectedGroup(group);
    setIsDeleteDialogOpen(true);
  };

  // Handle create group
  const handleCreateGroup = async () => {
    if (!name) {
      toast({
        title: 'Error',
        description: 'Name is required',
        variant: 'destructive',
      });
      return;
    }

    try {
      await createPermissionGroup({
        organizationId,
        data: {
          name,
          description,
          permissions: selectedPermissions,
        },
      }).unwrap();

      toast({
        title: 'Success',
        description: 'Permission group created successfully',
      });

      resetForm();
      setIsCreateDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create permission group',
        variant: 'destructive',
      });
    }
  };

  // Handle update group
  const handleUpdateGroup = async () => {
    if (!selectedGroup) return;

    try {
      await updatePermissionGroup({
        organizationId,
        groupId: selectedGroup.id,
        data: {
          name,
          description,
          permissions: selectedPermissions,
        },
      }).unwrap();

      toast({
        title: 'Success',
        description: 'Permission group updated successfully',
      });

      resetForm();
      setIsEditDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update permission group',
        variant: 'destructive',
      });
    }
  };

  // Handle delete group
  const handleDeleteGroup = async () => {
    if (!selectedGroup) return;

    try {
      await deletePermissionGroup({
        organizationId,
        groupId: selectedGroup.id,
      }).unwrap();

      toast({
        title: 'Success',
        description: 'Permission group deleted successfully',
      });

      resetForm();
      setIsDeleteDialogOpen(false);
    } catch (error: any) {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete permission group',
        variant: 'destructive',
      });
    }
  };

  // Toggle permission selection
  const togglePermission = (permission: string) => {
    setSelectedPermissions((prev) =>
      prev.includes(permission)
        ? prev.filter((p) => p !== permission)
        : [...prev, permission]
    );
  };

  // Render permission checkboxes
  const renderPermissionCheckboxes = () => {
    return Object.entries(groupedPermissions).map(([type, permissions]) => (
      <div key={type} className="mb-6">
        <h3 className="text-lg font-semibold mb-2">{type}</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          {permissions.map((permission) => (
            <div key={permission.key} className="flex items-center space-x-2">
              <Checkbox
                id={permission.key}
                checked={selectedPermissions.includes(permission.key)}
                onCheckedChange={() => togglePermission(permission.key)}
              />
              <Label htmlFor={permission.key} className="cursor-pointer">
                {permission.key}
                <p className="text-xs text-muted-foreground">{permission.description}</p>
              </Label>
            </div>
          ))}
        </div>
      </div>
    ));
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Permission Groups</h2>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Create Group
        </Button>
      </div>

      {isLoadingGroups ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      ) : permissionGroupsData?.data?.length === 0 ? (
        <Card>
          <CardContent className="py-8">
            <div className="text-center">
              <p className="text-muted-foreground">No permission groups found</p>
              <Button
                variant="outline"
                className="mt-4"
                onClick={() => setIsCreateDialogOpen(true)}
              >
                Create your first permission group
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {permissionGroupsData?.data?.map((group) => (
            <Card key={group.id}>
              <CardHeader>
                <CardTitle>{group.name}</CardTitle>
                <CardDescription>{group.description}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-sm">
                  <strong>Permissions:</strong>
                  <div className="mt-2 flex flex-wrap gap-1">
                    {group.permissions.map((permission) => (
                      <span
                        key={permission}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEditClick(group)}
                >
                  <Edit className="h-4 w-4 mr-1" /> Edit
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleDeleteClick(group)}
                >
                  <Trash className="h-4 w-4 mr-1" /> Delete
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create Permission Group</DialogTitle>
            <DialogDescription>
              Create a new permission group to manage API key permissions.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter group name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter group description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Permissions</Label>
                {isLoadingPermissions ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  renderPermissionCheckboxes()
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                resetForm();
                setIsCreateDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleCreateGroup} disabled={isCreating}>
              {isCreating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Create Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Permission Group</DialogTitle>
            <DialogDescription>
              Update the permission group details and permissions.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Name</Label>
                <Input
                  id="edit-name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="Enter group name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter group description"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>Permissions</Label>
                {isLoadingPermissions ? (
                  <div className="flex justify-center py-4">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  renderPermissionCheckboxes()
                )}
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                resetForm();
                setIsEditDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button onClick={handleUpdateGroup} disabled={isUpdating}>
              {isUpdating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Update Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Permission Group</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this permission group? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                resetForm();
                setIsDeleteDialogOpen(false);
              }}
            >
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteGroup} disabled={isDeleting}>
              {isDeleting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Delete Group
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
