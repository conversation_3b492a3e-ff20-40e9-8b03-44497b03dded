'use client';

import { ReactNode, useState, useEffect } from 'react';
import { loadStripe, Stripe } from '@stripe/stripe-js';
import { Elements } from '@stripe/react-stripe-js';

interface StripeProviderProps {
  children: ReactNode;
}

// Initialize Stripe outside of the component to avoid recreating it on each render
let stripePromise: Promise<Stripe | null> | null = null;

export function StripeProvider({ children }: StripeProviderProps) {
  const [stripeKey, setStripeKey] = useState<string | null>(null);

  useEffect(() => {
    // Get the publishable key from environment variables
    const key = process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY;
    if (key) {
      setStripeKey(key);
      if (!stripePromise) {
        stripePromise = loadStripe(key);
      }
    }
  }, []);

  if (!stripeKey || !stripePromise) {
    // You could return a loading state here if needed
    return <>{children}</>;
  }

  return (
    <Elements stripe={stripePromise}>
      {children}
    </Elements>
  );
}
