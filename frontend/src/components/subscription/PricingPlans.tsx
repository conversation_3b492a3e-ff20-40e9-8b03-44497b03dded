'use client';

import { useState } from 'react';
import { PricingCard, PricingPlan } from './PricingCard';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

interface PricingPlansProps {
  plans: PricingPlan[];
  onSelectPlan: (planId: string, isYearly: boolean) => void;
  isLoading?: boolean;
  currentPlan?: string;
  organizationId?: string;
  useStripeCheckout?: boolean;
  initialIsYearly?: boolean;
}

export function PricingPlans({
  plans,
  onSelectPlan,
  isLoading = false,
  currentPlan,
  organizationId,
  useStripeCheckout = false,
  initialIsYearly = true
}: PricingPlansProps) {
  const [isYearly, setIsYearly] = useState(initialIsYearly);

  const handleSelectPlan = (planId: string) => {
    onSelectPlan(planId, isYearly);
  };

  return (
    <div className="space-y-8">
      <div className="flex justify-center items-center space-x-4">
        <Label htmlFor="billing-toggle" className={isYearly ? 'text-muted-foreground' : ''}>
          Monthly
        </Label>
        <Switch
          id="billing-toggle"
          checked={isYearly}
          onCheckedChange={setIsYearly}
        />
        <div className="flex items-center">
          <Label htmlFor="billing-toggle" className={!isYearly ? 'text-muted-foreground' : ''}>
            Yearly
          </Label>
          <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
            Save 20%
          </span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {plans.map((plan) => (
          <PricingCard
            key={plan.id}
            plan={plan}
            isYearly={isYearly}
            onSelectPlan={handleSelectPlan}
            isLoading={isLoading}
            currentPlan={currentPlan}
            organizationId={organizationId}
            useStripeCheckout={useStripeCheckout}
          />
        ))}
      </div>
    </div>
  );
}
