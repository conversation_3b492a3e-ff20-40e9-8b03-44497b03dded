'use client';

import { useState } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { Button } from '@/components/ui/button';
import { CardElement as CustomCardElement } from '../payment/CardElement';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useListOrganizationPaymentMethodsQuery } from '@/lib/redux/api/endpoints';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface SubscriptionCheckoutProps {
  organizationId: string;
  planId: string;
  isYearly: boolean;
  onSuccess?: (paymentMethodId: string) => void;
  onCancel?: () => void;
}

export function SubscriptionCheckout({
  organizationId,
  planId,
  isYearly,
  onSuccess,
  onCancel
}: SubscriptionCheckoutProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentMethod, setPaymentMethod] = useState<'existing' | 'new'>('existing');
  const [selectedPaymentMethodId, setSelectedPaymentMethodId] = useState<string>('');
  const [isCardComplete, setIsCardComplete] = useState(false);

  const stripe = useStripe();
  const elements = useElements();
  const { toast } = useToast();

  // Fetch existing payment methods
  const { data: paymentMethodsData, isLoading: isLoadingPaymentMethods } =
    useListOrganizationPaymentMethodsQuery(organizationId);

  const paymentMethods = paymentMethodsData?.data || [];
  const hasPaymentMethods = paymentMethods.length > 0;

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!stripe) {
      return;
    }

    setIsProcessing(true);

    try {
      let paymentMethodId = selectedPaymentMethodId;

      // If using a new card, create a payment method
      if (paymentMethod === 'new') {
        if (!elements) {
          throw new Error('Stripe Elements not initialized');
        }

        const cardElement = elements.getElement(CardElement);
        if (!cardElement) {
          throw new Error('Card Element not found');
        }

        const { error, paymentMethod: newPaymentMethod } = await stripe.createPaymentMethod({
          type: 'card',
          card: cardElement,
        });

        if (error) {
          throw new Error(error.message);
        }

        if (!newPaymentMethod) {
          throw new Error('Failed to create payment method');
        }

        paymentMethodId = newPaymentMethod.id;
      }

      // Call the onSuccess callback with the payment method ID
      if (onSuccess) {
        onSuccess(paymentMethodId);
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to create subscription',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Payment Method</h3>

        <RadioGroup
          value={paymentMethod}
          onValueChange={(value) => setPaymentMethod(value as 'existing' | 'new')}
          className="space-y-4"
        >
          {hasPaymentMethods && (
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="existing" id="existing" />
              <Label htmlFor="existing">Use existing payment method</Label>
            </div>
          )}

          <div className="flex items-center space-x-2">
            <RadioGroupItem value="new" id="new" />
            <Label htmlFor="new">Use new payment method</Label>
          </div>
        </RadioGroup>

        {paymentMethod === 'existing' && hasPaymentMethods && (
          <div className="space-y-2">
            <Label htmlFor="payment-method-select">Select Payment Method</Label>
            <Select
              value={selectedPaymentMethodId}
              onValueChange={setSelectedPaymentMethodId}
            >
              <SelectTrigger id="payment-method-select">
                <SelectValue placeholder="Select a payment method" />
              </SelectTrigger>
              <SelectContent>
                {paymentMethods.map((method) => (
                  <SelectItem key={method.id} value={method.stripe_payment_method_id}>
                    {method.card_brand} •••• {method.card_last4}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}

        {paymentMethod === 'new' && (
          <CustomCardElement onChange={(e) => setIsCardComplete(e.complete)} />
        )}
      </div>

      <div className="flex justify-end space-x-2">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
        )}

        <Button
          type="submit"
          disabled={
            isProcessing ||
            !stripe ||
            (paymentMethod === 'existing' && !selectedPaymentMethodId) ||
            (paymentMethod === 'new' && (!elements || !isCardComplete))
          }
        >
          {isProcessing ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Processing...
            </>
          ) : (
            'Subscribe Now'
          )}
        </Button>
      </div>
    </form>
  );
}
