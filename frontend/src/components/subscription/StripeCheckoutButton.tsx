'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, CreditCard } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from '@/components/ui/tooltip';

interface StripeCheckoutButtonProps {
  priceId: string;
  organizationId: string;
  isYearly: boolean;
  buttonText?: string;
  variant?: 'default' | 'outline' | 'secondary' | 'destructive' | 'ghost' | 'link';
}

export function StripeCheckoutButton({
  priceId,
  organizationId,
  isYearly,
  buttonText = 'Subscribe',
  variant = 'default'
}: StripeCheckoutButtonProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handleClick = async () => {
    setIsLoading(true);
    console.log('StripeCheckoutButton: handleClick called');

    // Validate inputs
    if (!priceId) {
      console.error('StripeCheckoutButton: No price ID provided');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No price ID provided. Please contact support.',
      });
      setIsLoading(false);
      return;
    }

    if (!organizationId) {
      console.error('StripeCheckoutButton: No organization ID provided');
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No organization selected. Please create or select an organization first.',
      });
      setIsLoading(false);
      return;
    }

    try {
      console.log('StripeCheckoutButton: Creating checkout session with:', {
        priceId,
        organizationId,
        successUrl: `${window.location.origin}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${window.location.origin}/subscription`,
      });

      // Create a checkout session
      const response = await fetch('/api/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          organizationId,
          successUrl: `${window.location.origin}/subscription/success?session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${window.location.origin}/subscription`,
        }),
      });

      console.log('StripeCheckoutButton: Response status:', response.status);

      const responseData = await response.json();
      console.log('StripeCheckoutButton: Response data:', responseData);

      if (!response.ok) {
        throw new Error(responseData.error || 'Failed to create checkout session');
      }

      const { url } = responseData;

      if (!url) {
        throw new Error('No checkout URL returned from server');
      }

      console.log('StripeCheckoutButton: Redirecting to:', url);

      // Redirect to Stripe Checkout
      window.location.href = url;
    } catch (error: any) {
      console.error('StripeCheckoutButton: Error creating checkout session:', error);
      console.error('StripeCheckoutButton: Error details:', error.stack);

      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.message || 'Failed to create checkout session. Please try again later.',
      });
      setIsLoading(false);
    }
  };

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            onClick={handleClick}
            disabled={isLoading}
            variant={variant}
            className="flex items-center"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="mr-2 h-4 w-4" />
                {buttonText}
              </>
            )}
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-2 text-amber-500" />
            <p className="text-xs">You'll be redirected to Stripe to complete your payment securely.</p>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
