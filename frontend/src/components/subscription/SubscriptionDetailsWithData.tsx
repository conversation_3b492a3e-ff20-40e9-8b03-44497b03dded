'use client';

import { useEffect } from 'react';
import { SubscriptionDetails } from '@/components/subscription/SubscriptionDetails';
import { useGetAICreditUsageQuery } from '@/lib/redux/api/endpoints/subscriptionApi';
import { ApiResponse } from '@/lib/redux/api/endpoints/types';
import { Subscription } from '@/lib/redux/api/endpoints/subscriptionApi';

interface SubscriptionDetailsWithDataProps {
  organizationId: string;
  subscriptionData?: ApiResponse<Subscription>;
  isLoadingSubscription: boolean;
  refetchSubscription: () => void;
}

export function SubscriptionDetailsWithData({
  organizationId,
  subscriptionData,
  isLoadingSubscription,
  refetchSubscription,
}: SubscriptionDetailsWithDataProps) {
  // Fetch AI credit usage data
  const {
    data: aiCreditData,
    isLoading: isLoadingAICredits,
    refetch: refetchAICredits,
  } = useGetAICreditUsageQuery(organizationId, {
    skip: !organizationId,
    // Refresh every 5 minutes
    pollingInterval: 5 * 60 * 1000,
  });

  // Handle refresh for both subscription and AI credits
  const handleRefresh = async () => {
    refetchSubscription();
    refetchAICredits();
  };

  // Log data for debugging
  useEffect(() => {
    if (aiCreditData) {
      console.log('AI Credit Usage Data:', aiCreditData);
    }
  }, [aiCreditData]);

  return (
    <SubscriptionDetails
      subscriptionData={subscriptionData}
      isLoading={isLoadingSubscription || isLoadingAICredits}
      onRefresh={handleRefresh}
      onManage={() => {
        // Scroll to pricing plans section
        document.getElementById('pricing-plans')?.scrollIntoView({ behavior: 'smooth' });
      }}
      aiCredits={
        aiCreditData?.data
          ? {
              total: aiCreditData.data.total,
              used: aiCreditData.data.used,
              resetDate: aiCreditData.data.reset_date,
            }
          : undefined
      }
    />
  );
}
