'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { Separator } from '@/components/ui/separator';
import { format, addMonths, isBefore } from 'date-fns';
import { AlertCircle, CalendarIcon, CheckCircle2, CreditCard, RefreshCw, Zap } from 'lucide-react';
import { Subscription } from '@/lib/redux/api/endpoints/subscriptionApi';
import { ApiResponse } from '@/lib/redux/api/endpoints/types';

interface SubscriptionDetailsProps {
  subscriptionData?: ApiResponse<Subscription>;
  isLoading: boolean;
  onRefresh: () => void;
  onManage: () => void;
  aiCredits?: {
    used: number;
    total: number;
    resetDate: string;
  };
}

export function SubscriptionDetails({
  subscriptionData,
  isLoading,
  onRefresh,
  onManage,
  aiCredits,
}: SubscriptionDetailsProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await onRefresh();
    setIsRefreshing(false);
  };

  // Format date
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  // Calculate days until renewal
  const getDaysUntilRenewal = (dateString?: string) => {
    if (!dateString) return 0;
    
    const endDate = new Date(dateString);
    const today = new Date();
    
    const diffTime = endDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Calculate days until AI credits reset
  const getDaysUntilReset = (dateString?: string) => {
    if (!dateString) return 0;
    
    const resetDate = new Date(dateString);
    const today = new Date();
    
    const diffTime = resetDate.getTime() - today.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Get badge variant based on status
  const getBadgeVariant = (status?: string) => {
    if (!status) return 'outline';
    
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'trialing':
        return 'warning';
      case 'past_due':
        return 'destructive';
      case 'canceled':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Check if subscription is expiring soon (within 7 days)
  const isExpiringSoon = (dateString?: string) => {
    if (!dateString) return false;
    
    const endDate = new Date(dateString);
    const today = new Date();
    const sevenDaysFromNow = addMonths(today, 0);
    sevenDaysFromNow.setDate(today.getDate() + 7);
    
    return isBefore(endDate, sevenDaysFromNow) && isBefore(today, endDate);
  };

  // Calculate AI credits usage percentage
  const getAiCreditsUsagePercentage = () => {
    if (!aiCredits || aiCredits.total === 0) return 0;
    return Math.min(100, Math.round((aiCredits.used / aiCredits.total) * 100));
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-1/3 mb-2" />
          <Skeleton className="h-4 w-1/2" />
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-2">
            <Skeleton className="h-5 w-1/4" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-5 w-1/4" />
            <Skeleton className="h-10 w-full" />
          </div>
          <Skeleton className="h-20 w-full" />
        </CardContent>
      </Card>
    );
  }

  const subscription = subscriptionData?.data;
  const daysUntilRenewal = subscription ? getDaysUntilRenewal(subscription.current_period_end) : 0;
  const aiCreditsUsagePercentage = getAiCreditsUsagePercentage();
  const daysUntilReset = aiCredits ? getDaysUntilReset(aiCredits.resetDate) : 0;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Subscription Details</CardTitle>
          <CardDescription>Your current subscription plan and usage</CardDescription>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Badge variant={getBadgeVariant(subscription?.status)}>
            {subscription?.status || 'Unknown'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {subscription ? (
          <>
            <div className="bg-muted rounded-lg p-4">
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold">{subscription.plan?.name || 'Standard'} Plan</h3>
                <span className="text-sm font-medium">
                  {subscription.plan?.price ? `$${subscription.plan.price}/${subscription.plan.interval || 'month'}` : ''}
                </span>
              </div>
              <p className="text-sm text-muted-foreground mb-4">
                {subscription.plan?.description || 'Your current subscription plan'}
              </p>
              
              <div className="flex items-center text-sm">
                <CalendarIcon className="h-4 w-4 mr-2 text-muted-foreground" />
                <span>
                  Renews on {formatDate(subscription.current_period_end)}
                  {daysUntilRenewal > 0 && ` (${daysUntilRenewal} days)`}
                </span>
              </div>
              
              {isExpiringSoon(subscription.current_period_end) && (
                <div className="mt-3 flex items-center text-sm text-amber-600 dark:text-amber-500">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span>Your subscription will renew soon</span>
                </div>
              )}
              
              {subscription.cancel_at_period_end && (
                <div className="mt-3 flex items-center text-sm text-destructive">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <span>Your subscription will be canceled at the end of the current period</span>
                </div>
              )}
            </div>
            
            {aiCredits && (
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <h3 className="text-sm font-medium flex items-center">
                    <Zap className="h-4 w-4 mr-2 text-amber-500" />
                    AI Translation Credits
                  </h3>
                  <span className="text-sm">
                    {aiCredits.used} / {aiCredits.total} used
                  </span>
                </div>
                <Progress value={aiCreditsUsagePercentage} className="h-2" />
                <div className="text-xs text-muted-foreground">
                  Credits reset on {formatDate(aiCredits.resetDate)}
                  {daysUntilReset > 0 && ` (${daysUntilReset} days)`}
                </div>
              </div>
            )}
            
            <div className="space-y-3">
              <h3 className="text-sm font-medium">Plan Features</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {subscription.plan?.features?.map((feature, index) => (
                  <div key={index} className="flex items-center text-sm">
                    <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                    <span>{feature}</span>
                  </div>
                )) || (
                  <>
                    <div className="flex items-center text-sm">
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      <span>Multiple projects</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      <span>AI-powered translations</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      <span>Export translations</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <CheckCircle2 className="h-4 w-4 mr-2 text-green-500" />
                      <span>Team collaboration</span>
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <CreditCard className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-medium mb-2">No Active Subscription</h3>
            <p className="text-sm text-muted-foreground mb-4">
              You don't have an active subscription. Choose a plan to get started.
            </p>
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button onClick={onManage} className="w-full">
          {subscription ? 'Manage Subscription' : 'Choose a Plan'}
        </Button>
      </CardFooter>
    </Card>
  );
}
