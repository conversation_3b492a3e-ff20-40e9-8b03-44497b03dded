'use client';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Check } from 'lucide-react';
import { StripeCheckoutButton } from './StripeCheckoutButton';

export interface PricingFeature {
  name: string;
  included: boolean;
}

export interface PricingPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  priceYearly: number;
  features: PricingFeature[];
  isPopular?: boolean;
  stripePriceId?: string;
  stripePriceIdYearly?: string;
}

interface PricingCardProps {
  plan: PricingPlan;
  isYearly: boolean;
  onSelectPlan: (planId: string) => void;
  isLoading?: boolean;
  currentPlan?: string;
  organizationId?: string;
  useStripeCheckout?: boolean;
}

export function PricingCard({
  plan,
  isYearly,
  onSelectPlan,
  isLoading = false,
  currentPlan,
  organizationId,
  useStripeCheckout = false
}: PricingCardProps) {
  const isCurrentPlan = currentPlan === plan.id;
  const price = isYearly ? plan.priceYearly : plan.price;

  return (
    <Card className={`flex flex-col ${plan.isPopular ? 'border-primary shadow-lg' : ''}`}>
      {plan.isPopular && (
        <div className="absolute top-0 right-0 -mt-2 -mr-2 px-3 py-1 bg-primary text-primary-foreground text-xs font-medium rounded-full">
          Popular
        </div>
      )}

      <CardHeader>
        <CardTitle>{plan.name}</CardTitle>
        <CardDescription>{plan.description}</CardDescription>
      </CardHeader>

      <CardContent className="flex-grow">
        <div className="mb-6">
          <p className="text-3xl font-bold">${price}<span className="text-sm font-normal text-muted-foreground">/{isYearly ? 'year' : 'month'}</span></p>
          {isYearly && (
            <p className="text-sm text-muted-foreground">
              Save ${(plan.price * 12 - plan.priceYearly).toFixed(2)} per year
            </p>
          )}
        </div>

        <ul className="space-y-2">
          {plan.features.map((feature, index) => (
            <li key={index} className="flex items-center">
              {feature.included ? (
                <Check className="h-4 w-4 mr-2 text-green-500" />
              ) : (
                <span className="h-4 w-4 mr-2" />
              )}
              <span className={feature.included ? '' : 'text-muted-foreground line-through'}>
                {feature.name}
              </span>
            </li>
          ))}
        </ul>
      </CardContent>

      <CardFooter>
        {useStripeCheckout && organizationId && (isYearly ? plan.stripePriceIdYearly : plan.stripePriceId) ? (
          <StripeCheckoutButton
            priceId={isYearly ? plan.stripePriceIdYearly! : plan.stripePriceId!}
            organizationId={organizationId}
            isYearly={isYearly}
            buttonText={isCurrentPlan ? 'Current Plan' : 'Subscribe Now'}
            variant={plan.isPopular ? 'default' : 'outline'}
          />
        ) : (
          <Button
            className="w-full"
            variant={plan.isPopular ? 'default' : 'outline'}
            onClick={() => onSelectPlan(plan.id)}
            disabled={isLoading || isCurrentPlan}
          >
            {isCurrentPlan ? 'Current Plan' : 'Select Plan'}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
