'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { format } from 'date-fns';
import { Download, RefreshCw } from 'lucide-react';

interface SubscriptionEvent {
  id: string;
  event_type: 'subscription_created' | 'subscription_updated' | 'subscription_canceled' | 'payment_succeeded' | 'payment_failed';
  created_at: string;
  details: {
    plan_name?: string;
    amount?: number;
    currency?: string;
    status?: string;
    period_start?: string;
    period_end?: string;
  };
}

interface SubscriptionHistoryProps {
  organizationId: string;
  isLoading?: boolean;
  events?: SubscriptionEvent[];
  onRefresh?: () => void;
}

export function SubscriptionHistory({
  organizationId,
  isLoading = false,
  events = [],
  onRefresh,
}: SubscriptionHistoryProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (onRefresh) {
      setIsRefreshing(true);
      await onRefresh();
      setIsRefreshing(false);
    }
  };

  // Format currency amount
  const formatAmount = (amount?: number, currency?: string) => {
    if (amount === undefined) return '-';
    
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    });
    
    return formatter.format(amount / 100); // Assuming amount is in cents
  };

  // Get badge variant based on event type
  const getBadgeVariant = (eventType: string) => {
    switch (eventType) {
      case 'subscription_created':
        return 'success';
      case 'subscription_updated':
        return 'secondary';
      case 'subscription_canceled':
        return 'destructive';
      case 'payment_succeeded':
        return 'success';
      case 'payment_failed':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  // Format event type for display
  const formatEventType = (eventType: string) => {
    return eventType
      .replace(/_/g, ' ')
      .replace(/\b\w/g, (char) => char.toUpperCase());
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Subscription History</CardTitle>
          <CardDescription>Recent subscription events and billing history</CardDescription>
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isLoading || isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </div>
        ) : events.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <p>No subscription history available.</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Event</TableHead>
                  <TableHead>Plan</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {events.map((event) => (
                  <TableRow key={event.id}>
                    <TableCell>
                      {format(new Date(event.created_at), 'MMM d, yyyy')}
                    </TableCell>
                    <TableCell>
                      <Badge variant={getBadgeVariant(event.event_type)}>
                        {formatEventType(event.event_type)}
                      </Badge>
                    </TableCell>
                    <TableCell>{event.details.plan_name || '-'}</TableCell>
                    <TableCell>
                      {formatAmount(event.details.amount, event.details.currency)}
                    </TableCell>
                    <TableCell className="text-right">
                      {event.event_type === 'payment_succeeded' && (
                        <Button variant="ghost" size="sm">
                          <Download className="h-4 w-4 mr-2" />
                          Invoice
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
