'use client';

import { useEffect } from 'react';
import { SubscriptionHistory } from '@/components/subscription/SubscriptionHistory';
import { useGetSubscriptionHistoryQuery } from '@/lib/redux/api/endpoints/subscriptionApi';

interface SubscriptionHistoryWithDataProps {
  organizationId: string;
  limit?: number;
}

export function SubscriptionHistoryWithData({
  organizationId,
  limit = 10,
}: SubscriptionHistoryWithDataProps) {
  // Fetch subscription history data
  const {
    data: historyData,
    isLoading,
    refetch,
  } = useGetSubscriptionHistoryQuery(
    { organizationId, limit },
    {
      skip: !organizationId,
      // Refresh every 5 minutes
      pollingInterval: 5 * 60 * 1000,
    }
  );

  // Log data for debugging
  useEffect(() => {
    if (historyData) {
      console.log('Subscription History Data:', historyData);
    }
  }, [historyData]);

  return (
    <SubscriptionHistory
      organizationId={organizationId}
      isLoading={isLoading}
      events={historyData?.data?.events || []}
      onRefresh={refetch}
    />
  );
}
