'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useGetOrganizationSubscriptionQuery } from '@/lib/redux/api/endpoints/subscriptionApi';
import { useListOrganizationsQuery } from '@/lib/redux/api/endpoints';
import { CalendarIcon, CheckCircle, AlertCircle, Clock } from 'lucide-react';
import { format } from 'date-fns';

export function SubscriptionStatusIndicator() {
  const [tooltipOpen, setTooltipOpen] = useState(false);

  // Fetch organizations
  const { data: organizationsData } = useListOrganizationsQuery();

  // Get the first organization ID (if available)
  const organizationId = organizationsData?.data?.[0]?.id;

  // Fetch current subscription if organization ID is available
  const {
    data: subscriptionData,
    isLoading: isLoadingSubscription,
  } = useGetOrganizationSubscriptionQuery(organizationId || '', {
    skip: !organizationId,
    // Refresh every 5 minutes
    pollingInterval: 5 * 60 * 1000,
  });

  // Get subscription status and plan name
  const status = subscriptionData?.data?.status;
  const planName = subscriptionData?.data?.plan?.name;
  const currentPeriodEnd = subscriptionData?.data?.current_period_end;
  
  // Format the expiration date
  const formattedExpirationDate = currentPeriodEnd 
    ? format(new Date(currentPeriodEnd), 'MMM d, yyyy')
    : '';

  // Calculate days until expiration
  const daysUntilExpiration = currentPeriodEnd
    ? Math.ceil((new Date(currentPeriodEnd).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
    : 0;

  // Determine badge variant based on status
  const getBadgeVariant = () => {
    if (!status) return 'outline';
    
    switch (status.toLowerCase()) {
      case 'active':
        return 'success';
      case 'trialing':
        return 'warning';
      case 'past_due':
        return 'destructive';
      case 'canceled':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  // Get icon based on status
  const getStatusIcon = () => {
    if (!status) return null;
    
    switch (status.toLowerCase()) {
      case 'active':
        return <CheckCircle className="h-3 w-3 mr-1" />;
      case 'trialing':
        return <Clock className="h-3 w-3 mr-1" />;
      case 'past_due':
      case 'canceled':
        return <AlertCircle className="h-3 w-3 mr-1" />;
      default:
        return null;
    }
  };

  // Get display text
  const getDisplayText = () => {
    if (isLoadingSubscription) return 'Loading...';
    if (!status) return 'No Subscription';
    
    return planName ? `${planName} Plan` : status;
  };

  // Get tooltip content
  const getTooltipContent = () => {
    if (isLoadingSubscription) return 'Loading subscription information...';
    if (!status) return 'No active subscription found';
    
    let content = `Status: ${status}`;
    
    if (planName) {
      content += `\nPlan: ${planName}`;
    }
    
    if (formattedExpirationDate) {
      content += `\nRenews: ${formattedExpirationDate}`;
      
      if (daysUntilExpiration > 0) {
        content += ` (${daysUntilExpiration} days)`;
      }
    }
    
    return content;
  };

  // Close tooltip after a delay when opened
  useEffect(() => {
    let timeout: NodeJS.Timeout;
    
    if (tooltipOpen) {
      timeout = setTimeout(() => {
        setTooltipOpen(false);
      }, 3000);
    }
    
    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [tooltipOpen]);

  if (!organizationId) return null;

  return (
    <TooltipProvider>
      <Tooltip open={tooltipOpen} onOpenChange={setTooltipOpen}>
        <TooltipTrigger asChild>
          <Badge 
            variant={getBadgeVariant()} 
            className="cursor-help flex items-center gap-1"
          >
            {getStatusIcon()}
            {getDisplayText()}
            {currentPeriodEnd && daysUntilExpiration <= 7 && (
              <CalendarIcon className="h-3 w-3 ml-1" />
            )}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p className="whitespace-pre-line">{getTooltipContent()}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
