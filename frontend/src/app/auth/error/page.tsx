'use client';

import { useEffect, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle } from 'lucide-react';
import Link from 'next/link';

// Error codes and their messages
const errorMessages: Record<string, string> = {
  'Configuration': 'There is a problem with the server configuration. Please contact support.',
  'AccessDenied': 'You do not have permission to sign in.',
  'Verification': 'The verification link may have expired or already been used.',
  'OAuthSignin': 'Error in the OAuth sign-in process.',
  'OAuthCallback': 'Error in the OAuth callback process.',
  'OAuthCreateAccount': 'Could not create OAuth account.',
  'EmailCreateAccount': 'Could not create email account.',
  'Callback': 'Error in the callback handler.',
  'OAuthAccountNotLinked': 'This email is already associated with another account.',
  'EmailSignin': 'Error sending the email for sign in.',
  'CredentialsSignin': 'The email or password you entered is incorrect.',
  'SessionRequired': 'You must be signed in to access this page.',
  'Default': 'An unexpected error occurred. Please try again later.',
};

export default function ErrorPage() {
  const searchParams = useSearchParams();
  const [error, setError] = useState<string>('Default');
  const [errorDescription, setErrorDescription] = useState<string | null>(null);

  useEffect(() => {
    const errorParam = searchParams?.get('error');
    if (errorParam && errorParam in errorMessages) {
      setError(errorParam);
    } else {
      setError('Default');
    }

    const errorDescriptionParam = searchParams?.get('error_description');
    if (errorDescriptionParam) {
      setErrorDescription(errorDescriptionParam);
    }
  }, [searchParams]);

  return (
    <div className="flex min-h-screen items-center justify-center p-4">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="space-y-1">
          <div className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            <CardTitle>Authentication Error</CardTitle>
          </div>
          <CardDescription>
            {errorDescription || errorMessages[error] || errorMessages['Default']}
          </CardDescription>
        </CardHeader>
        <CardContent className="pt-4">
          <p className="text-sm text-muted-foreground">
            Please try again or contact support if the problem persists.
          </p>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button variant="outline" asChild>
            <Link href="/auth/signin">Back to Sign In</Link>
          </Button>
          <Button asChild>
            <Link href="/">Go to Home</Link>
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
