import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

const BACKEND_URL = process.env.BACKEND_API_URL || 'http://localhost:8000';

interface TestResult {
  endpoint: string;
  method: string;
  status: number;
  success: boolean;
  error?: string;
  response?: any;
}

async function testEndpoint(
  endpoint: string,
  method: string,
  headers: Record<string, string> = {},
  body?: any
): Promise<TestResult> {
  try {
    const response = await fetch(`${BACKEND_URL}${endpoint}`, {
      method,
      headers: {
        'Content-Type': 'application/json',
        ...headers,
      },
      body: body ? JSON.stringify(body) : undefined,
    });

    const data = await response.json();

    return {
      endpoint,
      method,
      status: response.status,
      success: response.ok,
      response: data,
    };
  } catch (error) {
    return {
      endpoint,
      method,
      status: 0,
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  const authHeaders = session?.accessToken
    ? { Authorization: `Bearer ${session.accessToken}` }
    : {};

  const results: TestResult[] = [];

  // Test public endpoints
  results.push(await testEndpoint('/', 'GET'));
  results.push(await testEndpoint('/health', 'GET'));

  // Test API endpoints that don't require auth
  results.push(await testEndpoint('/api/hello', 'GET'));
  results.push(await testEndpoint('/api/echo', 'POST', {}, { message: 'test' }));

  // Test auth endpoints (without actual auth)
  results.push(await testEndpoint('/api/auth/signin', 'POST', {}, {
    email: '<EMAIL>',
    password: 'testpassword'
  }));

  // Test authenticated endpoints
  if (session?.accessToken) {
    // User endpoints
    results.push(await testEndpoint('/api/users/me', 'GET', authHeaders));
    
    // Organization endpoints
    results.push(await testEndpoint('/api/organizations', 'GET', authHeaders));
    
    // Project endpoints
    results.push(await testEndpoint('/api/projects', 'GET', authHeaders));
    
    // Translation endpoints
    results.push(await testEndpoint('/api/translations/keys', 'GET', authHeaders));
    
    // Locale endpoints
    results.push(await testEndpoint('/api/locales', 'GET', authHeaders));
    
    // AI Credits endpoints
    results.push(await testEndpoint('/api/ai-credits/pricing', 'GET', authHeaders));
    
    // Subscription plans
    results.push(await testEndpoint('/api/subscription-plans', 'GET', authHeaders));
  }

  // Summary
  const summary = {
    total: results.length,
    successful: results.filter(r => r.success).length,
    failed: results.filter(r => !r.success).length,
    authenticated: !!session?.accessToken,
  };

  return NextResponse.json({
    summary,
    results,
    timestamp: new Date().toISOString(),
  });
}
