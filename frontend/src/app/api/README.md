# API Routes Proxy Pattern

This directory contains Next.js API routes that act as a proxy to the backend Rust API.

## Why Use a Proxy?

1. **Security**: Keeps backend API keys and sensitive data on the server side
2. **CORS**: Avoids CORS issues by making requests from the same origin
3. **Authentication**: Handles authentication tokens server-side
4. **Error Handling**: Provides consistent error handling and response formatting

## How It Works

All API routes use the `createRouteHandlers` utility from `/src/lib/api/proxy.ts` which:

- Automatically forwards requests to the backend API (running on port 8000)
- Handles authentication by including the JWT token from the session
- Forwards query parameters and request bodies
- Returns the backend response with appropriate status codes

## Usage Examples

### Simple Proxy Route

```typescript
// src/app/api/users/profile/route.ts
import { createRouteHandlers } from '@/lib/api/proxy';

export const { GET, PUT } = createRouteHandlers('/api/users/me');
```

### Route with Dynamic Parameters

```typescript
// src/app/api/organizations/[organizationId]/route.ts
import { createRouteHandlers } from '@/lib/api/proxy';
import { NextRequest } from 'next/server';

export const { GET, PUT, DELETE } = createRouteHandlers(
  (request: NextRequest) => {
    const organizationId = request.nextUrl.pathname.split('/')[4];
    return `/api/organizations/${organizationId}`;
  }
);
```

### Public Route (No Authentication)

```typescript
// src/app/api/public/health/route.ts
import { createRouteHandlers } from '@/lib/api/proxy';

export const { GET } = createRouteHandlers('/health', { requireAuth: false });
```

## Environment Variables

Make sure these are set in your `.env.local`:

```env
NEXT_PUBLIC_API_URL=http://localhost:8000
BACKEND_API_URL=http://localhost:8000
```

## Adding New Routes

1. Create a new file in the appropriate directory under `/src/app/api/`
2. Import `createRouteHandlers` from `@/lib/api/proxy`
3. Export the HTTP methods you need (GET, POST, PUT, DELETE, PATCH)
4. The proxy will automatically handle the rest!

## Backend API Reference

The backend API is running on port 8000 and provides the following endpoints:

- `/api/users/*` - User management
- `/api/organizations/*` - Organization management
- `/api/projects/*` - Project management
- `/api/translations/*` - Translation management
- `/api/auth/*` - Authentication
- `/health` - Health check (public)

See the backend documentation for detailed API specifications.
