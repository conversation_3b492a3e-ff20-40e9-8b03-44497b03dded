import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';

const execPromise = promisify(exec);

export async function POST(req: NextRequest) {
  try {
    const body = await req.text();
    const signature = req.headers.get('stripe-signature');

    if (!signature) {
      return NextResponse.json(
        { error: 'Missing Stripe signature' },
        { status: 400 }
      );
    }

    // In a production environment, you would verify the signature
    // For now, we'll just parse the event
    const event = JSON.parse(body);
    const eventType = event.type;
    const eventData = JSON.stringify(event.data.object);

    // Execute the script to handle the webhook event
    const scriptPath = process.env.NODE_ENV === 'production'
      ? '/app/scripts/handle_webhook.sh'
      : '../../../adc-muti-languages-service/scripts/handle_webhook.sh';

    const { stdout, stderr } = await execPromise(
      `${scriptPath} ${eventType} '${eventData}'`
    );

    if (stderr) {
      console.error('Error handling webhook:', stderr);
      return NextResponse.json(
        { error: 'Failed to handle webhook' },
        { status: 500 }
      );
    }

    console.log('Webhook handled successfully:', stdout);

    return NextResponse.json({ received: true });
  } catch (error: any) {
    console.error('Error in webhook handler:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
