import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get, post } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for fetching translation keys by project slug
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the slug from the params
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project slug is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Get the resource_id from the query params (optional filter)
    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resource_id');

    // Build query parameters
    const queryParams: Record<string, string> = {};
    if (resourceId) queryParams.resource_id = resourceId;

    // Forward request to backend API
    const response = await get(`/projects/slug/${slug}/keys`, {
      baseUrl: API_BASE_URL,
      params: queryParams,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    // Return the response
    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching translation keys by project slug:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An error occurred while fetching the translation keys',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * POST handler for creating a translation key for a project by slug
 * This is a proxy to the backend API
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the slug from the params
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project slug is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.key_name) {
      return NextResponse.json(
        {
          success: false,
          message: 'Key name is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await post(`/projects/slug/${slug}/keys`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    // Return the response
    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('Error creating translation key by project slug:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An error occurred while creating the translation key',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
