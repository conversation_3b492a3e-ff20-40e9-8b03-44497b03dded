import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

/**
 * GET handler for fetching a project by slug
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }
    
    // Get the slug from the params
    const { slug } = params;
    
    if (!slug) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project slug is required',
          data: null,
        },
        { status: 400 }
      );
    }
    
    // Get the organization_id from the query params
    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get('organization_id');
    
    if (!organizationId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Organization ID is required',
          data: null,
        },
        { status: 400 }
      );
    }
    
    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
    
    // Make the request to the backend API
    const response = await fetch(`${apiUrl}/api/projects/slug/${slug}?organization_id=${organizationId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    });
    
    // Get the response data
    const data = await response.json();
    
    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error fetching project by slug:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while fetching the project',
        data: null,
      },
      { status: 500 }
    );
  }
}
