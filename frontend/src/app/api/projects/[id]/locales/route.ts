import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get, post } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for fetching project locales
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the ID from the params
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await get(`/projects/${id}/locales`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    // Return the response
    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching project locales:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An error occurred while fetching project locales',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * POST handler for adding a locale to a project
 * This is a proxy to the backend API
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the ID from the params
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Forward request to backend API
    const response = await post(`/projects/${id}/locales`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    // Return the response
    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('Error adding locale to project:', error);

    return NextResponse.json(
      {
        success: false,
        message: error.message || 'An error occurred while adding locale to project',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
