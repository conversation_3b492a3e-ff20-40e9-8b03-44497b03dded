import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { success: false, message: 'Unauthorized' },
      { status: 401 }
    );
  }

  const projectId = params.id;

  if (!projectId) {
    return NextResponse.json(
      { success: false, message: 'Project ID is required' },
      { status: 400 }
    );
  }

  // Get resource_id from query params (optional filter)
  const searchParams = request.nextUrl.searchParams;
  const resourceId = searchParams.get('resource_id');

  // Mock data for translation keys
  let mockKeys = [
    {
      id: '1',
      resource_id: 'resource1',
      resource_name: 'Common',
      key_name: 'welcome_message',
      description: 'Welcome message shown on the homepage',
      context: 'Homepage',
      is_plural: false,
      max_length: 100,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: '2',
      resource_id: 'resource1',
      resource_name: 'Common',
      key_name: 'login_button',
      description: 'Text for the login button',
      context: 'Authentication',
      is_plural: false,
      max_length: 20,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: '3',
      resource_id: 'resource2',
      resource_name: 'Errors',
      key_name: 'error_message',
      description: 'Generic error message',
      context: 'Error handling',
      is_plural: false,
      max_length: 200,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: '4',
      resource_id: 'resource2',
      resource_name: 'Errors',
      key_name: 'not_found',
      description: '404 error message',
      context: 'Error handling',
      is_plural: false,
      max_length: 100,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: '5',
      resource_id: 'resource3',
      resource_name: 'Homepage',
      key_name: 'hero_title',
      description: 'Main title on the homepage',
      context: 'Homepage',
      is_plural: false,
      max_length: 50,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];

  // Filter by resource_id if provided
  if (resourceId) {
    mockKeys = mockKeys.filter(key => key.resource_id === resourceId);
  }

  return NextResponse.json(
    { success: true, data: mockKeys, message: 'Translation keys retrieved successfully' },
    { status: 200 }
  );
}
