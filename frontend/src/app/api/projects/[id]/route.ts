import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

/**
 * GET handler for fetching a project by ID
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }
    
    // Get the ID from the params
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project ID is required',
          data: null,
        },
        { status: 400 }
      );
    }
    
    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
    
    // Make the request to the backend API
    const response = await fetch(`${apiUrl}/api/projects/${id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    });
    
    // Get the response data
    const data = await response.json();
    
    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error fetching project:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while fetching the project',
        data: null,
      },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating a project
 * This is a proxy to the backend API
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }
    
    // Get the ID from the params
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project ID is required',
          data: null,
        },
        { status: 400 }
      );
    }
    
    // Get the request body
    const body = await request.json();
    
    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
    
    // Make the request to the backend API
    const response = await fetch(`${apiUrl}/api/projects/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
      body: JSON.stringify(body),
    });
    
    // Get the response data
    const data = await response.json();
    
    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error updating project:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while updating the project',
        data: null,
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting a project
 * This is a proxy to the backend API
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);
    
    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }
    
    // Get the ID from the params
    const { id } = params;
    
    if (!id) {
      return NextResponse.json(
        {
          success: false,
          message: 'Project ID is required',
          data: null,
        },
        { status: 400 }
      );
    }
    
    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8002';
    
    // Make the request to the backend API
    const response = await fetch(`${apiUrl}/api/projects/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    });
    
    // Get the response data
    const data = await response.json();
    
    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error deleting project:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while deleting the project',
        data: null,
      },
      { status: 500 }
    );
  }
}
