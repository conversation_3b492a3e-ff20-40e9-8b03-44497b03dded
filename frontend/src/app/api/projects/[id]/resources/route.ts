import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get, post } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { success: false, message: 'Unauthorized' },
      { status: 401 }
    );
  }

  const projectId = params.id;

  try {
    // Forward request to backend API
    const response = await get(`/projects/${projectId}/resources`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching resources:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to fetch resources',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { success: false, message: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const projectId = params.id;
    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.type_) {
      return NextResponse.json(
        { success: false, message: 'Name and type are required' },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await post(`/projects/${projectId}/resources`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 201 });
  } catch (error: any) {
    console.error('Error creating resource:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to create resource',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
