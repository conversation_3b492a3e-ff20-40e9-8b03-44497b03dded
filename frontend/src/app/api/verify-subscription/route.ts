import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
if (!stripeSecretKey) {
  console.error('STRIPE_SECRET_KEY is not set in environment variables');
}
const stripe = new Stripe(stripeSecretKey || '');

export async function POST(req: NextRequest) {
  try {
    const { sessionId } = await req.json();

    if (!sessionId) {
      return NextResponse.json(
        { error: 'Missing session ID' },
        { status: 400 }
      );
    }

    // Retrieve the checkout session using the Stripe SDK
    const session = await stripe.checkout.sessions.retrieve(sessionId);

    const organizationId = session.metadata?.organization_id;

    if (!organizationId) {
      return NextResponse.json(
        { error: 'Organization ID not found in session' },
        { status: 400 }
      );
    }

    // Get the subscription ID from the session
    const subscriptionId = session.subscription;

    if (!subscriptionId) {
      return NextResponse.json(
        { error: 'Subscription ID not found in session' },
        { status: 400 }
      );
    }

    // Get the subscription details from Stripe
    const subscription = await stripe.subscriptions.retrieve(subscriptionId as string);

    // Log subscription details for debugging
    console.log('Subscription details:', {
      id: subscription.id,
      status: subscription.status,
      current_period_end: subscription.current_period_end,
      current_period_end_type: typeof subscription.current_period_end,
    });

    // Get the plan/price details
    const priceId = subscription.items.data[0]?.price.id;
    const status = subscription.status;

    // Safely convert the current_period_end to a date string
    let currentPeriodEnd;
    try {
      // Check if current_period_end is a valid number
      if (subscription.current_period_end && !isNaN(Number(subscription.current_period_end))) {
        // Convert to milliseconds and create a date
        currentPeriodEnd = new Date(Number(subscription.current_period_end) * 1000).toISOString();
      } else {
        // If it's not a valid number, use current date plus 1 month as fallback
        const fallbackDate = new Date();
        fallbackDate.setMonth(fallbackDate.getMonth() + 1);
        currentPeriodEnd = fallbackDate.toISOString();
        console.log('Using fallback date for current_period_end:', currentPeriodEnd);
      }
    } catch (error) {
      console.error('Error converting current_period_end to date:', error);
      // Use current date plus 1 month as fallback
      const fallbackDate = new Date();
      fallbackDate.setMonth(fallbackDate.getMonth() + 1);
      currentPeriodEnd = fallbackDate.toISOString();
      console.log('Using fallback date after error:', currentPeriodEnd);
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    if (!apiUrl) {
      console.error('NEXT_PUBLIC_API_URL is not set in environment variables');
      return NextResponse.json(
        { error: 'API URL not configured' },
        { status: 500 }
      );
    }

    // Log the data we're sending to the backend
    const requestBody = {
      stripe_subscription_id: subscriptionId,
      price_id: priceId,
      status: status,
      current_period_end: currentPeriodEnd,
    };

    console.log('Sending subscription verification data to backend:', requestBody);
    console.log('Backend API URL:', `${apiUrl}/api/organizations/${organizationId}/subscription/verify`);

    // Call the backend API to update the organization's subscription
    let backendResponse;
    try {
      backendResponse = await fetch(`${apiUrl}/api/organizations/${organizationId}/subscription/verify`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });
    } catch (error) {
      console.error('Network error when calling backend API:', error);
      throw new Error(`Network error when verifying subscription: ${error.message}`);
    }

    if (!backendResponse.ok) {
      let errorMessage = 'Failed to update subscription in backend';
      let statusCode = backendResponse.status;
      let statusText = backendResponse.statusText;

      console.error(`Backend API error: ${statusCode} ${statusText}`);

      try {
        const errorData = await backendResponse.json();
        console.error('Backend API error details:', errorData);
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        console.error('Error parsing backend error response:', e);
      }
      throw new Error(`${errorMessage} (${statusCode} ${statusText})`);
    }

    let backendData;
    try {
      backendData = await backendResponse.json();
      console.log('Backend API response:', backendData);
    } catch (error) {
      console.error('Error parsing backend response:', error);
      throw new Error('Failed to parse backend response');
    }

    return NextResponse.json({
      success: true,
      organizationId,
      subscriptionId,
      data: backendData.data,
    });
  } catch (error: any) {
    console.error('Error in verify-subscription:', error);
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
