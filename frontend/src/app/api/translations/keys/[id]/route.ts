import { createRouteHandlers } from '@/lib/api/proxy';
import { NextRequest } from 'next/server';

// Create route handlers for specific translation key
// This proxies requests to the backend /api/translations/keys/{id} endpoint
export const { GET, PUT, DELETE } = createRouteHandlers(
  (request: NextRequest) => {
    const id = request.nextUrl.pathname.split('/').pop(); // Extract id from path
    return `/api/translations/keys/${id}`;
  }
);