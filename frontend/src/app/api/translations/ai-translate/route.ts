import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { post } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json(
      { success: false, message: 'Unauthorized' },
      { status: 401 }
    );
  }

  try {
    const body = await request.json();

    // Validate required fields
    if (!body.text || !body.source_locale || !body.target_locale) {
      return NextResponse.json(
        { success: false, message: 'Text, source locale, and target locale are required' },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await post(`/translations/ai-translate`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error with AI translation:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to generate AI translation',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
