import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for listing audit logs for a specific API key
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string; keyId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get query parameters
    const searchParams = request.nextUrl.searchParams;
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    // Build query string
    let queryString = '';
    if (limit) queryString += `&limit=${limit}`;
    if (offset) queryString += `&offset=${offset}`;

    // Add ? if there are query parameters
    if (queryString) {
      queryString = '?' + queryString.substring(1);
    }

    // Forward request to backend API
    const response = await get(
      `/organizations/${params.organizationId}/api-keys/${params.keyId}/audit-logs${queryString}`,
      {
        baseUrl: API_BASE_URL,
        headers: {
          Authorization: `Bearer ${session.accessToken}`
        }
      }
    );

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error listing API key audit logs:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to list API key audit logs',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
