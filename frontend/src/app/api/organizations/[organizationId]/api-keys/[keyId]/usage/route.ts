import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for fetching API key usage statistics
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string; keyId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the organization ID and key ID from the params
    const { organizationId, keyId } = params;

    if (!organizationId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Organization ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    if (!keyId) {
      return NextResponse.json(
        {
          success: false,
          message: 'API key ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Get the days parameter from the query string
    const searchParams = request.nextUrl.searchParams;
    const days = searchParams.get('days');
    
    // Build the URL with the days parameter if provided
    let url = `/organizations/${organizationId}/api-keys/${keyId}/usage`;
    if (days) {
      url += `?days=${days}`;
    }

    // Forward request to backend API
    const response = await get(url, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching API key usage:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to fetch API key usage',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
