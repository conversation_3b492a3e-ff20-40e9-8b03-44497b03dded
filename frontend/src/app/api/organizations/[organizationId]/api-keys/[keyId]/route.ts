import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { del } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * DELETE handler for revoking an API key
 * This is a proxy to the backend API
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { organizationId: string; keyId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the organization ID and key ID from the params
    const { organizationId, keyId } = params;

    if (!organizationId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Organization ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    if (!keyId) {
      return NextResponse.json(
        {
          success: false,
          message: 'API key ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await del(`/organizations/${organizationId}/api-keys/${keyId}`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error revoking API key:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to revoke API key',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
