import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get, post } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for fetching API keys for an organization
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the organization ID from the params
    const { organizationId } = params;

    if (!organizationId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Organization ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await get(`/organizations/${organizationId}/api-keys`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error fetching API keys:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to fetch API keys',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * POST handler for creating an API key for an organization
 * This is a proxy to the backend API
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the organization ID from the params
    const { organizationId } = params;

    if (!organizationId) {
      return NextResponse.json(
        {
          success: false,
          message: 'Organization ID is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Get the request body
    const body = await request.json();

    // Validate required fields
    if (!body.name) {
      return NextResponse.json(
        {
          success: false,
          message: 'Name is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Forward request to backend API
    const response = await post(`/organizations/${organizationId}/api-keys`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error creating API key:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to create API key',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
