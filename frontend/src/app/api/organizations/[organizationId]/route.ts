import { createRouteHandlers } from '@/lib/api/proxy';
import { NextRequest } from 'next/server';

// Create route handlers for specific organization
// This proxies requests to the backend /api/organizations/{organizationId} endpoint
export const { GET, PUT, DELETE } = createRouteHandlers(
  (request: NextRequest) => {
    const organizationId = request.nextUrl.pathname.split('/')[4]; // Extract organizationId from path
    return `/api/organizations/${organizationId}`;
  }
);
