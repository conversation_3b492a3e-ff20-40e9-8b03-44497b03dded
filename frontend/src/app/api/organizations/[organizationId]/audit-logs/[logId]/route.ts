import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { get } from '@/lib/api';
import { API_BASE_URL } from '@/lib/constants';

/**
 * GET handler for retrieving a specific audit log
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string; logId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Forward request to backend API
    const response = await get(`/organizations/${params.organizationId}/audit-logs/${params.logId}`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error(`Error retrieving audit log ${params.logId}:`, error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to retrieve audit log',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
