import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get, put, del } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for getting a permission group
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string; groupId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Forward request to backend API
    const response = await get(`/organizations/${params.organizationId}/permission-groups/${params.groupId}`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error getting permission group:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to get permission group',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * PUT handler for updating a permission group
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { organizationId: string; groupId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();

    // Forward request to backend API
    const response = await put(`/organizations/${params.organizationId}/permission-groups/${params.groupId}`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error updating permission group:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to update permission group',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * DELETE handler for deleting a permission group
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { organizationId: string; groupId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Forward request to backend API
    const response = await del(`/organizations/${params.organizationId}/permission-groups/${params.groupId}`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error deleting permission group:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to delete permission group',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
