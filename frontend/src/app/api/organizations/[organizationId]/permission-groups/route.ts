import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import { get, post, put, del } from '@/lib/fetch/fetchClient';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

/**
 * GET handler for listing permission groups
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Forward request to backend API
    const response = await get(`/organizations/${params.organizationId}/permission-groups`, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error listing permission groups:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to list permission groups',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}

/**
 * POST handler for creating a permission group
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { organizationId: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get request body
    const body = await request.json();

    // Forward request to backend API
    const response = await post(`/organizations/${params.organizationId}/permission-groups`, body, {
      baseUrl: API_BASE_URL,
      headers: {
        Authorization: `Bearer ${session.accessToken}`
      }
    });

    return NextResponse.json(response, { status: 200 });
  } catch (error: any) {
    console.error('Error creating permission group:', error);
    return NextResponse.json(
      {
        success: false,
        message: error.message || 'Failed to create permission group',
        error: error.data
      },
      { status: error.status || 500 }
    );
  }
}
