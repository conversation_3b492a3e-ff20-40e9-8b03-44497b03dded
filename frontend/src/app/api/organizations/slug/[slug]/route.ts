import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

/**
 * GET handler for fetching an organization by slug
 * This is a proxy to the backend API
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Get the session
    const session = await getServerSession(authOptions);

    // Check if user is authenticated
    if (!session?.accessToken) {
      return NextResponse.json(
        {
          success: false,
          message: 'Unauthorized',
          data: null,
        },
        { status: 401 }
      );
    }

    // Get the slug from the params
    const { slug } = params;

    if (!slug) {
      return NextResponse.json(
        {
          success: false,
          message: 'Organization slug is required',
          data: null,
        },
        { status: 400 }
      );
    }

    // Get the API URL from environment variables
    const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001';

    // Make the request to the backend API
    const response = await fetch(`${apiUrl}/api/organizations/slug/${slug}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
    });

    // Get the response data
    const data = await response.json();

    // Return the response
    return NextResponse.json(data, { status: response.status });
  } catch (error) {
    console.error('Error fetching organization by slug:', error);

    return NextResponse.json(
      {
        success: false,
        message: 'An error occurred while fetching the organization',
        data: null,
      },
      { status: 500 }
    );
  }
}
