import { NextRequest, NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';

// Initialize Stripe with your secret key
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
if (!stripeSecretKey) {
  console.error('STRIPE_SECRET_KEY is not set in environment variables');
}
const stripe = new Stripe(stripeSecretKey || '');

export async function POST(req: NextRequest) {
  try {
    console.log('Received request to create checkout session');

    const { priceId, organizationId, successUrl, cancelUrl } = await req.json();
    console.log('Request parameters:', { priceId, organizationId, successUrl, cancelUrl });

    if (!priceId || !organizationId || !successUrl || !cancelUrl) {
      console.error('Missing required parameters:', { priceId, organizationId, successUrl, cancelUrl });
      return NextResponse.json(
        { error: 'Missing required parameters' },
        { status: 400 }
      );
    }

    // Validate Stripe is properly initialized
    if (!stripeSecretKey) {
      console.error('Stripe secret key is not set');
      return NextResponse.json(
        { error: 'Stripe is not properly configured' },
        { status: 500 }
      );
    }

    console.log('Stripe is properly configured, creating checkout session');

    try {
      // Create a checkout session using the Stripe SDK
      const session = await stripe.checkout.sessions.create({
        success_url: successUrl,
        cancel_url: cancelUrl,
        mode: 'subscription',
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        client_reference_id: organizationId,
        metadata: {
          organization_id: organizationId,
        },
        // Customer creation is handled automatically in subscription mode
        // Collect customer email and name
        billing_address_collection: 'auto',
        // Collect customer phone
        phone_number_collection: {
          enabled: true,
        },
      });

      if (!session.url) {
        return NextResponse.json(
          { error: 'Failed to generate checkout URL' },
          { status: 500 }
        );
      }

      return NextResponse.json({
        url: session.url,
        sessionId: session.id
      });
    } catch (stripeError: any) {
      console.error('Stripe error:', stripeError);
      console.error('Stripe error details:', JSON.stringify(stripeError, null, 2));
      return NextResponse.json(
        { error: stripeError.message || 'Error creating Stripe checkout session' },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('Error in create-checkout-session:', error);
    console.error('Error details:', JSON.stringify(error, null, 2));
    return NextResponse.json(
      { error: error.message || 'Internal server error' },
      { status: 500 }
    );
  }
}
