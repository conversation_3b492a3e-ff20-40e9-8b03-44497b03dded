'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useListProjectsQuery } from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Languages, FolderOpen, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

export default function TranslationsPage() {
  const router = useRouter();
  const [selectedProject, setSelectedProject] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  
  // Fetch projects
  const { 
    data: projectsData, 
    isLoading,
    error,
  } = useListProjectsQuery({ organization_id: undefined });
  
  // Handle project change
  const handleProjectChange = (value: string) => {
    setSelectedProject(value);
  };
  
  // Handle search change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };
  
  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Skeleton className="h-12 w-full mb-6" />
        <div className="grid grid-cols-1 gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-24 w-full" />
          ))}
        </div>
      </div>
    );
  }
  
  // Error state
  if (error) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load projects</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading your projects. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/dashboard')}>Return to Dashboard</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  // No projects state
  if (projectsData?.data?.length === 0) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
          <div>
            <h1 className="text-3xl font-bold mb-2">Translations</h1>
            <p className="text-muted-foreground">Manage your translations across all projects</p>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>No Projects</CardTitle>
            <CardDescription>You don't have any projects yet</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Create a project first to start managing translations.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/projects')}>Go to Projects</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold mb-2">Translations</h1>
          <p className="text-muted-foreground">Manage your translations across all projects</p>
        </div>
      </div>
      
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        <div className="w-full md:w-1/3">
          <Select value={selectedProject || ''} onValueChange={handleProjectChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select a project" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All Projects</SelectItem>
              {projectsData?.data?.map((project) => (
                <SelectItem key={project.id} value={project.id}>
                  {project.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        <div className="relative w-full md:w-2/3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search translations..."
            value={searchQuery}
            onChange={handleSearchChange}
            className="pl-10"
          />
        </div>
      </div>
      
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Languages className="mr-2 h-5 w-5 text-primary" />
            Translation Management
          </CardTitle>
          <CardDescription>
            This feature is coming soon. You'll be able to manage all your translations here.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="mb-4">
            The translation management interface will allow you to:
          </p>
          <ul className="list-disc pl-6 space-y-2">
            <li>View and edit translations across all projects</li>
            <li>Filter translations by key, locale, or project</li>
            <li>Import and export translations in various formats</li>
            <li>Use AI-powered translation suggestions</li>
            <li>Track translation progress and completion status</li>
          </ul>
        </CardContent>
        <CardFooter>
          <Button variant="outline" onClick={() => router.push('/projects')}>
            <FolderOpen className="mr-2 h-4 w-4" />
            Go to Projects
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
