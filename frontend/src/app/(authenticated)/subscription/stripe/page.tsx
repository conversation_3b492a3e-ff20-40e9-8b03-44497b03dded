'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useListOrganizationsQuery } from '@/lib/redux/api/endpoints';
import { PricingPlans } from '@/components/subscription/PricingPlans';
import { PricingPlan } from '@/components/subscription/PricingCard';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';

export default function StripeSubscriptionPage() {
  const [selectedOrganization, setSelectedOrganization] = useState<string>('');

  const router = useRouter();
  const { toast } = useToast();

  // Fetch organizations
  const {
    data: organizationsData,
    isLoading: isLoadingOrganizations,
    error: organizationsError
  } = useListOrganizationsQuery();

  // Set default organization if there's only one
  useEffect(() => {
    if (organizationsData?.data?.length === 1) {
      setSelectedOrganization(organizationsData.data[0].id);
    }
  }, [organizationsData]);

  // Mock subscription plans data
  const mockPlans = [
    {
      id: "plan_standard",
      name: "Standard",
      description: "Perfect for growing teams",
      price_monthly: 29,
      price_yearly: 290,
      features: [
        "Unlimited projects",
        "10 team members",
        "1,000 AI translation credits/month",
        "Priority support",
        "Advanced analytics",
        "Custom export formats"
      ],
      is_popular: true
    },
    {
      id: "plan_premium",
      name: "Premium",
      description: "For professional translation needs",
      price_monthly: 79,
      price_yearly: 790,
      features: [
        "Unlimited projects",
        "Unlimited team members",
        "5,000 AI translation credits/month",
        "24/7 priority support",
        "Advanced analytics",
        "Custom export formats",
        "API access",
        "Custom integrations"
      ],
      is_popular: false
    }
  ];

  // Transform mock plans to component format with Stripe price IDs
  const transformedPlans: PricingPlan[] = mockPlans.map(plan => ({
    id: plan.id,
    name: plan.name,
    description: plan.description,
    price: plan.price_monthly,
    priceYearly: plan.price_yearly,
    features: plan.features.map(feature => ({ name: feature, included: true })),
    isPopular: plan.is_popular,
    // Add Stripe price IDs
    stripePriceId: plan.name === 'Standard' ? 'price_1RQ5NxCtNXkGk5bXTvH1EfaT' :
                  plan.name === 'Premium' ? 'price_1RQ5OiCtNXkGk5bX1deWYLQv' : undefined,
    stripePriceIdYearly: plan.name === 'Standard' ? 'price_1RQ5OCCtNXkGk5bXHgAvz0S8' :
                        plan.name === 'Premium' ? 'price_1RQ5P2CtNXkGk5bX4Pf7cU53' : undefined,
  }));

  // Handle plan selection (for non-Stripe checkout)
  const handleSelectPlan = (planId: string, yearly: boolean) => {
    // This is a fallback for plans without Stripe price IDs
    toast({
      title: 'Stripe Checkout Required',
      description: 'This plan requires Stripe checkout, but no price ID is available.',
    });
  };

  // Loading state
  if (isLoadingOrganizations) {
    return (
      <div className="container py-10 space-y-8">
        <div className="text-center space-y-2">
          <Skeleton className="h-8 w-64 mx-auto" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-96 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (organizationsError) {
    return (
      <div className="container py-10">
        <div className="p-6 bg-destructive/10 rounded-lg">
          <h2 className="text-xl font-bold text-destructive">Error</h2>
          <p>Failed to load organizations. Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container py-10 space-y-8">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Choose Your Plan</h1>
        <p className="text-muted-foreground">
          Select the plan that best fits your needs
        </p>
      </div>

      {organizationsData?.data && organizationsData.data.length > 1 && (
        <div className="max-w-md mx-auto">
          <Label htmlFor="organization-select">Select Organization</Label>
          <Select
            value={selectedOrganization}
            onValueChange={setSelectedOrganization}
          >
            <SelectTrigger id="organization-select">
              <SelectValue placeholder="Select an organization" />
            </SelectTrigger>
            <SelectContent>
              {organizationsData.data.map((org) => (
                <SelectItem key={org.id} value={org.id}>
                  {org.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      )}

      <PricingPlans
        plans={transformedPlans}
        onSelectPlan={handleSelectPlan}
        isLoading={false}
        organizationId={selectedOrganization}
        useStripeCheckout={true}
      />
    </div>
  );
}
