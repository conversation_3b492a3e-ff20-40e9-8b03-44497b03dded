'use client';

import { useState, useEffect } from 'react';
import {
  useListSubscriptionPlansQuery,
  useGetOrganizationSubscriptionQuery
} from '@/lib/redux/api/endpoints/subscriptionApi';
import { useListOrganizationsQuery } from '@/lib/redux/api/endpoints';
import { PricingPlans } from '@/components/subscription/PricingPlans';
import { PricingPlan } from '@/components/subscription/PricingCard';
import { SubscriptionDetailsWithData } from '@/components/subscription/SubscriptionDetailsWithData';
import { SubscriptionHistoryWithData } from '@/components/subscription/SubscriptionHistoryWithData';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export default function SubscriptionPage() {
  // State for yearly/monthly toggle (used in handleSelectPlan)
  const [isYearly, setIsYearly] = useState(true);
  const [showSuccessNotification, setShowSuccessNotification] = useState(false);

  // Toast for notifications
  const { toast } = useToast();

  // Fetch subscription plans
  const {
    data: plansData,
    isLoading: isLoadingPlans,
    error: plansError
  } = useListSubscriptionPlansQuery();

  // Fetch organizations
  const {
    data: organizationsData,
    isLoading: isLoadingOrganizations,
    error: organizationsError
  } = useListOrganizationsQuery();

  // Get the first organization ID (if available)
  const organizationId = organizationsData?.data?.[0]?.id;

  // Fetch current subscription if organization ID is available
  const {
    data: subscriptionData,
    isLoading: isLoadingSubscription,
    error: subscriptionError,
    refetch: refetchSubscription
  } = useGetOrganizationSubscriptionQuery(organizationId || '', {
    skip: !organizationId,
    refetchOnMountOrArgChange: true // Always refetch when component mounts
  });

  // Get current plan ID from subscription
  const currentPlanId = subscriptionData?.data?.plan_id;

  // For development purposes, we'll check if the subscription endpoint is available
  // If not, we'll just show the plans without the current subscription info
  const isSubscriptionEndpointAvailable = !subscriptionError;

  // Automatically refetch subscription data when component mounts
  useEffect(() => {
    if (organizationId) {
      console.log('Subscription page mounted, refetching subscription data for organization:', organizationId);
      refetchSubscription()
        .then(result => {
          if (result.error) {
            console.error('Error refetching subscription data:', result.error);
          } else {
            console.log('Successfully refetched subscription data:', result.data);
          }
        })
        .catch(error => {
          console.error('Exception when refetching subscription data:', error);
        });
    }
  }, [organizationId, refetchSubscription]);

  // Log subscription data changes and show notification
  useEffect(() => {
    if (subscriptionData) {
      console.log('Subscription data updated:', subscriptionData);

      // Check if we came from the success page by looking at the URL
      const urlParams = new URLSearchParams(window.location.search);
      const fromSuccess = urlParams.get('from') === 'success';

      if (fromSuccess && !showSuccessNotification) {
        setShowSuccessNotification(true);
        toast({
          title: 'Subscription Updated',
          description: 'Your subscription has been successfully updated.',
          variant: 'success',
        });
      }
    }
  }, [subscriptionData, toast, showSuccessNotification]);

  // Transform API plans to component format with Stripe price IDs
  const transformedPlans: PricingPlan[] = plansData?.data?.map(plan => {
    // Get Stripe price IDs directly from the API response
    const stripePriceId = plan.stripe_price_monthly || '';
    const stripePriceIdYearly = plan.stripe_price_yearly || '';

    console.log(`Plan: ${plan.name}, Monthly Price ID: ${stripePriceId}, Yearly Price ID: ${stripePriceIdYearly}`);

    return {
      id: plan.id,
      name: plan.name,
      description: plan.description,
      price: plan.price_monthly,
      priceYearly: plan.price_yearly,
      features: plan.features.map(feature => ({ name: feature, included: true })),
      isPopular: plan.is_popular,
      stripePriceId,
      stripePriceIdYearly
    };
  }) || [];

  // Log all transformed plans for debugging
  console.log('All transformed plans:', transformedPlans);

  // Handle plan selection (for non-Stripe checkout)
  const handleSelectPlan = (planId: string, yearly: boolean) => {
    setIsYearly(yearly);

    // If we have an organization and the plan has Stripe price IDs, we'll use Stripe Checkout
    // Otherwise, we'll show a message
    if (!organizationId) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No organization found. Please create an organization first.',
      });
      return;
    }

    const plan = transformedPlans.find(p => p.id === planId);
    if (!plan) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Selected plan not found',
      });
      return;
    }

    const priceId = yearly ? plan.stripePriceIdYearly : plan.stripePriceId;
    if (!priceId) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'No price ID found for this plan',
      });
      return;
    }

    // The actual checkout will be handled by the StripeCheckoutButton component
  };

  // Loading state
  if (isLoadingPlans || isLoadingOrganizations || (organizationId && isLoadingSubscription && isSubscriptionEndpointAvailable)) {
    return (
      <div className="container py-10 space-y-8">
        <div className="space-y-2">
          <Skeleton className="h-10 w-1/4" />
          <Skeleton className="h-6 w-1/2" />
        </div>
        {isSubscriptionEndpointAvailable && (
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <Skeleton className="h-6 w-48 mb-2" />
                  <Skeleton className="h-4 w-64" />
                </div>
                <Skeleton className="h-8 w-20" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
                <div className="space-y-4">
                  <Skeleton className="h-5 w-32" />
                  <Skeleton className="h-4 w-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        )}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-96" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (plansError || organizationsError) {
    return (
      <div className="container py-10">
        <div className="p-6 bg-destructive/10 rounded-lg">
          <h2 className="text-xl font-bold text-destructive">Error</h2>
          <p>Failed to load subscription plans or organizations. Please try again later.</p>
        </div>
      </div>
    );
  }

  // Find the current plan ID for highlighting in the pricing plans

  return (
    <div className="container py-10 space-y-8">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          {/* Subscription Details with real API data */}
          {isSubscriptionEndpointAvailable && (
            <SubscriptionDetailsWithData
              organizationId={organizationId || ''}
              subscriptionData={subscriptionData}
              isLoadingSubscription={isLoadingSubscription}
              refetchSubscription={refetchSubscription}
            />
          )}
        </div>

        <div className="lg:col-span-1">
          {/* Subscription History with real API data */}
          {organizationId && (
            <SubscriptionHistoryWithData
              organizationId={organizationId}
            />
          )}
        </div>
      </div>

      {subscriptionError && (
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="text-lg font-medium text-yellow-800 mb-2">Subscription Information Unavailable</h3>
          <p className="text-yellow-700">
            We couldn't retrieve your current subscription information. You can still browse and select subscription plans below.
          </p>
        </div>
      )}

      <div id="pricing-plans" className="text-center space-y-2">
        <h1 className="text-3xl font-bold">
          {isSubscriptionEndpointAvailable && subscriptionData?.data
            ? 'Manage Your Subscription'
            : 'Choose Your Plan'
          }
        </h1>
        <p className="text-muted-foreground">
          {isSubscriptionEndpointAvailable && subscriptionData?.data
            ? 'Upgrade or change your current plan to better fit your needs'
            : 'Select the plan that best fits your needs'
          }
        </p>
      </div>

      <PricingPlans
        plans={transformedPlans}
        onSelectPlan={handleSelectPlan}
        isLoading={false}
        currentPlan={isSubscriptionEndpointAvailable ? currentPlanId?.replace('plan_', '') : undefined}
        useStripeCheckout={true}
        organizationId={organizationId}
        initialIsYearly={isYearly}
      />
    </div>
  );
}
