'use client';

import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckCircle, Loader2 } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { useDispatch } from 'react-redux';
import { apiSlice } from '@/lib/redux/api/apiSlice';

export default function SubscriptionSuccessPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [isVerifying, setIsVerifying] = useState(false);
  const [isVerified, setIsVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [subscriptionData, setSubscriptionData] = useState<any>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [isRetrying, setIsRetrying] = useState(false);
  const { toast } = useToast();
  const dispatch = useDispatch();

  useEffect(() => {
    // Get the session ID from the URL
    const session_id = searchParams.get('session_id');
    if (!session_id) {
      setError('No session ID found in URL. Please try again or contact support.');
      return;
    }

    setSessionId(session_id);
    verifySubscription(session_id);
  }, [searchParams, router, dispatch]);

  const verifySubscription = async (sessionId: string, isRetry = false) => {
    if (isRetry) {
      setIsRetrying(true);
    } else {
      setIsVerifying(true);
    }
    setError(null);

    try {
      console.log(`Verifying subscription (${isRetry ? 'retry attempt ' + retryCount : 'initial attempt'})...`);

      const response = await fetch('/api/verify-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sessionId }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to verify subscription');
      }

      setSubscriptionData(data);
      setIsVerified(true);
      setRetryCount(0); // Reset retry count on success

      // Invalidate the subscription cache to force a refresh
      if (data.organizationId) {
        console.log('Invalidating subscription cache for organization:', data.organizationId);

        // Invalidate the subscription endpoint cache
        dispatch(
          apiSlice.util.invalidateTags([
            { type: 'Subscription', id: data.organizationId },
            { type: 'Organization', id: data.organizationId }
          ])
        );
      }

      toast({
        title: 'Subscription Activated',
        description: 'Your subscription has been successfully verified and activated.',
      });

      // Redirect to subscription page after a short delay
      setTimeout(() => {
        router.push('/subscription?from=success');
      }, 2000);
    } catch (error) {
      console.error('Error verifying subscription:', error);

      const errorMessage = error instanceof Error ? error.message : 'Failed to verify subscription';
      setError(errorMessage);

      // If this is the first few attempts, try again after a delay
      if (retryCount < 3) {
        const nextRetryCount = retryCount + 1;
        setRetryCount(nextRetryCount);

        const retryDelay = 2000 * nextRetryCount; // Exponential backoff

        toast({
          title: 'Verification Pending',
          description: `Subscription verification in progress. Retrying in ${retryDelay/1000} seconds...`,
          variant: 'default',
        });

        console.log(`Will retry verification in ${retryDelay}ms (attempt ${nextRetryCount})`);

        setTimeout(() => {
          verifySubscription(sessionId, true);
        }, retryDelay);
      } else {
        // After 3 retries, show the error
        toast({
          title: 'Verification Failed',
          description: `${errorMessage}. You can try again manually.`,
          variant: 'destructive',
        });
      }
    } finally {
      if (isRetry) {
        setIsRetrying(false);
      } else {
        setIsVerifying(false);
      }
    }
  };

  // Function to manually retry verification
  const handleRetry = () => {
    if (sessionId) {
      setRetryCount(0);
      verifySubscription(sessionId);
    }
  };

  const handleGoToDashboard = () => {
    router.push('/dashboard');
  };

  const handleViewSubscription = () => {
    router.push('/subscription?from=success');
  };

  return (
    <div className="container py-20 max-w-md mx-auto">
      <Card className="text-center">
        <CardHeader>
          <div className="flex justify-center mb-4">
            {isVerifying || isRetrying ? (
              <Loader2 className="h-16 w-16 text-blue-500 animate-spin" />
            ) : error ? (
              <div className="h-16 w-16 rounded-full bg-red-100 flex items-center justify-center">
                <span className="text-red-500 text-2xl font-bold">!</span>
              </div>
            ) : (
              <CheckCircle className="h-16 w-16 text-green-500" />
            )}
          </div>
          <CardTitle className="text-2xl">
            {isVerifying ? 'Verifying Subscription...' :
             isRetrying ? `Retrying Verification (Attempt ${retryCount})...` :
             error ? 'Subscription Verification Failed' :
             'Subscription Successful!'}
          </CardTitle>
          <CardDescription>
            {isVerifying
              ? 'Please wait while we verify your subscription...'
              : isRetrying
                ? 'We\'re trying again to verify your subscription...'
                : error
                  ? 'We encountered a problem with your subscription'
                  : 'Thank you for subscribing to our service.'}
          </CardDescription>
          {retryCount > 0 && !isVerified && !isRetrying && (
            <div className="mt-2 text-sm text-muted-foreground">
              {retryCount >= 3
                ? 'We\'ve tried several times to verify your subscription.'
                : `Retry attempt ${retryCount} of 3.`}
            </div>
          )}
        </CardHeader>
        <CardContent>
          {error ? (
            <div className="text-red-500 mb-4">
              <p>{error}</p>
              <p className="text-sm text-muted-foreground mt-2">
                {retryCount >= 3
                  ? 'You can try again manually or contact support if the problem persists.'
                  : 'Please wait while we try again automatically...'}
              </p>
              {retryCount >= 3 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="mt-2"
                  onClick={handleRetry}
                  disabled={isVerifying || isRetrying}
                >
                  {isVerifying || isRetrying ? (
                    <>
                      <Loader2 className="mr-2 h-3 w-3 animate-spin" />
                      Verifying...
                    </>
                  ) : (
                    'Retry Verification'
                  )}
                </Button>
              )}
            </div>
          ) : (
            <p className="mb-4">
              {isVerifying || isRetrying
                ? 'We are processing your subscription details...'
                : 'Your subscription has been activated and you now have access to all the features included in your plan.'}
            </p>
          )}

          {!error && subscriptionData && (
            <div className="bg-muted p-3 rounded-md text-left text-sm">
              <p className="font-medium">Subscription Details:</p>
              <p>Organization: {subscriptionData.organizationId}</p>
              <p>Subscription ID: {subscriptionData.subscriptionId}</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-center space-x-4">
          <Button
            variant="outline"
            onClick={handleViewSubscription}
            disabled={isVerifying || isRetrying}
          >
            {error && retryCount >= 3 ? 'Try Again' : 'View Subscription'}
          </Button>
          <Button
            onClick={handleGoToDashboard}
            disabled={isVerifying || isRetrying}
          >
            Go to Dashboard
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
