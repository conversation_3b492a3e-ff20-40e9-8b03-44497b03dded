'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useGetProfileQuery, useUpdateProfileMutation } from '@/lib/redux/api/endpoints/userApi';
import { useListOrganizationsQuery, useGetAICreditsQuery } from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/components/ui/use-toast';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  User, 
  Mail, 
  Calendar, 
  Building, 
  Zap, 
  CreditCard, 
  Settings, 
  Key, 
  RefreshCw,
  Loader2
} from 'lucide-react';

export default function ProfilePage() {
  const { data: session } = useSession();
  const { toast } = useToast();
  
  // Get user profile data
  const {
    data: profileData,
    isLoading: isLoadingProfile,
    error: profileError,
    refetch: refetchProfile
  } = useGetProfileQuery();
  
  // Get user's organizations
  const {
    data: organizationsData,
    isLoading: isLoadingOrgs,
    error: orgsError
  } = useListOrganizationsQuery();
  
  // State for profile form
  const [fullName, setFullName] = useState<string>('');
  const [preferredLanguage, setPreferredLanguage] = useState<string>('');
  const [isUpdating, setIsUpdating] = useState<boolean>(false);
  
  // Update profile mutation
  const [updateProfile] = useUpdateProfileMutation();
  
  // Set initial form values when profile data is loaded
  useState(() => {
    if (profileData?.data) {
      setFullName(profileData.data.full_name || '');
      setPreferredLanguage(profileData.data.preferred_language || 'en');
    }
  });
  
  // Get AI credits for the first organization (if any)
  const firstOrgId = organizationsData?.data?.[0]?.id;
  const {
    data: creditsData,
    isLoading: isLoadingCredits,
    error: creditsError
  } = useGetAICreditsQuery(firstOrgId || '', { skip: !firstOrgId });
  
  // Handle profile update
  const handleUpdateProfile = async () => {
    try {
      setIsUpdating(true);
      
      await updateProfile({
        full_name: fullName,
        preferred_language: preferredLanguage
      }).unwrap();
      
      toast({
        title: 'Profile Updated',
        description: 'Your profile has been updated successfully.',
      });
      
      // Refetch profile data
      refetchProfile();
    } catch (error) {
      toast({
        title: 'Update Failed',
        description: 'Failed to update profile. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsUpdating(false);
    }
  };
  
  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };
  
  // Get user initials for avatar fallback
  const getUserInitials = () => {
    const name = profileData?.data?.full_name || profileData?.data?.username || '';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };
  
  // Loading state
  if (isLoadingProfile) {
    return (
      <div className="container mx-auto py-8 max-w-5xl">
        <div className="space-y-6">
          <Skeleton className="h-12 w-1/3" />
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }
  
  // Error state
  if (profileError) {
    return (
      <div className="container mx-auto py-8 max-w-5xl">
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load profile data</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading your profile. Please try again.</p>
            <Button onClick={() => refetchProfile()} className="mt-4">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retry
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="container mx-auto py-8 max-w-5xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">My Profile</h1>
        <p className="text-muted-foreground">Manage your account settings and preferences</p>
      </div>
      
      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile" className="flex items-center">
            <User className="mr-2 h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="organizations" className="flex items-center">
            <Building className="mr-2 h-4 w-4" />
            Organizations
          </TabsTrigger>
          <TabsTrigger value="credits" className="flex items-center">
            <Zap className="mr-2 h-4 w-4" />
            AI Credits
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center">
            <Key className="mr-2 h-4 w-4" />
            Security
          </TabsTrigger>
        </TabsList>
        
        {/* Profile Tab */}
        <TabsContent value="profile" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Update your personal information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col md:flex-row gap-6">
                <div className="flex-shrink-0">
                  <Avatar className="h-24 w-24">
                    <AvatarImage src={profileData?.data?.profile_image_url || ''} alt={profileData?.data?.username} />
                    <AvatarFallback className="text-lg">{getUserInitials()}</AvatarFallback>
                  </Avatar>
                </div>
                
                <div className="space-y-4 flex-grow">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="username">Username</Label>
                      <div className="flex items-center mt-1 border rounded-md px-3 py-2 bg-muted">
                        <User className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{profileData?.data?.username}</span>
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="email">Email</Label>
                      <div className="flex items-center mt-1 border rounded-md px-3 py-2 bg-muted">
                        <Mail className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{profileData?.data?.email}</span>
                        {profileData?.data?.email_verified ? (
                          <Badge className="ml-2" variant="outline">Verified</Badge>
                        ) : (
                          <Badge className="ml-2" variant="outline">Not Verified</Badge>
                        )}
                      </div>
                    </div>
                    
                    <div>
                      <Label htmlFor="fullName">Full Name</Label>
                      <Input
                        id="fullName"
                        value={fullName}
                        onChange={(e) => setFullName(e.target.value)}
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="preferredLanguage">Preferred Language</Label>
                      <Input
                        id="preferredLanguage"
                        value={preferredLanguage}
                        onChange={(e) => setPreferredLanguage(e.target.value)}
                        placeholder="e.g., en, fr, es"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end">
              <Button onClick={handleUpdateProfile} disabled={isUpdating}>
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  'Save Changes'
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {/* Organizations Tab */}
        <TabsContent value="organizations" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>My Organizations</CardTitle>
              <CardDescription>Organizations you are a member of</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingOrgs ? (
                <div className="space-y-4">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : orgsError ? (
                <p className="text-destructive">Failed to load organizations</p>
              ) : organizationsData?.data?.length === 0 ? (
                <p className="text-muted-foreground">You are not a member of any organizations.</p>
              ) : (
                <div className="space-y-4">
                  {organizationsData?.data?.map((org) => (
                    <div key={org.id} className="flex items-center justify-between p-4 border rounded-md">
                      <div className="flex items-center">
                        <Building className="h-5 w-5 mr-3 text-primary" />
                        <div>
                          <p className="font-medium">{org.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {org.subscription_tier} Plan
                          </p>
                        </div>
                      </div>
                      <Badge variant={org.owner_id === profileData?.data?.id ? "default" : "outline"}>
                        {org.owner_id === profileData?.data?.id ? "Owner" : "Member"}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* AI Credits Tab */}
        <TabsContent value="credits" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>AI Credits</CardTitle>
              <CardDescription>Manage your AI translation credits</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingCredits || isLoadingOrgs ? (
                <div className="space-y-4">
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                  <Skeleton className="h-12 w-full" />
                </div>
              ) : creditsError || !firstOrgId ? (
                <p className="text-muted-foreground">
                  {!firstOrgId 
                    ? "You need to be a member of an organization to use AI credits." 
                    : "Failed to load AI credits information."}
                </p>
              ) : creditsData?.data ? (
                <div className="space-y-6">
                  <div>
                    <div className="flex justify-between mb-2">
                      <p className="text-sm font-medium">Credits Usage</p>
                      <p className="text-sm">
                        {creditsData.data.remaining} / {creditsData.data.total} credits remaining
                      </p>
                    </div>
                    <Progress 
                      value={(creditsData.data.remaining / creditsData.data.total) * 100} 
                      className="h-2"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium mb-1">Organization</p>
                      <p className="flex items-center">
                        <Building className="h-4 w-4 mr-2 text-muted-foreground" />
                        {organizationsData?.data?.[0]?.name || 'N/A'}
                      </p>
                    </div>
                    
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium mb-1">Subscription Tier</p>
                      <p className="flex items-center">
                        <CreditCard className="h-4 w-4 mr-2 text-muted-foreground" />
                        {creditsData.data.subscription_tier}
                      </p>
                    </div>
                    
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium mb-1">Monthly Allowance</p>
                      <p className="flex items-center">
                        <Zap className="h-4 w-4 mr-2 text-muted-foreground" />
                        {creditsData.data.monthly_allowance} credits
                      </p>
                    </div>
                    
                    <div className="p-4 border rounded-md">
                      <p className="text-sm font-medium mb-1">Next Reset Date</p>
                      <p className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        {formatDate(creditsData.data.reset_date)}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex justify-end">
                    <Button variant="outline" onClick={() => window.location.href = `/organizations/${organizationsData?.data?.[0]?.slug}`}>
                      Manage Organization Credits
                    </Button>
                  </div>
                </div>
              ) : (
                <p className="text-muted-foreground">No AI credits information available.</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Security Tab */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Security Settings</CardTitle>
              <CardDescription>Manage your account security</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Change Password</p>
                    <p className="text-sm text-muted-foreground">Update your account password</p>
                  </div>
                  <Button variant="outline">Change Password</Button>
                </div>
              </div>
              
              <div className="p-4 border rounded-md">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium">Email Verification</p>
                    <p className="text-sm text-muted-foreground">
                      {profileData?.data?.email_verified 
                        ? "Your email is verified" 
                        : "Verify your email address"}
                    </p>
                  </div>
                  {!profileData?.data?.email_verified && (
                    <Button variant="outline">Verify Email</Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
