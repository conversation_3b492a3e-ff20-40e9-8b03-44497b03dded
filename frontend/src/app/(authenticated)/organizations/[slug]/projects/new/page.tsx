'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import {
  useGetOrganizationBySlugQuery,
  useCreateProjectMutation,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/components/ui/use-toast';
import { ArrowLeft, Loader2 } from 'lucide-react';

export default function NewProjectPage({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
  });

  // Create local variable to avoid direct access to params properties
  // This approach works with the current version of Next.js and avoids the warning
  const orgSlug = params?.slug;

  // Fetch organization by slug
  const {
    data: organizationData,
    isLoading: isLoadingOrg,
    error: orgError,
  } = useGetOrganizationBySlugQuery(orgSlug);

  // Create project mutation
  const [createProject, { isLoading: isCreating }] = useCreateProjectMutation();

  // Auto-generate slug from name
  useEffect(() => {
    if (formData.name) {
      const slugValue = formData.name
        .toLowerCase()
        .replace(/\s+/g, '-')
        .replace(/[^a-z0-9-]/g, '');

      setFormData(prev => ({ ...prev, slug: slugValue }));
    }
  }, [formData.name]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!organizationData?.data?.id) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: 'Organization not found',
      });
      return;
    }

    try {
      const result = await createProject({
        organization_id: organizationData.data.id,
        name: formData.name,
        slug: formData.slug,
        description: formData.description || undefined,
      }).unwrap();

      if (result.success) {
        toast({
          variant: 'success',
          title: 'Project Created',
          description: `Project "${formData.name}" has been created successfully.`,
        });

        // Navigate to the new project
        router.push(`/organizations/${orgSlug}/projects/${result.data.slug}`);
      } else {
        toast({
          variant: 'destructive',
          title: 'Error',
          description: result.message || 'Failed to create project. Please try again.',
        });
      }
    } catch (error: any) {
      toast({
        variant: 'destructive',
        title: 'Error',
        description: error.data?.message || 'An unexpected error occurred. Please try again.',
      });
    }
  };

  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Handle back button click
  const handleBackClick = () => {
    router.push(`/organizations/${orgSlug}`);
  };

  // Loading state
  if (isLoadingOrg) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </div>
    );
  }

  // Error state
  if (orgError || !organizationData?.data) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Button variant="outline" onClick={handleBackClick} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Organization
        </Button>

        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>
              {orgError ? 'Failed to load organization' : 'Organization not found'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading the organization details. Please try again later.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const organization = organizationData.data;

  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={handleBackClick} className="mr-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">Create New Project</h1>
      </div>

      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>New Project</CardTitle>
          <CardDescription>
            Create a new translation project in {organization.name}
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Project Name</Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="My Translation Project"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="slug">Project Slug</Label>
              <Input
                id="slug"
                name="slug"
                value={formData.slug}
                onChange={handleChange}
                placeholder="my-translation-project"
                required
              />
              <p className="text-xs text-muted-foreground">
                The slug is used in URLs and API endpoints. Use only lowercase letters, numbers, and hyphens.
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                placeholder="Describe your project..."
                rows={4}
              />
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                </>
              ) : (
                'Create Project'
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
}
