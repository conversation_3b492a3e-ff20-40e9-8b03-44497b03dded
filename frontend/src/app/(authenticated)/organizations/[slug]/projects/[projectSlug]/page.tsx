'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  useGetOrganizationBySlugQuery,
  useGetProjectBySlugQuery,
  useListProjectsQuery,
  useGetProjectQuery,
} from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  ArrowLeft,
  FileText,
  Settings,
  Globe,
  Languages,
} from 'lucide-react';
import { formatDate } from '@/lib/utils';
import { TranslationsTab } from '@/components/project/TranslationsTab';
import { LocalesTab } from '@/components/project/LocalesTab';

export default function ProjectDetailPage({
  params
}: {
  params: { slug: string; projectSlug: string }
}) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  // Create local variables to avoid direct access to params properties
  // This approach works with the current version of Next.js and avoids the warning
  const orgSlug = params?.slug;
  const projSlug = params?.projectSlug;

  // Fetch organization by slug
  const {
    data: organizationData,
    isLoading: isLoadingOrg,
    error: orgError,
  } = useGetOrganizationBySlugQuery(orgSlug);

  // Fetch project by slug directly
  const {
    data: projectData,
    isLoading: isLoadingProject,
    error: projectError,
    refetch: refetchProject,
  } = useGetProjectBySlugQuery(
    {
      slug: projSlug,
      organization_id: organizationData?.data?.id || ''
    },
    { skip: !organizationData?.data?.id }
  );

  // Handle back button click
  const handleBackClick = () => {
    router.push(`/organizations/${orgSlug}`);
  };

  // Handle tab change
  const handleTabChange = (value: string) => {
    setActiveTab(value);
  };

  // Loading state
  if (isLoadingOrg || isLoadingProject) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex items-center mb-6">
          <Skeleton className="h-10 w-10 mr-4" />
          <Skeleton className="h-10 w-48" />
        </div>
        <Skeleton className="h-12 w-full mb-6" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Skeleton className="h-64 w-full" />
          <Skeleton className="h-64 w-full" />
        </div>
      </div>
    );
  }

  // Error state
  if (orgError || projectError) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Button variant="outline" onClick={handleBackClick} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Organization
        </Button>

        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>
              {orgError
                ? 'Failed to load organization'
                : 'Failed to load project details'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading the details. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => {
              if (orgError) {
                // Refetch organization data
                window.location.reload();
              } else if (projectError) {
                refetchProject();
              }
            }}>Retry</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  const organization = organizationData?.data;
  const projectDetails = projectData?.data;

  // If we have the organization but couldn't find the project with the matching slug
  if (organization && projectError) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Button variant="outline" onClick={handleBackClick} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Organization
        </Button>

        <Card>
          <CardHeader>
            <CardTitle>Project Not Found</CardTitle>
            <CardDescription>
              No project with slug "{projSlug}" found in this organization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>The project you're looking for doesn't exist or you don't have access to it.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleBackClick}>
              Go Back
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  if (!organization || !projectDetails) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Button variant="outline" onClick={handleBackClick} className="mb-6">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Organization
        </Button>

        <Card>
          <CardHeader>
            <CardTitle>Not Found</CardTitle>
            <CardDescription>
              {!organization ? 'Organization not found' : 'Project not found'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>The resource you're looking for doesn't exist or you don't have access to it.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleBackClick}>
              Go Back
            </Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={handleBackClick} className="mr-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold flex items-center">
          <FileText className="mr-3 h-6 w-6 text-primary" />
          {projectDetails.name}
        </h1>
      </div>

      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="locales">Locales</TabsTrigger>
          <TabsTrigger value="translations">Translations</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  Project Details
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Name:</p>
                  <p>{projectDetails.name}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Slug:</p>
                  <p>{projectDetails.slug}</p>
                </div>
                {projectDetails.description && (
                  <div>
                    <p className="text-sm font-medium">Description:</p>
                    <p>{projectDetails.description}</p>
                  </div>
                )}
                <div>
                  <p className="text-sm font-medium">Created:</p>
                  <p>{projectDetails.created_at ? formatDate(projectDetails.created_at) : 'Unknown'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium">Organization:</p>
                  <p>{organization.name}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common project tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button className="w-full justify-start" onClick={() => setActiveTab('locales')}>
                  <Globe className="mr-2 h-4 w-4" />
                  Manage Locales
                </Button>
                <Button className="w-full justify-start" onClick={() => setActiveTab('translations')}>
                  <Languages className="mr-2 h-4 w-4" />
                  Manage Translations
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Settings className="mr-2 h-4 w-4" />
                  Project Settings
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="locales">
          <LocalesTab projectId={projectDetails.id} />
        </TabsContent>

        <TabsContent value="translations">
          <TranslationsTab projectId={projectDetails.id} projectSlug={projSlug} />
        </TabsContent>

        <TabsContent value="settings">
          <Card>
            <CardHeader>
              <CardTitle>Settings</CardTitle>
              <CardDescription>Manage project settings</CardDescription>
            </CardHeader>
            <CardContent>
              <p>Settings feature coming soon...</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
