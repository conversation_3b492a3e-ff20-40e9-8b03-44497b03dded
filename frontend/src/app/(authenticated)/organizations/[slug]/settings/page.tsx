'use client';

import { useState } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useGetOrganizationBySlugQuery } from '@/lib/redux/api/endpoints/organizationApi';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft } from 'lucide-react';
import OrganizationSettingsTab from "@/components/settings/OrganizationSettingsTab";
import BillingTab from "@/components/settings/BillingTab";
import MembersTab from "@/components/settings/MembersTab";
import ApiKeysTab from "@/components/settings/ApiKeysTab";
import PermissionGroupsTab from "@/components/settings/PermissionGroupsTab";
import AuditLogsTab from "@/components/settings/AuditLogsTab";

export default function SettingsPage({ params }: { params: { slug: string } }) {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('organization');
  
  // Create local variable to avoid direct access to params properties
  const orgSlug = params?.slug;
  
  // Fetch organization by slug
  const {
    data: organizationData,
    isLoading: isLoadingOrg,
    error: orgError,
  } = useGetOrganizationBySlugQuery(orgSlug);
  
  const organization = organizationData?.data;
  
  // Handle back button click
  const handleBackClick = () => {
    router.push(`/organizations/${orgSlug}`);
  };
  
  if (isLoadingOrg) {
    return (
      <div className="container py-6">
        <Skeleton className="h-10 w-1/4 mb-6" />
        <Skeleton className="h-6 w-1/2 mb-10" />
        <div className="grid gap-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    );
  }
  
  if (!organization) {
    return (
      <div className="container py-6">
        <h1 className="text-3xl font-bold mb-6">Organization Not Found</h1>
        <p>The organization you're looking for doesn't exist or you don't have access to it.</p>
      </div>
    );
  }
  
  return (
    <div className="container py-6">
      <div className="flex items-center mb-6">
        <Button variant="outline" onClick={handleBackClick} className="mr-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        <h1 className="text-3xl font-bold">Settings</h1>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="organization">Organization</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
          <TabsTrigger value="members">Members</TabsTrigger>
          <TabsTrigger value="api-keys">API Keys</TabsTrigger>
          <TabsTrigger value="permission-groups">Permission Groups</TabsTrigger>
          <TabsTrigger value="audit-logs">Audit Logs</TabsTrigger>
        </TabsList>
        
        <TabsContent value="organization">
          <OrganizationSettingsTab slug={orgSlug} />
        </TabsContent>
        
        <TabsContent value="billing">
          <BillingTab slug={orgSlug} />
        </TabsContent>
        
        <TabsContent value="members">
          <MembersTab slug={orgSlug} />
        </TabsContent>
        
        <TabsContent value="api-keys">
          <ApiKeysTab slug={orgSlug} />
        </TabsContent>
        
        <TabsContent value="permission-groups">
          <PermissionGroupsTab />
        </TabsContent>
        
        <TabsContent value="audit-logs">
          <AuditLogsTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}
