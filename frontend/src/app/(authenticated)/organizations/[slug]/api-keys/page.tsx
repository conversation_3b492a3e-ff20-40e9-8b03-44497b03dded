'use client';

import { useState } from 'react';
import { useParams } from 'next/navigation';
import { useGetOrganizationBySlugQuery } from '@/lib/redux/api/endpoints/organizationApi';
import {
  useListApiKeysQuery,
  useRevokeApiKeyMutation,
  useGetApiKeyUsageQuery
} from '@/lib/redux/api/endpoints/apiKeyApi';
import ApiKeyUsageCharts from '@/components/api-keys/ApiKeyUsageCharts';
import CreateApiKeyForm from '@/components/api-keys/CreateApiKeyForm';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { formatDate } from '@/lib/utils';
import { Key, Plus, Trash2, BarChart, RefreshCw, BookOpen } from 'lucide-react';
import Link from 'next/link';

export default function ApiKeysPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { toast } = useToast();

  // State for the create API key dialog
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // State for API key usage
  const [selectedKeyId, setSelectedKeyId] = useState<string | null>(null);
  const [usageDays, setUsageDays] = useState(30);

  // Get organization data
  const { data: organizationData, isLoading: isLoadingOrg } = useGetOrganizationBySlugQuery(slug);
  const organization = organizationData?.data;

  // Get API keys
  const { data: apiKeysData, isLoading: isLoadingKeys, refetch: refetchApiKeys } = useListApiKeysQuery(
    organization?.id || '',
    { skip: !organization?.id }
  );

  // Mutations
  const [revokeApiKey, { isLoading: isRevokingKey }] = useRevokeApiKeyMutation();

  // Get API key usage
  const { data: apiKeyUsageData, isLoading: isLoadingUsage, refetch: refetchUsage } = useGetApiKeyUsageQuery(
    {
      organizationId: organization?.id || '',
      keyId: selectedKeyId || '',
      days: usageDays
    },
    { skip: !organization?.id || !selectedKeyId }
  );



  // Handle revoking an API key
  const handleRevokeApiKey = async (keyId: string) => {
    if (!organization?.id) return;

    try {
      const result = await revokeApiKey({
        organizationId: organization.id,
        keyId
      }).unwrap();

      if (result.success) {
        toast({
          title: "Success",
          description: "API key revoked successfully"
        });
        refetchApiKeys();
      } else {
        toast({
          title: "Error",
          description: result.message || "Failed to revoke API key",
          variant: "destructive"
        });
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to revoke API key",
        variant: "destructive"
      });
    }
  };

  // Handle closing the create dialog
  const handleCloseCreateDialog = () => {
    setIsCreateDialogOpen(false);
  };

  // Handle viewing API key usage
  const handleViewApiKeyUsage = (keyId: string) => {
    if (selectedKeyId === keyId) {
      // If the same key is clicked again, close the usage view
      setSelectedKeyId(null);
    } else {
      setSelectedKeyId(keyId);
      refetchUsage();
    }
  };

  // Handle refreshing API key usage data
  const handleRefreshUsage = () => {
    if (selectedKeyId) {
      refetchUsage();
    }
  };

  // Handle changing the usage time period
  const handleChangeDays = (days: number) => {
    setUsageDays(days);
    refetchUsage();
  };

  if (isLoadingOrg) {
    return (
      <div className="container py-10">
        <Skeleton className="h-10 w-1/4 mb-6" />
        <Skeleton className="h-6 w-1/2 mb-10" />
        <div className="grid gap-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container py-10">
        <h1 className="text-3xl font-bold mb-6">Organization Not Found</h1>
        <p>The organization you're looking for doesn't exist or you don't have access to it.</p>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">API Keys</h1>
          <p className="text-muted-foreground">
            Manage API keys for {organization.name}
          </p>
        </div>
        <div className="flex space-x-2">
          <Link href={`/organizations/${slug}/api-keys/documentation`}>
            <Button variant="outline">
              <BookOpen className="mr-2 h-4 w-4" />
              Documentation
            </Button>
          </Link>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="mr-2 h-4 w-4" />
            Create API Key
          </Button>
        </div>
      </div>

      <Card className="mb-6">
        <CardHeader>
          <CardTitle>API Key Authentication</CardTitle>
          <CardDescription>
            Use API keys to authenticate requests to the API
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p>
              API keys provide secure access to the ADC Multi-Languages API. Include your API key in the <code className="bg-muted px-1 py-0.5 rounded">X-API-Key</code> header with each request.
            </p>
            <div className="bg-muted p-4 rounded-md">
              <pre className="text-sm overflow-x-auto">
                {`curl -X GET "https://api.adcmultilanguages.com/api/projects" \\
  -H "X-API-Key: your_api_key_here"`}
              </pre>
            </div>
            <div className="flex justify-end">
              <Link href={`/organizations/${slug}/api-keys/documentation`}>
                <Button variant="link" className="px-0">
                  <BookOpen className="mr-2 h-4 w-4" />
                  View full documentation
                </Button>
              </Link>
            </div>
          </div>
        </CardContent>
      </Card>

      {isLoadingKeys ? (
        <div className="grid gap-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      ) : apiKeysData?.data && apiKeysData.data.length > 0 ? (
        <div className="grid gap-6">
          {apiKeysData.data.map((apiKey) => (
            <Card key={apiKey.id}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Key className="mr-2 h-5 w-5 text-primary" />
                  {apiKey.name}
                </CardTitle>
                <CardDescription>
                  Created on {formatDate(apiKey.created_at)}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Prefix</p>
                      <p className="text-sm font-mono">{apiKey.prefix}...</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Permissions</p>
                      <p className="text-sm">{apiKey.permissions.join(', ')}</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm font-medium">Status</p>
                      <p className={`text-sm ${apiKey.is_active ? 'text-green-500' : 'text-red-500'}`}>
                        {apiKey.is_active ? 'Active' : 'Revoked'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Last Used</p>
                      <p className="text-sm">
                        {apiKey.last_used_at ? formatDate(apiKey.last_used_at) : 'Never'}
                      </p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div>
                      <p className="text-sm font-medium">Rate Limit</p>
                      <p className="text-sm">
                        {apiKey.rate_limit
                          ? `${apiKey.rate_limit} requests per ${apiKey.rate_limit_period || 'minute'}`
                          : 'No limit'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm font-medium">Expires</p>
                      <p className="text-sm">
                        {apiKey.expires_at ? formatDate(apiKey.expires_at) : 'Never'}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewApiKeyUsage(apiKey.id)}
                >
                  <BarChart className="mr-2 h-4 w-4" />
                  {selectedKeyId === apiKey.id ? 'Hide Usage' : 'View Usage'}
                </Button>
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={() => handleRevokeApiKey(apiKey.id)}
                  disabled={isRevokingKey || !apiKey.is_active}
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Revoke
                </Button>
              </CardFooter>

              {/* API Key Usage Section */}
              {selectedKeyId === apiKey.id && (
                <div className="px-6 pb-6">
                  <div className="border-t pt-4 mt-2">
                    <div className="flex justify-between items-center mb-4">
                      <h4 className="text-sm font-medium">API Key Usage</h4>
                      <div className="flex items-center space-x-2">
                        <select
                          className="text-xs border rounded p-1"
                          value={usageDays}
                          onChange={(e) => handleChangeDays(Number(e.target.value))}
                        >
                          <option value={7}>Last 7 days</option>
                          <option value={30}>Last 30 days</option>
                          <option value={90}>Last 90 days</option>
                        </select>
                        <Button
                          variant="ghost"
                          size="icon"
                          onClick={handleRefreshUsage}
                          disabled={isLoadingUsage}
                        >
                          <RefreshCw className={`h-4 w-4 ${isLoadingUsage ? 'animate-spin' : ''}`} />
                        </Button>
                      </div>
                    </div>

                    {isLoadingUsage ? (
                      <div className="h-20 flex items-center justify-center">
                        <Skeleton className="h-4 w-32" />
                      </div>
                    ) : apiKeyUsageData?.data ? (
                      <div className="space-y-6">
                        <div className="bg-muted/50 p-4 rounded-md">
                          <div className="text-center">
                            <div className="text-3xl font-bold">{apiKeyUsageData.data.usage_count}</div>
                            <div className="text-xs text-muted-foreground">API calls in the last {apiKeyUsageData.data.days} days</div>
                          </div>
                        </div>

                        {/* Usage Charts */}
                        <ApiKeyUsageCharts
                          organizationId={organization.id}
                          keyId={apiKey.id}
                          days={usageDays}
                        />
                      </div>
                    ) : (
                      <div className="text-center text-sm text-muted-foreground p-4">
                        No usage data available
                      </div>
                    )}
                  </div>
                </div>
              )}
            </Card>
          ))}
        </div>
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>No API Keys</CardTitle>
            <CardDescription>
              You haven't created any API keys yet. Create one to get started.
            </CardDescription>
          </CardHeader>
          <CardFooter>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Create API Key
            </Button>
          </CardFooter>
        </Card>
      )}

      {/* Create API Key Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={handleCloseCreateDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>{newApiKey ? 'API Key Created' : 'Create API Key'}</DialogTitle>
            <DialogDescription>
              {newApiKey
                ? 'Copy your API key now. You won\'t be able to see it again.'
                : 'Create a new API key for programmatic access to the API.'}
            </DialogDescription>
          </DialogHeader>

          {newApiKey ? (
            <div className="space-y-4">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md flex items-start">
                <AlertCircle className="h-5 w-5 text-yellow-500 mr-2 mt-0.5" />
                <p className="text-sm text-yellow-700">
                  This API key will only be displayed once. Please copy it and store it securely.
                </p>
              </div>

              <div className="relative">
                <Input
                  value={newApiKey}
                  readOnly
                  className="pr-10 font-mono"
                />
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute right-0 top-0"
                  onClick={handleCopyApiKey}
                >
                  {hasCopiedKey ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  placeholder="Enter a name for this API key"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <Label>Permissions</Label>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="permissionType" className="text-xs">Permission Type:</Label>
                    <select
                      id="permissionType"
                      className="text-xs border rounded p-1"
                      value={showResourcePermissions ? "resource" : "basic"}
                      onChange={(e) => setShowResourcePermissions(e.target.value === "resource")}
                    >
                      <option value="basic">Basic</option>
                      <option value="resource">Resource-specific</option>
                    </select>
                  </div>
                </div>

                {!showResourcePermissions ? (
                  // Basic permissions
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="read"
                        checked={readPermission}
                        onCheckedChange={(checked) => setReadPermission(checked as boolean)}
                      />
                      <Label htmlFor="read" className="cursor-pointer">Read</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="write"
                        checked={writePermission}
                        onCheckedChange={(checked) => setWritePermission(checked as boolean)}
                      />
                      <Label htmlFor="write" className="cursor-pointer">Write</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="admin"
                        checked={adminPermission}
                        onCheckedChange={(checked) => setAdminPermission(checked as boolean)}
                      />
                      <Label htmlFor="admin" className="cursor-pointer">Admin</Label>
                    </div>
                  </div>
                ) : (
                  // Resource-specific permissions
                  <div className="space-y-4">
                    <div className="border p-3 rounded-md">
                      <h4 className="text-sm font-medium mb-2">Projects</h4>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="projectsRead"
                            checked={projectsRead}
                            onCheckedChange={(checked) => setProjectsRead(checked as boolean)}
                          />
                          <Label htmlFor="projectsRead" className="cursor-pointer text-sm">Read</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="projectsWrite"
                            checked={projectsWrite}
                            onCheckedChange={(checked) => setProjectsWrite(checked as boolean)}
                          />
                          <Label htmlFor="projectsWrite" className="cursor-pointer text-sm">Write</Label>
                        </div>
                      </div>
                    </div>

                    <div className="border p-3 rounded-md">
                      <h4 className="text-sm font-medium mb-2">Translations</h4>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="translationsRead"
                            checked={translationsRead}
                            onCheckedChange={(checked) => setTranslationsRead(checked as boolean)}
                          />
                          <Label htmlFor="translationsRead" className="cursor-pointer text-sm">Read</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="translationsWrite"
                            checked={translationsWrite}
                            onCheckedChange={(checked) => setTranslationsWrite(checked as boolean)}
                          />
                          <Label htmlFor="translationsWrite" className="cursor-pointer text-sm">Write</Label>
                        </div>
                      </div>
                    </div>

                    <div className="border p-3 rounded-md">
                      <h4 className="text-sm font-medium mb-2">Locales</h4>
                      <div className="grid grid-cols-2 gap-2">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="localesRead"
                            checked={localesRead}
                            onCheckedChange={(checked) => setLocalesRead(checked as boolean)}
                          />
                          <Label htmlFor="localesRead" className="cursor-pointer text-sm">Read</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="localesWrite"
                            checked={localesWrite}
                            onCheckedChange={(checked) => setLocalesWrite(checked as boolean)}
                          />
                          <Label htmlFor="localesWrite" className="cursor-pointer text-sm">Write</Label>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label>Rate Limiting</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Input
                      id="rateLimit"
                      type="number"
                      placeholder="Requests"
                      min={1}
                      value={rateLimit || ''}
                      onChange={(e) => setRateLimit(e.target.value ? parseInt(e.target.value) : undefined)}
                    />
                  </div>
                  <div>
                    <select
                      className="w-full h-10 px-3 py-2 text-sm border rounded-md"
                      value={rateLimitPeriod}
                      onChange={(e) => setRateLimitPeriod(e.target.value)}
                    >
                      <option value="minute">Per Minute</option>
                      <option value="hour">Per Hour</option>
                      <option value="day">Per Day</option>
                    </select>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Leave blank for no rate limit
                </p>
              </div>
            </div>
          )}

          <DialogFooter>
            {newApiKey ? (
              <Button onClick={handleCloseCreateDialog}>
                Close
              </Button>
            ) : (
              <Button onClick={handleCreateApiKey} disabled={isCreatingKey || !newKeyName.trim()}>
                {isCreatingKey ? 'Creating...' : 'Create API Key'}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
