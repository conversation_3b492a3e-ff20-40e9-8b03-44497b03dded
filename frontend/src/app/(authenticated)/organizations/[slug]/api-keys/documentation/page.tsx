'use client';

import { useParams } from 'next/navigation';
import { useGetOrganizationBySlugQuery } from '@/lib/redux/api/endpoints/organizationApi';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Button } from '@/components/ui/button';
import { Copy, Check } from 'lucide-react';
import { useState } from 'react';
import { useToast } from '@/components/ui/use-toast';
import Link from 'next/link';

export default function ApiKeyDocumentationPage() {
  const params = useParams();
  const slug = params.slug as string;
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');
  const [copiedSnippets, setCopiedSnippets] = useState<Record<string, boolean>>({});

  // Get organization data
  const { data: organizationData, isLoading: isLoadingOrg } = useGetOrganizationBySlugQuery(slug);
  const organization = organizationData?.data;

  // Handle copying code snippets
  const handleCopyCode = (code: string, id: string) => {
    navigator.clipboard.writeText(code);
    setCopiedSnippets({ ...copiedSnippets, [id]: true });
    toast({
      title: "Copied",
      description: "Code snippet copied to clipboard"
    });
    setTimeout(() => {
      setCopiedSnippets({ ...copiedSnippets, [id]: false });
    }, 3000);
  };

  // Code snippets
  const curlSnippet = `curl -X GET "https://api.adcmultilanguages.com/api/projects" \\
  -H "X-API-Key: your_api_key_here"`;

  const nodeSnippet = `const fetch = require('node-fetch');

async function fetchProjects() {
  const response = await fetch('https://api.adcmultilanguages.com/api/projects', {
    headers: {
      'X-API-Key': 'your_api_key_here'
    }
  });
  
  const data = await response.json();
  console.log(data);
}

fetchProjects();`;

  const pythonSnippet = `import requests

response = requests.get(
    'https://api.adcmultilanguages.com/api/projects',
    headers={'X-API-Key': 'your_api_key_here'}
)

data = response.json()
print(data)`;

  const goSnippet = `package main

import (
	"fmt"
	"io/ioutil"
	"net/http"
)

func main() {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", "https://api.adcmultilanguages.com/api/projects", nil)
	req.Header.Add("X-API-Key", "your_api_key_here")
	
	resp, err := client.Do(req)
	if err != nil {
		fmt.Println("Error:", err)
		return
	}
	defer resp.Body.Close()
	
	body, _ := ioutil.ReadAll(resp.Body)
	fmt.Println(string(body))
}`;

  if (isLoadingOrg) {
    return (
      <div className="container py-10">
        <Skeleton className="h-10 w-1/4 mb-6" />
        <Skeleton className="h-6 w-1/2 mb-10" />
        <div className="grid gap-6">
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    );
  }

  if (!organization) {
    return (
      <div className="container py-10">
        <h1 className="text-3xl font-bold mb-6">Organization Not Found</h1>
        <p>The organization you're looking for doesn't exist or you don't have access to it.</p>
      </div>
    );
  }

  return (
    <div className="container py-10">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">API Key Documentation</h1>
          <p className="text-muted-foreground">
            Learn how to use API keys with {organization.name}
          </p>
        </div>
        <Link href={`/organizations/${slug}/api-keys`}>
          <Button variant="outline">
            Manage API Keys
          </Button>
        </Link>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="authentication">Authentication</TabsTrigger>
          <TabsTrigger value="examples">Code Examples</TabsTrigger>
          <TabsTrigger value="best-practices">Best Practices</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>API Key Overview</CardTitle>
              <CardDescription>
                API keys provide secure access to the ADC Multi-Languages API
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                API keys are used to authenticate requests to the ADC Multi-Languages API. Each API key is associated with your organization and has specific permissions that determine what actions it can perform.
              </p>
              <h3 className="text-lg font-medium mt-4">Key Features</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li>Secure authentication for API requests</li>
                <li>Granular permission control</li>
                <li>Usage tracking and analytics</li>
                <li>Rate limiting to prevent abuse</li>
                <li>Ability to revoke keys if compromised</li>
              </ul>
              <h3 className="text-lg font-medium mt-4">Getting Started</h3>
              <ol className="list-decimal pl-6 space-y-2">
                <li>Create an API key in the <Link href={`/organizations/${slug}/api-keys`} className="text-primary hover:underline">API Keys management page</Link></li>
                <li>Set appropriate permissions for your use case</li>
                <li>Copy the API key (you'll only see it once)</li>
                <li>Use the key in your API requests as shown in the examples</li>
              </ol>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="authentication">
          <Card>
            <CardHeader>
              <CardTitle>Authentication with API Keys</CardTitle>
              <CardDescription>
                How to authenticate your API requests
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p>
                To authenticate your API requests, include your API key in the <code className="bg-muted px-1 py-0.5 rounded">X-API-Key</code> header with each request.
              </p>
              <div className="bg-muted p-4 rounded-md relative">
                <Button
                  variant="ghost"
                  size="icon"
                  className="absolute top-2 right-2"
                  onClick={() => handleCopyCode(curlSnippet, 'auth-curl')}
                >
                  {copiedSnippets['auth-curl'] ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
                <pre className="text-sm overflow-x-auto">{curlSnippet}</pre>
              </div>
              <h3 className="text-lg font-medium mt-4">API Key Format</h3>
              <p>
                API keys follow this format: <code className="bg-muted px-1 py-0.5 rounded">prefix_random_string</code>
              </p>
              <p>
                The prefix helps identify the key, while the random string provides security. Never share your full API key publicly.
              </p>
              <h3 className="text-lg font-medium mt-4">Error Responses</h3>
              <p>
                If authentication fails, you'll receive one of these error responses:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>401 Unauthorized</strong>: Invalid API key or the key has been revoked</li>
                <li><strong>403 Forbidden</strong>: The API key doesn't have permission for the requested resource</li>
                <li><strong>429 Too Many Requests</strong>: The API key has exceeded its rate limit</li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="examples">
          <Card>
            <CardHeader>
              <CardTitle>Code Examples</CardTitle>
              <CardDescription>
                Examples of using API keys in different programming languages
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="text-lg font-medium mb-2">cURL</h3>
                <div className="bg-muted p-4 rounded-md relative">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(curlSnippet, 'curl')}
                  >
                    {copiedSnippets['curl'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <pre className="text-sm overflow-x-auto">{curlSnippet}</pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Node.js</h3>
                <div className="bg-muted p-4 rounded-md relative">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(nodeSnippet, 'node')}
                  >
                    {copiedSnippets['node'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <pre className="text-sm overflow-x-auto">{nodeSnippet}</pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Python</h3>
                <div className="bg-muted p-4 rounded-md relative">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(pythonSnippet, 'python')}
                  >
                    {copiedSnippets['python'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <pre className="text-sm overflow-x-auto">{pythonSnippet}</pre>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-2">Go</h3>
                <div className="bg-muted p-4 rounded-md relative">
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-2 right-2"
                    onClick={() => handleCopyCode(goSnippet, 'go')}
                  >
                    {copiedSnippets['go'] ? (
                      <Check className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                  </Button>
                  <pre className="text-sm overflow-x-auto">{goSnippet}</pre>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="best-practices">
          <Card>
            <CardHeader>
              <CardTitle>API Key Best Practices</CardTitle>
              <CardDescription>
                Security recommendations for using API keys
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <h3 className="text-lg font-medium">Security Best Practices</h3>
              <ul className="list-disc pl-6 space-y-2">
                <li><strong>Keep keys secure</strong>: Never expose API keys in client-side code or public repositories</li>
                <li><strong>Use environment variables</strong>: Store API keys in environment variables, not in your code</li>
                <li><strong>Limit permissions</strong>: Give each API key only the permissions it needs</li>
                <li><strong>Rotate keys regularly</strong>: Create new keys and revoke old ones periodically</li>
                <li><strong>Monitor usage</strong>: Regularly check API key usage for suspicious activity</li>
                <li><strong>Revoke compromised keys</strong>: If a key is compromised, revoke it immediately</li>
              </ul>

              <h3 className="text-lg font-medium mt-4">Rate Limiting</h3>
              <p>
                API keys are subject to rate limits based on your subscription plan. When creating an API key, you can set custom rate limits that are lower than your plan's limits.
              </p>
              <p>
                If you exceed your rate limit, you'll receive a <code className="bg-muted px-1 py-0.5 rounded">429 Too Many Requests</code> response. Implement exponential backoff in your code to handle rate limiting gracefully.
              </p>

              <h3 className="text-lg font-medium mt-4">Error Handling</h3>
              <p>
                Implement proper error handling in your code to handle API key authentication errors. This includes:
              </p>
              <ul className="list-disc pl-6 space-y-2">
                <li>Checking for 401, 403, and 429 status codes</li>
                <li>Implementing retry logic with exponential backoff</li>
                <li>Logging authentication failures for investigation</li>
              </ul>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
