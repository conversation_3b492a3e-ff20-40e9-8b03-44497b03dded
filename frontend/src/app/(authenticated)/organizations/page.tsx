'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useListOrganizationsQuery } from '@/lib/redux/api/endpoints';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { PlusCircle, Building, Users, Calendar } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import CreateOrganizationDialog from '@/components/organizations/CreateOrganizationDialog';

export default function OrganizationsPage() {
  const router = useRouter();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Fetch organizations
  const {
    data: organizationsData,
    isLoading,
    error,
    refetch,
  } = useListOrganizationsQuery();

  // Handle organization click
  const handleOrganizationClick = (organizationSlug: string) => {
    router.push(`/organizations/${organizationSlug}`);
  };

  // Handle create organization
  const handleCreateOrganization = () => {
    setIsCreateDialogOpen(true);
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load organizations</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading your organizations. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => refetch()}>Retry</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold mb-2">Organizations</h1>
          <p className="text-muted-foreground">Manage your translation organizations</p>
        </div>

        <Button onClick={handleCreateOrganization}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create Organization
        </Button>
      </div>

      {organizationsData?.data?.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No Organizations</CardTitle>
            <CardDescription>You don't have any organizations yet</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Create your first organization to get started with translations.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleCreateOrganization}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Organization
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {organizationsData?.data?.map((organization) => (
            <Card
              key={organization.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleOrganizationClick(organization.slug)}
            >
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Building className="mr-2 h-5 w-5 text-primary" />
                  {organization.name}
                </CardTitle>
                <CardDescription>{organization.slug}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center text-sm text-muted-foreground mb-2">
                  <Users className="mr-2 h-4 w-4" />
                  Subscription: {organization.subscription_tier || 'Free'}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="mr-2 h-4 w-4" />
                  Created: {organization.created_at ? formatDate(organization.created_at) : 'Unknown'}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Organization Dialog */}
      <CreateOrganizationDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onSuccess={() => refetch()}
      />
    </div>
  );
}
