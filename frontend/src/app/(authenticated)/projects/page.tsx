'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  useListProjectsQuery,
  useListOrganizationsQuery,
} from '@/lib/redux/api/endpoints';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { PlusCircle, FolderOpen, Globe, Calendar } from 'lucide-react';
import { formatDate } from '@/lib/utils';
import CreateProjectDialog from '@/components/projects/CreateProjectDialog';

export default function ProjectsPage() {
  const router = useRouter();
  const [selectedOrganization, setSelectedOrganization] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Fetch organizations
  const {
    data: organizationsData,
    isLoading: isLoadingOrganizations,
    error: organizationsError
  } = useListOrganizationsQuery();

  // Fetch projects based on selected organization
  const {
    data: projectsData,
    isLoading: isLoadingProjects,
    error: projectsError
  } = useListProjectsQuery(
    { organization_id: selectedOrganization || undefined },
    { skip: !selectedOrganization }
  );

  // Set the first organization as selected when data is loaded
  if (organizationsData?.data?.length && !selectedOrganization) {
    setSelectedOrganization(organizationsData.data[0].id);
  }

  // Handle organization change
  const handleOrganizationChange = (value: string) => {
    setSelectedOrganization(value);
  };

  // Handle project click
  const handleProjectClick = (projectSlug: string) => {
    // Find the organization slug for the selected organization
    const selectedOrg = organizationsData?.data?.find(org => org.id === selectedOrganization);
    if (selectedOrg) {
      router.push(`/organizations/${selectedOrg.slug}/projects/${projectSlug}`);
    }
  };

  // Handle create project
  const handleCreateProject = () => {
    setIsCreateDialogOpen(true);
  };

  // Loading state
  if (isLoadingOrganizations) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <div className="flex justify-between items-center mb-6">
          <Skeleton className="h-10 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      </div>
    );
  }

  // Error state
  if (organizationsError) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load organizations</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading your organizations. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/dashboard')}>Return to Dashboard</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  // No organizations state
  if (organizationsData?.data?.length === 0) {
    return (
      <div className="container mx-auto p-4 md:p-8">
        <Card className="w-full max-w-md mx-auto">
          <CardHeader>
            <CardTitle>No Organizations</CardTitle>
            <CardDescription>You don't have any organizations yet</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Create an organization to start managing your translation projects.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/organizations/create')}>Create Organization</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4 md:p-8">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div>
          <h1 className="text-3xl font-bold mb-2">Projects</h1>
          <p className="text-muted-foreground">Manage your translation projects</p>
        </div>

        <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
          {organizationsData?.data?.length > 1 && (
            <Select value={selectedOrganization || ''} onValueChange={handleOrganizationChange}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Select organization" />
              </SelectTrigger>
              <SelectContent>
                {organizationsData?.data?.map((org) => (
                  <SelectItem key={org.id} value={org.id}>
                    {org.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}

          <Button onClick={handleCreateProject}>
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Project
          </Button>
        </div>
      </div>

      {isLoadingProjects ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} className="h-64 w-full" />
          ))}
        </div>
      ) : projectsError ? (
        <Card>
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load projects</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading your projects. Please try again later.</p>
          </CardContent>
        </Card>
      ) : projectsData?.data?.length === 0 ? (
        <Card>
          <CardHeader>
            <CardTitle>No Projects</CardTitle>
            <CardDescription>You don't have any projects yet</CardDescription>
          </CardHeader>
          <CardContent>
            <p>Create your first project to get started with translations.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={handleCreateProject}>
              <PlusCircle className="mr-2 h-4 w-4" />
              Create Project
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projectsData?.data?.map((project) => (
            <Card
              key={project.id}
              className="cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleProjectClick(project.slug)}
            >
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FolderOpen className="mr-2 h-5 w-5 text-primary" />
                  {project.name}
                </CardTitle>
                <CardDescription>{project.description || 'No description'}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center text-sm text-muted-foreground mb-2">
                  <Globe className="mr-2 h-4 w-4" />
                  Default locale: {project.default_locale || 'Not set'}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="mr-2 h-4 w-4" />
                  Created: {project.created_at ? formatDate(project.created_at) : 'Unknown'}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Create Project Dialog */}
      <CreateProjectDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        organizationId={selectedOrganization || ''}
      />
    </div>
  );
}
