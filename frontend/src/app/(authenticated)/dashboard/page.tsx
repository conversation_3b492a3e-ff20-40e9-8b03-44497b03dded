'use client';

import { useRouter } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { useGetProfileQuery } from '@/lib/redux/api/endpoints/userApi';
import { useListProjectsQuery, useListOrganizationsQuery } from '@/lib/redux/api/endpoints';
import { Skeleton } from '@/components/ui/skeleton';
import { Building, FolderOpen, Globe, Languages } from 'lucide-react';

export default function Dashboard() {
  const router = useRouter();
  const { data: session } = useSession();
  const { data: profileData, isLoading: isLoadingProfile, error: profileError } = useGetProfileQuery();

  // Fetch organizations and projects for the dashboard
  const { data: organizationsData, isLoading: isLoadingOrgs } = useListOrganizationsQuery();
  const { data: projectsData, isLoading: isLoadingProjects } = useListProjectsQuery(
    { organization_id: organizationsData?.data?.[0]?.id },
    { skip: !organizationsData?.data?.[0]?.id }
  );

  const isLoading = isLoadingProfile || isLoadingOrgs || isLoadingProjects;
  
  if (isLoading) {
    return (
      <div className="flex flex-col p-4 md:p-8">
        <div className="container mx-auto max-w-6xl">
          <div className="mb-8">
            <Skeleton className="h-12 w-48 mb-4" />
            <Skeleton className="h-6 w-96" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Skeleton className="h-64 w-full" />
            <Skeleton className="h-64 w-full" />
          </div>
        </div>
      </div>
    );
  }
  
  if (profileError) {
    return (
      <div className="flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Error</CardTitle>
            <CardDescription>Failed to load dashboard data</CardDescription>
          </CardHeader>
          <CardContent>
            <p>There was an error loading your profile. Please try again later.</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => router.push('/')}>Return to Home</Button>
          </CardFooter>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="flex flex-col p-4 md:p-8">
      <div className="container mx-auto max-w-6xl">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Welcome, {profileData?.data?.username || session?.user?.name || 'User'}</h1>
          <p className="text-muted-foreground">Manage your translations and projects</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5 text-primary" />
                Organizations
              </CardTitle>
              <CardDescription>
                Manage your translation organizations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                {organizationsData?.data?.length
                  ? `You have ${organizationsData.data.length} organization(s).`
                  : 'You don\'t have any organizations yet.'}
              </p>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={() => router.push('/organizations')}
              >
                View Organizations
              </Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FolderOpen className="mr-2 h-5 w-5 text-primary" />
                Projects
              </CardTitle>
              <CardDescription>
                Manage your translation projects
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                {projectsData?.data?.length
                  ? `You have ${projectsData.data.length} project(s).`
                  : 'You don\'t have any projects yet.'}
              </p>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                onClick={() => router.push('/projects')}
              >
                View Projects
              </Button>
            </CardFooter>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Languages className="mr-2 h-5 w-5 text-primary" />
                Translations
              </CardTitle>
              <CardDescription>
                Manage your translations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="mb-4">
                Access and manage translations across all your projects.
              </p>
            </CardContent>
            <CardFooter>
              <Button
                className="w-full"
                variant="outline"
                onClick={() => router.push('/translations')}
              >
                View Translations
              </Button>
            </CardFooter>
          </Card>
        </div>
      </div>
    </div>
  );
}
