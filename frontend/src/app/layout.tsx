import type { Metada<PERSON> } from 'next';
import './globals.css';

import { ReduxProvider } from '@/lib/redux/provider';
import { AuthProvider } from '@/lib/auth/AuthProvider';
import { StripeProvider } from '@/components/providers/StripeProvider';
import { Toaster } from '@/components/ui/toaster';

export const metadata: Metadata = {
  title: 'ADC Multi-Languages',
  description: 'A multi-language translation platform',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="font-sans bg-background text-foreground flex flex-col min-h-screen items-center  w-full">
        <ReduxProvider>
          <AuthProvider>
            <StripeProvider>
              {children}
            </StripeProvider>
          </AuthProvider>
        </ReduxProvider>
        <Toaster />
      </body>
    </html>
  );
}
