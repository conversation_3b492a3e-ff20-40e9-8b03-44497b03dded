#!/bin/sh

# Check if eslint is installed
if command -v bun &> /dev/null && [ -f "$(git rev-parse --show-toplevel)/package.json" ]; then
  echo "Running ESLint..."
  bun run eslint . --ext .js,.ts,.tsx
  if [ $? -ne 0 ]; then
    echo "ESLint failed, fix the errors before committing."
    exit 1
  fi
fi

# Check if cargo is installed
if command -v cargo &> /dev/null && [ -f "$(git rev-parse --show-toplevel)/Cargo.toml" ]; then
  echo "Running Cargo check..."
  cargo check
  if [ $? -ne 0 ]; then
    echo "Cargo check failed, fix the errors before committing."
    exit 1
  fi
fi

exit 0
