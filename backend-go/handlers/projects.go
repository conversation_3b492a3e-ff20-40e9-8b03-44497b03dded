package handlers

import (
	"fmt"
	"strings"
	"time"

	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

// ProjectHandler handles project-related routes
type ProjectHandler struct{}

// NewProjectHandler creates a new project handler
func NewProjectHandler() *ProjectHandler {
	return &ProjectHandler{}
}

// Request structures
type CreateProjectRequest struct {
	Name           string  `json:"name" binding:"required,min=1,max=100"`
	Slug           string  `json:"slug" binding:"required,min=1,max=50"`
	Description    *string `json:"description"`
	OrganizationID string  `json:"organization_id" binding:"required"`
	DefaultLocale  *string `json:"default_locale"`
	IsPublic       *bool   `json:"is_public"`
}

type UpdateProjectRequest struct {
	Name          *string `json:"name"`
	Slug          *string `json:"slug"`
	Description   *string `json:"description"`
	DefaultLocale *string `json:"default_locale"`
	IsPublic      *bool   `json:"is_public"`
}

type AddProjectLocaleRequest struct {
	LocaleCode string `json:"locale_code" binding:"required"`
}

type CreateResourceRequest struct {
	Name        string  `json:"name" binding:"required,min=1,max=100"`
	Type        string  `json:"type" binding:"required"`
	Path        *string `json:"path"`
	Description *string `json:"description"`
}

// Response structures
type ProjectResponse struct {
	ID             string    `json:"id"`
	Name           string    `json:"name"`
	Slug           string    `json:"slug"`
	Description    *string   `json:"description"`
	OrganizationID string    `json:"organization_id"`
	DefaultLocale  *string   `json:"default_locale"`
	IsPublic       *bool     `json:"is_public"`
	CreatedBy      *string   `json:"created_by"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
}

type ProjectLocaleResponse struct {
	ID        string    `json:"id"`
	ProjectID string    `json:"project_id"`
	LocaleID  string    `json:"locale_id"`
	Code      string    `json:"code"`
	Name      string    `json:"name"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
}

type ResourceResponse struct {
	ID          string    `json:"id"`
	ProjectID   string    `json:"project_id"`
	Name        string    `json:"name"`
	Type        string    `json:"type"`
	Path        *string   `json:"path"`
	Description *string   `json:"description"`
	CreatedBy   *string   `json:"created_by"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ListProjects handles GET /api/projects
func (h *ProjectHandler) ListProjects(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	// Get organization_id query parameter
	organizationIDStr := c.Query("organization_id")

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get pagination parameters
	pagination := utils.GetPaginationParams(c)

	// Get user's organizations
	var userOrgIDs []uuid.UUID
	if err := db.Table("organization_members").
		Select("organization_id").
		Where("user_id = ? AND is_active = ?", user.ID, true).
		Pluck("organization_id", &userOrgIDs).Error; err != nil {
		utils.LogError(c, "Failed to get user organizations", err, nil)
		utils.InternalServerError(c, "Failed to retrieve user organizations")
		return
	}

	if len(userOrgIDs) == 0 {
		utils.PaginatedResponse(c, []ProjectResponse{}, pagination, 0)
		return
	}

	// Build query
	query := db.Model(&models.Project{}).Where("organization_id IN ?", userOrgIDs)

	// Filter by organization if specified
	if organizationIDStr != "" {
		orgID, err := uuid.Parse(organizationIDStr)
		if err != nil {
			utils.BadRequest(c, "Invalid organization_id format")
			return
		}

		// Check if user has access to this organization
		hasAccess := false
		for _, userOrgID := range userOrgIDs {
			if userOrgID == orgID {
				hasAccess = true
				break
			}
		}

		if !hasAccess {
			utils.Forbidden(c, "You don't have access to this organization")
			return
		}

		query = query.Where("organization_id = ?", orgID)
	}

	// Count total projects
	var total int64
	if err := query.Count(&total).Error; err != nil {
		utils.LogError(c, "Failed to count projects", err, nil)
		utils.InternalServerError(c, "Failed to retrieve projects")
		return
	}

	// Get projects with pagination
	var projects []models.Project
	if err := query.Preload("Organization").Preload("Creator").
		Limit(int(pagination.PerPage)).
		Offset(int(pagination.Offset)).
		Find(&projects).Error; err != nil {
		utils.LogError(c, "Failed to list projects", err, nil)
		utils.InternalServerError(c, "Failed to retrieve projects")
		return
	}

	// Convert to response format
	response := make([]ProjectResponse, len(projects))
	for i, project := range projects {
		response[i] = ProjectResponse{
			ID:             project.ID.String(),
			Name:           project.Name,
			Slug:           project.Slug,
			Description:    project.Description,
			OrganizationID: project.OrganizationID.String(),
			DefaultLocale:  project.DefaultLocale,
			IsPublic:       project.IsPublic,
			CreatedBy:      uuidPtrToStringPtr(project.CreatedBy),
			CreatedAt:      project.CreatedAt,
			UpdatedAt:      project.UpdatedAt,
		}
	}

	utils.PaginatedResponse(c, response, pagination, uint64(total))
}

// CreateProject handles POST /api/projects
func (h *ProjectHandler) CreateProject(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	var req CreateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Validate slug format
	if !isValidSlug(req.Slug) {
		utils.BadRequest(c, "Slug must contain only lowercase letters, numbers, and hyphens")
		return
	}

	// Parse organization ID
	orgID, err := uuid.Parse(req.OrganizationID)
	if err != nil {
		utils.BadRequest(c, "Invalid organization_id format")
		return
	}

	// Parse user ID
	userID, err := uuid.Parse(user.ID)
	if err != nil {
		utils.BadRequest(c, "Invalid user ID")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user has access to the organization
	var member models.OrganizationMember
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?",
		orgID, user.ID, true).First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have access to this organization")
			return
		}
		utils.LogError(c, "Failed to check organization membership", err, nil)
		utils.InternalServerError(c, "Failed to verify organization access")
		return
	}

	// Check if project with same slug already exists in this organization
	var existingProject models.Project
	if err := db.Where("organization_id = ? AND slug = ?", orgID, req.Slug).
		First(&existingProject).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("Project with slug '%s' already exists in this organization", req.Slug))
		return
	}

	// Create project
	project := models.Project{
		Name:           req.Name,
		Slug:           req.Slug,
		Description:    req.Description,
		OrganizationID: orgID,
		DefaultLocale:  req.DefaultLocale,
		IsPublic:       req.IsPublic,
		CreatedBy:      &userID,
	}

	if err := db.Create(&project).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "unique constraint") {
			utils.Conflict(c, "Project with this name or slug already exists in this organization")
			return
		}
		utils.LogError(c, "Failed to create project", err, nil)
		utils.InternalServerError(c, "Failed to create project")
		return
	}

	// Reload project with relations
	if err := db.Preload("Organization").Preload("Creator").
		Where("id = ?", project.ID).First(&project).Error; err != nil {
		utils.LogError(c, "Failed to reload created project", err, nil)
		utils.InternalServerError(c, "Failed to retrieve created project")
		return
	}

	// Convert to response format
	response := ProjectResponse{
		ID:             project.ID.String(),
		Name:           project.Name,
		Slug:           project.Slug,
		Description:    project.Description,
		OrganizationID: project.OrganizationID.String(),
		DefaultLocale:  project.DefaultLocale,
		IsPublic:       project.IsPublic,
		CreatedBy:      uuidPtrToStringPtr(project.CreatedBy),
		CreatedAt:      project.CreatedAt,
		UpdatedAt:      project.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Project created successfully")
}

// GetProject handles GET /api/projects/:id
func (h *ProjectHandler) GetProject(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	projectIDStr := c.Param("id")
	projectID, err := uuid.Parse(projectIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid project ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get project
	var project models.Project
	if err := db.Preload("Organization").Preload("Creator").
		Where("id = ?", projectID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.LogError(c, "Failed to get project", err, nil)
		utils.InternalServerError(c, "Failed to retrieve project")
		return
	}

	// Check if user has access to the organization
	var member models.OrganizationMember
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?",
		project.OrganizationID, user.ID, true).First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have access to this project")
			return
		}
		utils.LogError(c, "Failed to check organization membership", err, nil)
		utils.InternalServerError(c, "Failed to verify project access")
		return
	}

	// Convert to response format
	response := ProjectResponse{
		ID:             project.ID.String(),
		Name:           project.Name,
		Slug:           project.Slug,
		Description:    project.Description,
		OrganizationID: project.OrganizationID.String(),
		DefaultLocale:  project.DefaultLocale,
		IsPublic:       project.IsPublic,
		CreatedBy:      uuidPtrToStringPtr(project.CreatedBy),
		CreatedAt:      project.CreatedAt,
		UpdatedAt:      project.UpdatedAt,
	}

	utils.Success(c, response)
}

// GetProjectBySlug handles GET /api/projects/slug/:slug
func (h *ProjectHandler) GetProjectBySlug(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	slug := c.Param("slug")
	organizationIDStr := c.Query("organization_id")

	if organizationIDStr == "" {
		utils.BadRequest(c, "organization_id query parameter is required")
		return
	}

	orgID, err := uuid.Parse(organizationIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid organization_id format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user has access to the organization
	var member models.OrganizationMember
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?",
		orgID, user.ID, true).First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have access to this organization")
			return
		}
		utils.LogError(c, "Failed to check organization membership", err, nil)
		utils.InternalServerError(c, "Failed to verify organization access")
		return
	}

	// Get project by slug
	var project models.Project
	if err := db.Preload("Organization").Preload("Creator").
		Where("organization_id = ? AND slug = ?", orgID, slug).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, fmt.Sprintf("Project with slug '%s' not found in this organization", slug))
			return
		}
		utils.LogError(c, "Failed to get project by slug", err, nil)
		utils.InternalServerError(c, "Failed to retrieve project")
		return
	}

	// Convert to response format
	response := ProjectResponse{
		ID:             project.ID.String(),
		Name:           project.Name,
		Slug:           project.Slug,
		Description:    project.Description,
		OrganizationID: project.OrganizationID.String(),
		DefaultLocale:  project.DefaultLocale,
		IsPublic:       project.IsPublic,
		CreatedBy:      uuidPtrToStringPtr(project.CreatedBy),
		CreatedAt:      project.CreatedAt,
		UpdatedAt:      project.UpdatedAt,
	}

	utils.Success(c, response)
}

// UpdateProject handles PUT /api/projects/:id
func (h *ProjectHandler) UpdateProject(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	projectIDStr := c.Param("id")
	projectID, err := uuid.Parse(projectIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid project ID format")
		return
	}

	var req UpdateProjectRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get project
	var project models.Project
	if err := db.Where("id = ?", projectID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.LogError(c, "Failed to get project", err, nil)
		utils.InternalServerError(c, "Failed to retrieve project")
		return
	}

	// Check if user has admin access to the organization
	var member models.OrganizationMember
	if err := db.Where("organization_id = ? AND user_id = ? AND role IN ? AND is_active = ?",
		project.OrganizationID, user.ID, []string{"admin", "owner"}, true).First(&member).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Forbidden(c, "You don't have admin access to this project")
			return
		}
		utils.LogError(c, "Failed to check organization admin access", err, nil)
		utils.InternalServerError(c, "Failed to verify project access")
		return
	}

	// Prepare update data
	updates := map[string]interface{}{
		"updated_at": time.Now(),
	}

	// Validate and update fields
	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Slug != nil {
		if !isValidSlug(*req.Slug) {
			utils.BadRequest(c, "Slug must contain only lowercase letters, numbers, and hyphens")
			return
		}
		// Check if slug is already taken by another project in the same organization
		var existingProject models.Project
		if err := db.Where("organization_id = ? AND slug = ? AND id != ?",
			project.OrganizationID, *req.Slug, projectID).First(&existingProject).Error; err == nil {
			utils.Conflict(c, fmt.Sprintf("Project with slug '%s' already exists in this organization", *req.Slug))
			return
		}
		updates["slug"] = *req.Slug
	}
	if req.Description != nil {
		updates["description"] = req.Description
	}
	if req.DefaultLocale != nil {
		updates["default_locale"] = req.DefaultLocale
	}
	if req.IsPublic != nil {
		updates["is_public"] = *req.IsPublic
	}

	// Update project
	if err := db.Model(&project).Updates(updates).Error; err != nil {
		if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "unique constraint") {
			utils.Conflict(c, "Project with this name or slug already exists in this organization")
			return
		}
		utils.LogError(c, "Failed to update project", err, nil)
		utils.InternalServerError(c, "Failed to update project")
		return
	}

	// Reload project with relations
	if err := db.Preload("Organization").Preload("Creator").
		Where("id = ?", projectID).First(&project).Error; err != nil {
		utils.LogError(c, "Failed to reload updated project", err, nil)
		utils.InternalServerError(c, "Failed to retrieve updated project")
		return
	}

	// Convert to response format
	response := ProjectResponse{
		ID:             project.ID.String(),
		Name:           project.Name,
		Slug:           project.Slug,
		Description:    project.Description,
		OrganizationID: project.OrganizationID.String(),
		DefaultLocale:  project.DefaultLocale,
		IsPublic:       project.IsPublic,
		CreatedBy:      uuidPtrToStringPtr(project.CreatedBy),
		CreatedAt:      project.CreatedAt,
		UpdatedAt:      project.UpdatedAt,
	}

	utils.SuccessWithMessage(c, response, "Project updated successfully")
}

// DeleteProject handles DELETE /api/projects/:id
func (h *ProjectHandler) DeleteProject(c *gin.Context) {
	// Get authenticated user from context
	user, exists := middleware.GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return
	}

	projectIDStr := c.Param("id")
	projectID, err := uuid.Parse(projectIDStr)
	if err != nil {
		utils.BadRequest(c, "Invalid project ID format")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Get project
	var project models.Project
	if err := db.Where("id = ?", projectID).First(&project).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.NotFound(c, "Project not found")
			return
		}
		utils.LogError(c, "Failed to get project", err, nil)
		utils.InternalServerError(c, "Failed to retrieve project")
		return
	}

	// Check if user has admin access to the organization or is the project creator
	var member models.OrganizationMember
	if err := db.Where("organization_id = ? AND user_id = ? AND role IN ? AND is_active = ?",
		project.OrganizationID, user.ID, []string{"admin", "owner"}, true).First(&member).Error; err != nil {
		// If not admin, check if user is the project creator
		if project.CreatedBy == nil || project.CreatedBy.String() != user.ID {
			utils.Forbidden(c, "You don't have permission to delete this project")
			return
		}
	}

	// Soft delete project (using GORM's soft delete)
	if err := db.Delete(&project).Error; err != nil {
		utils.LogError(c, "Failed to delete project", err, nil)
		utils.InternalServerError(c, "Failed to delete project")
		return
	}

	utils.SuccessWithMessage(c, map[string]string{
		"message": "Project deleted successfully",
	}, "Project deleted successfully")
}

// Helper function to check if user has access to project
func (h *ProjectHandler) checkProjectAccess(db *gorm.DB, projectID uuid.UUID, userID string) (*models.Project, *models.OrganizationMember, error) {
	// Get project
	var project models.Project
	if err := db.Where("id = ?", projectID).First(&project).Error; err != nil {
		return nil, nil, err
	}

	// Check if user has access to the organization
	var member models.OrganizationMember
	if err := db.Where("organization_id = ? AND user_id = ? AND is_active = ?",
		project.OrganizationID, userID, true).First(&member).Error; err != nil {
		return &project, nil, err
	}

	return &project, &member, nil
}
