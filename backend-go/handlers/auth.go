package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthHandler handles authentication routes
type AuthHandler struct {
	authConfig middleware.AuthConfig
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authConfig middleware.AuthConfig) *AuthHandler {
	return &AuthHandler{
		authConfig: authConfig,
	}
}

// Request structures
type SignUpRequest struct {
	Email    string  `json:"email" binding:"required,email"`
	Username string  `json:"username" binding:"required,min=3,max=50"`
	Password string  `json:"password" binding:"required,min=8"`
	FullName *string `json:"full_name"`
}

type SignInRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

type RequestPasswordResetRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type ConfirmPasswordResetRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

type RequestEmailVerificationRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type VerifyEmailRequest struct {
	Token string `json:"token" binding:"required"`
}

type GoogleAuthRequest struct {
	Email    string  `json:"email" binding:"required,email"`
	Name     string  `json:"name" binding:"required"`
	GoogleID string  `json:"googleId" binding:"required"`
	Picture  *string `json:"picture"`
}

// Response structures
type AuthResponse struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	User         UserProfile `json:"user"`
}

type UserProfile struct {
	ID            string     `json:"id"`
	Email         string     `json:"email"`
	Username      string     `json:"username"`
	FullName      *string    `json:"full_name"`
	EmailVerified bool       `json:"email_verified"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	LastLoginAt   *time.Time `json:"last_login_at"`
}

// SignUp handles POST /api/auth/signup
func (h *AuthHandler) SignUp(c *gin.Context) {
	var req SignUpRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user already exists
	var existingUser models.User
	if err := db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("User with email '%s' already exists", req.Email))
		return
	}

	// Check if username is taken
	if err := db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("Username '%s' is already taken", req.Username))
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		utils.LogError(c, "Failed to hash password", err, nil)
		utils.InternalServerError(c, "Failed to create user")
		return
	}

	// Create user
	user := models.User{
		Email:        req.Email,
		Username:     &req.Username,
		PasswordHash: string(hashedPassword),
		FirstName:    req.FullName,
		IsActive:     true,
	}

	if err := db.Create(&user).Error; err != nil {
		utils.LogError(c, "Failed to create user", err, nil)
		utils.InternalServerError(c, "Failed to create user")
		return
	}

	// Generate tokens
	accessToken, err := middleware.GenerateJWT(h.authConfig, user.ID.String(), user.Email, "")
	if err != nil {
		utils.LogError(c, "Failed to generate access token", err, nil)
		utils.InternalServerError(c, "Failed to generate authentication token")
		return
	}

	refreshToken, err := middleware.GenerateRefreshToken(h.authConfig, user.ID.String())
	if err != nil {
		utils.LogError(c, "Failed to generate refresh token", err, nil)
		utils.InternalServerError(c, "Failed to generate refresh token")
		return
	}

	// Update last login
	user.LastLoginAt = &time.Time{}
	*user.LastLoginAt = time.Now()
	db.Save(&user)

	// Prepare response
	username := req.Username
	if user.Username != nil {
		username = *user.Username
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User: UserProfile{
			ID:            user.ID.String(),
			Email:         user.Email,
			Username:      username,
			FullName:      user.FirstName,
			EmailVerified: user.EmailVerified,
			CreatedAt:     user.CreatedAt,
			UpdatedAt:     user.UpdatedAt,
			LastLoginAt:   user.LastLoginAt,
		},
	}

	utils.SuccessWithMessage(c, response, "User created successfully")
}

// SignIn handles POST /api/auth/signin
func (h *AuthHandler) SignIn(c *gin.Context) {
	var req SignInRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by email
	var user models.User
	if err := db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Unauthorized(c, "Invalid email or password")
			return
		}
		utils.LogError(c, "Database error during signin", err, nil)
		utils.InternalServerError(c, "Failed to authenticate user")
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		utils.Unauthorized(c, "Invalid email or password")
		return
	}

	// Generate tokens
	accessToken, err := middleware.GenerateJWT(h.authConfig, user.ID.String(), user.Email, "")
	if err != nil {
		utils.LogError(c, "Failed to generate access token", err, nil)
		utils.InternalServerError(c, "Failed to generate authentication token")
		return
	}

	refreshToken, err := middleware.GenerateRefreshToken(h.authConfig, user.ID.String())
	if err != nil {
		utils.LogError(c, "Failed to generate refresh token", err, nil)
		utils.InternalServerError(c, "Failed to generate refresh token")
		return
	}

	// Update last login
	now := time.Now()
	user.LastLoginAt = &now
	db.Save(&user)

	// Prepare response
	username := user.Email // Default to email
	if user.Username != nil {
		username = *user.Username
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User: UserProfile{
			ID:            user.ID.String(),
			Email:         user.Email,
			Username:      username,
			FullName:      user.FirstName,
			EmailVerified: user.EmailVerified,
			CreatedAt:     user.CreatedAt,
			UpdatedAt:     user.UpdatedAt,
			LastLoginAt:   user.LastLoginAt,
		},
	}

	utils.SuccessWithMessage(c, response, "Sign in successful")
}

// RefreshToken handles POST /api/auth/refresh-token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// Validate the refresh token
	claims, err := middleware.ValidateToken(h.authConfig, req.RefreshToken)
	if err != nil {
		utils.Unauthorized(c, "Invalid refresh token")
		return
	}

	// Check if the token is a refresh token (has empty email)
	if claims.Email != "" {
		utils.Unauthorized(c, "Invalid token type")
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find the user in the database
	var user models.User
	if err := db.Where("id = ?", claims.UserID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Unauthorized(c, "User not found")
			return
		}
		utils.LogError(c, "Database error during refresh token", err, nil)
		utils.InternalServerError(c, "Failed to validate user")
		return
	}

	// Check if user is still active
	if !user.IsActive {
		utils.Unauthorized(c, "User account is inactive")
		return
	}

	// Generate new tokens
	accessToken, err := middleware.GenerateJWT(h.authConfig, user.ID.String(), user.Email, "")
	if err != nil {
		utils.LogError(c, "Failed to generate new access token", err, nil)
		utils.InternalServerError(c, "Failed to generate new access token")
		return
	}

	refreshToken, err := middleware.GenerateRefreshToken(h.authConfig, user.ID.String())
	if err != nil {
		utils.LogError(c, "Failed to generate new refresh token", err, nil)
		utils.InternalServerError(c, "Failed to generate new refresh token")
		return
	}

	// Prepare response
	response := map[string]string{
		"access_token":  accessToken,
		"refresh_token": refreshToken,
	}

	utils.SuccessWithMessage(c, response, "Tokens refreshed successfully")
}

// RequestPasswordReset handles POST /api/auth/request-password-reset
func (h *AuthHandler) RequestPasswordReset(c *gin.Context) {
	var req RequestPasswordResetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by email (don't reveal if user exists or not)
	var user models.User
	userExists := db.Where("email = ?", req.Email).First(&user).Error == nil

	if userExists && user.IsActive {
		// Generate password reset token
		resetToken, err := middleware.GeneratePasswordResetToken(h.authConfig, user.ID.String(), user.Email)
		if err != nil {
			utils.LogError(c, "Failed to generate password reset token", err, nil)
			// Don't return error to avoid revealing user existence
		} else {
			// Store the token in database
			passwordResetToken := models.PasswordResetToken{
				UserID:    user.ID,
				Token:     resetToken,
				ExpiresAt: time.Now().Add(1 * time.Hour),
			}

			if err := db.Create(&passwordResetToken).Error; err != nil {
				utils.LogError(c, "Failed to store password reset token", err, nil)
				// Don't return error to avoid revealing user existence
			}

			// TODO: Send email with reset link
			// For now, just log the token (remove this in production)
			utils.LogInfo(c, "Password reset token generated", map[string]interface{}{
				"user_id": user.ID.String(),
				"token":   resetToken,
			})
		}
	}

	// Always return success to avoid user enumeration
	utils.SuccessWithMessage(c, map[string]string{
		"message": "If the email exists, a password reset link has been sent",
	}, "Password reset request processed")
}

// ConfirmPasswordReset handles POST /api/auth/confirm-password-reset
func (h *AuthHandler) ConfirmPasswordReset(c *gin.Context) {
	var req ConfirmPasswordResetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Validate the reset token
	claims, err := middleware.ValidateToken(h.authConfig, req.Token)
	if err != nil {
		utils.BadRequest(c, "Invalid or expired reset token")
		return
	}

	// Find the password reset token in database
	var resetToken models.PasswordResetToken
	if err := db.Where("token = ? AND used_at IS NULL", req.Token).First(&resetToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.BadRequest(c, "Invalid or expired reset token")
			return
		}
		utils.LogError(c, "Database error during password reset", err, nil)
		utils.InternalServerError(c, "Failed to validate reset token")
		return
	}

	// Check if token is expired
	if time.Now().After(resetToken.ExpiresAt) {
		utils.BadRequest(c, "Reset token has expired")
		return
	}

	// Find the user
	var user models.User
	if err := db.Where("id = ?", claims.UserID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.BadRequest(c, "User not found")
			return
		}
		utils.LogError(c, "Database error finding user", err, nil)
		utils.InternalServerError(c, "Failed to find user")
		return
	}

	// Hash the new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		utils.LogError(c, "Failed to hash new password", err, nil)
		utils.InternalServerError(c, "Failed to update password")
		return
	}

	// Start transaction
	tx := db.Begin()

	// Update user password
	if err := tx.Model(&user).Update("password_hash", string(hashedPassword)).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to update user password", err, nil)
		utils.InternalServerError(c, "Failed to update password")
		return
	}

	// Mark reset token as used
	now := time.Now()
	if err := tx.Model(&resetToken).Update("used_at", &now).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to mark reset token as used", err, nil)
		utils.InternalServerError(c, "Failed to complete password reset")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.LogError(c, "Failed to commit password reset transaction", err, nil)
		utils.InternalServerError(c, "Failed to complete password reset")
		return
	}

	utils.SuccessWithMessage(c, map[string]string{
		"message": "Password has been reset successfully",
	}, "Password reset successful")
}

// RequestEmailVerification handles POST /api/auth/request-email-verification
func (h *AuthHandler) RequestEmailVerification(c *gin.Context) {
	var req RequestEmailVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by email (don't reveal if user exists or not)
	var user models.User
	userExists := db.Where("email = ?", req.Email).First(&user).Error == nil

	if userExists && user.IsActive && !user.EmailVerified {
		// Generate email verification token
		verificationToken, err := middleware.GenerateEmailVerificationToken(h.authConfig, user.ID.String(), user.Email)
		if err != nil {
			utils.LogError(c, "Failed to generate email verification token", err, nil)
			// Don't return error to avoid revealing user existence
		} else {
			// Store the token in database
			emailVerificationToken := models.EmailVerificationToken{
				UserID:    user.ID,
				Token:     verificationToken,
				Email:     user.Email,
				ExpiresAt: time.Now().Add(24 * time.Hour),
			}

			if err := db.Create(&emailVerificationToken).Error; err != nil {
				utils.LogError(c, "Failed to store email verification token", err, nil)
				// Don't return error to avoid revealing user existence
			}

			// TODO: Send email with verification link
			// For now, just log the token (remove this in production)
			utils.LogInfo(c, "Email verification token generated", map[string]interface{}{
				"user_id": user.ID.String(),
				"token":   verificationToken,
			})
		}
	}

	// Always return success to avoid user enumeration
	utils.SuccessWithMessage(c, map[string]string{
		"message": "If the email exists and is not verified, a verification link has been sent",
	}, "Email verification request processed")
}

// VerifyEmail handles POST /api/auth/verify-email
func (h *AuthHandler) VerifyEmail(c *gin.Context) {
	var req VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Validate the verification token
	claims, err := middleware.ValidateToken(h.authConfig, req.Token)
	if err != nil {
		utils.BadRequest(c, "Invalid or expired verification token")
		return
	}

	// Find the email verification token in database
	var verificationToken models.EmailVerificationToken
	if err := db.Where("token = ? AND used_at IS NULL", req.Token).First(&verificationToken).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.BadRequest(c, "Invalid or expired verification token")
			return
		}
		utils.LogError(c, "Database error during email verification", err, nil)
		utils.InternalServerError(c, "Failed to validate verification token")
		return
	}

	// Check if token is expired
	if time.Now().After(verificationToken.ExpiresAt) {
		utils.BadRequest(c, "Verification token has expired")
		return
	}

	// Find the user
	var user models.User
	if err := db.Where("id = ?", claims.UserID).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.BadRequest(c, "User not found")
			return
		}
		utils.LogError(c, "Database error finding user", err, nil)
		utils.InternalServerError(c, "Failed to find user")
		return
	}

	// Check if email is already verified
	if user.EmailVerified {
		utils.BadRequest(c, "Email is already verified")
		return
	}

	// Start transaction
	tx := db.Begin()

	// Update user email verification status
	now := time.Now()
	if err := tx.Model(&user).Updates(map[string]interface{}{
		"email_verified":    true,
		"email_verified_at": &now,
	}).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to update user email verification", err, nil)
		utils.InternalServerError(c, "Failed to verify email")
		return
	}

	// Mark verification token as used
	if err := tx.Model(&verificationToken).Update("used_at", &now).Error; err != nil {
		tx.Rollback()
		utils.LogError(c, "Failed to mark verification token as used", err, nil)
		utils.InternalServerError(c, "Failed to complete email verification")
		return
	}

	// Commit transaction
	if err := tx.Commit().Error; err != nil {
		utils.LogError(c, "Failed to commit email verification transaction", err, nil)
		utils.InternalServerError(c, "Failed to complete email verification")
		return
	}

	utils.SuccessWithMessage(c, map[string]string{
		"message": "Email has been verified successfully",
	}, "Email verification successful")
}

// GoogleAuth handles POST /api/auth/google-auth
func (h *AuthHandler) GoogleAuth(c *gin.Context) {
	var req GoogleAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// TODO: Verify Google token with Google's API
	// For now, we'll trust the provided data (NOT SECURE - implement proper verification)

	// Check if user already exists
	var user models.User
	userExists := db.Where("email = ?", req.Email).First(&user).Error == nil

	if userExists {
		// User exists, sign them in
		if !user.IsActive {
			utils.Unauthorized(c, "User account is inactive")
			return
		}

		// Update last login
		now := time.Now()
		user.LastLoginAt = &now
		db.Save(&user)
	} else {
		// Create new user from Google data
		username := generateUsernameFromEmail(req.Email)
		user = models.User{
			Email:         req.Email,
			Username:      &username,
			PasswordHash:  "", // No password for OAuth users
			FirstName:     &req.Name,
			EmailVerified: true, // Google emails are pre-verified
			IsActive:      true,
		}

		if err := db.Create(&user).Error; err != nil {
			utils.LogError(c, "Failed to create Google user", err, nil)
			utils.InternalServerError(c, "Failed to create user account")
			return
		}
	}

	// Generate tokens
	accessToken, err := middleware.GenerateJWT(h.authConfig, user.ID.String(), user.Email, "")
	if err != nil {
		utils.LogError(c, "Failed to generate access token", err, nil)
		utils.InternalServerError(c, "Failed to generate authentication token")
		return
	}

	refreshToken, err := middleware.GenerateRefreshToken(h.authConfig, user.ID.String())
	if err != nil {
		utils.LogError(c, "Failed to generate refresh token", err, nil)
		utils.InternalServerError(c, "Failed to generate refresh token")
		return
	}

	// Prepare response
	username := user.Email // Default to email
	if user.Username != nil {
		username = *user.Username
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User: UserProfile{
			ID:            user.ID.String(),
			Email:         user.Email,
			Username:      username,
			FullName:      user.FirstName,
			EmailVerified: user.EmailVerified,
			CreatedAt:     user.CreatedAt,
			UpdatedAt:     user.UpdatedAt,
			LastLoginAt:   user.LastLoginAt,
		},
	}

	utils.SuccessWithMessage(c, response, "Google authentication successful")
}

// Helper functions

// generateRandomString generates a random string of specified length
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateUsernameFromEmail generates a username from email
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) == 0 {
		return "user"
	}

	username := parts[0]

	// Add random suffix to make it unique
	suffix, _ := generateRandomString(3)
	return fmt.Sprintf("%s_%s", username, suffix)
}
