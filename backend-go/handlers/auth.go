package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"strings"
	"time"

	"adc-multi-languages/database"
	"adc-multi-languages/middleware"
	"adc-multi-languages/models"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// AuthHandler handles authentication routes
type AuthHandler struct {
	authConfig middleware.AuthConfig
}

// NewAuthHandler creates a new auth handler
func NewAuthHandler(authConfig middleware.AuthConfig) *AuthHandler {
	return &AuthHandler{
		authConfig: authConfig,
	}
}

// Request structures
type SignUpRequest struct {
	Email    string  `json:"email" binding:"required,email"`
	Username string  `json:"username" binding:"required,min=3,max=50"`
	Password string  `json:"password" binding:"required,min=8"`
	FullName *string `json:"full_name"`
}

type SignInRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

type RequestPasswordResetRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type ConfirmPasswordResetRequest struct {
	Token       string `json:"token" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

type RequestEmailVerificationRequest struct {
	Email string `json:"email" binding:"required,email"`
}

type VerifyEmailRequest struct {
	Token string `json:"token" binding:"required"`
}

type GoogleAuthRequest struct {
	Email    string  `json:"email" binding:"required,email"`
	Name     string  `json:"name" binding:"required"`
	GoogleID string  `json:"googleId" binding:"required"`
	Picture  *string `json:"picture"`
}

// Response structures
type AuthResponse struct {
	AccessToken  string      `json:"access_token"`
	RefreshToken string      `json:"refresh_token"`
	User         UserProfile `json:"user"`
}

type UserProfile struct {
	ID            string     `json:"id"`
	Email         string     `json:"email"`
	Username      string     `json:"username"`
	FullName      *string    `json:"full_name"`
	EmailVerified bool       `json:"email_verified"`
	CreatedAt     time.Time  `json:"created_at"`
	UpdatedAt     time.Time  `json:"updated_at"`
	LastLoginAt   *time.Time `json:"last_login_at"`
}

// SignUp handles POST /api/auth/signup
func (h *AuthHandler) SignUp(c *gin.Context) {
	var req SignUpRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Check if user already exists
	var existingUser models.User
	if err := db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("User with email '%s' already exists", req.Email))
		return
	}

	// Check if username is taken
	if err := db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		utils.Conflict(c, fmt.Sprintf("Username '%s' is already taken", req.Username))
		return
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		utils.LogError(c, "Failed to hash password", err, nil)
		utils.InternalServerError(c, "Failed to create user")
		return
	}

	// Create user
	user := models.User{
		Email:        req.Email,
		Username:     &req.Username,
		PasswordHash: string(hashedPassword),
		FirstName:    req.FullName,
		IsActive:     true,
	}

	if err := db.Create(&user).Error; err != nil {
		utils.LogError(c, "Failed to create user", err, nil)
		utils.InternalServerError(c, "Failed to create user")
		return
	}

	// Generate tokens
	accessToken, err := middleware.GenerateJWT(h.authConfig, user.ID.String(), user.Email, "")
	if err != nil {
		utils.LogError(c, "Failed to generate access token", err, nil)
		utils.InternalServerError(c, "Failed to generate authentication token")
		return
	}

	refreshToken, err := middleware.GenerateRefreshToken(h.authConfig, user.ID.String())
	if err != nil {
		utils.LogError(c, "Failed to generate refresh token", err, nil)
		utils.InternalServerError(c, "Failed to generate refresh token")
		return
	}

	// Update last login
	user.LastLoginAt = &time.Time{}
	*user.LastLoginAt = time.Now()
	db.Save(&user)

	// Prepare response
	username := req.Username
	if user.Username != nil {
		username = *user.Username
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User: UserProfile{
			ID:            user.ID.String(),
			Email:         user.Email,
			Username:      username,
			FullName:      user.FirstName,
			EmailVerified: user.EmailVerified,
			CreatedAt:     user.CreatedAt,
			UpdatedAt:     user.UpdatedAt,
			LastLoginAt:   user.LastLoginAt,
		},
	}

	utils.SuccessWithMessage(c, response, "User created successfully")
}

// SignIn handles POST /api/auth/signin
func (h *AuthHandler) SignIn(c *gin.Context) {
	var req SignInRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	db := database.GetDB()
	if db == nil {
		utils.InternalServerError(c, "Database connection not available")
		return
	}

	// Find user by email
	var user models.User
	if err := db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			utils.Unauthorized(c, "Invalid email or password")
			return
		}
		utils.LogError(c, "Database error during signin", err, nil)
		utils.InternalServerError(c, "Failed to authenticate user")
		return
	}

	// Verify password
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(req.Password)); err != nil {
		utils.Unauthorized(c, "Invalid email or password")
		return
	}

	// Generate tokens
	accessToken, err := middleware.GenerateJWT(h.authConfig, user.ID.String(), user.Email, "")
	if err != nil {
		utils.LogError(c, "Failed to generate access token", err, nil)
		utils.InternalServerError(c, "Failed to generate authentication token")
		return
	}

	refreshToken, err := middleware.GenerateRefreshToken(h.authConfig, user.ID.String())
	if err != nil {
		utils.LogError(c, "Failed to generate refresh token", err, nil)
		utils.InternalServerError(c, "Failed to generate refresh token")
		return
	}

	// Update last login
	now := time.Now()
	user.LastLoginAt = &now
	db.Save(&user)

	// Prepare response
	username := user.Email // Default to email
	if user.Username != nil {
		username = *user.Username
	}

	response := AuthResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		User: UserProfile{
			ID:            user.ID.String(),
			Email:         user.Email,
			Username:      username,
			FullName:      user.FirstName,
			EmailVerified: user.EmailVerified,
			CreatedAt:     user.CreatedAt,
			UpdatedAt:     user.UpdatedAt,
			LastLoginAt:   user.LastLoginAt,
		},
	}

	utils.SuccessWithMessage(c, response, "Sign in successful")
}

// RefreshToken handles POST /api/auth/refresh-token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// TODO: Implement refresh token validation and new token generation
	// For now, return not implemented
	utils.Error(c, 501, utils.ErrorCodeInternalError, "Refresh token functionality not implemented yet")
}

// RequestPasswordReset handles POST /api/auth/request-password-reset
func (h *AuthHandler) RequestPasswordReset(c *gin.Context) {
	var req RequestPasswordResetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// TODO: Implement password reset email sending
	// For now, return success (don't reveal if email exists)
	utils.SuccessWithMessage(c, map[string]string{
		"message": "If the email exists, a password reset link has been sent",
	}, "Password reset request processed")
}

// ConfirmPasswordReset handles POST /api/auth/confirm-password-reset
func (h *AuthHandler) ConfirmPasswordReset(c *gin.Context) {
	var req ConfirmPasswordResetRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// TODO: Implement password reset token validation and password update
	utils.Error(c, 501, utils.ErrorCodeInternalError, "Password reset confirmation not implemented yet")
}

// RequestEmailVerification handles POST /api/auth/request-email-verification
func (h *AuthHandler) RequestEmailVerification(c *gin.Context) {
	var req RequestEmailVerificationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// TODO: Implement email verification sending
	utils.SuccessWithMessage(c, map[string]string{
		"message": "If the email exists, a verification link has been sent",
	}, "Email verification request processed")
}

// VerifyEmail handles POST /api/auth/verify-email
func (h *AuthHandler) VerifyEmail(c *gin.Context) {
	var req VerifyEmailRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// TODO: Implement email verification token validation
	utils.Error(c, 501, utils.ErrorCodeInternalError, "Email verification not implemented yet")
}

// GoogleAuth handles POST /api/auth/google-auth
func (h *AuthHandler) GoogleAuth(c *gin.Context) {
	var req GoogleAuthRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, "Invalid request body: "+err.Error())
		return
	}

	// TODO: Implement Google OAuth authentication
	utils.Error(c, 501, utils.ErrorCodeInternalError, "Google authentication not implemented yet")
}

// Helper functions

// generateRandomString generates a random string of specified length
func generateRandomString(length int) (string, error) {
	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// generateUsernameFromEmail generates a username from email
func generateUsernameFromEmail(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) == 0 {
		return "user"
	}

	username := parts[0]

	// Add random suffix to make it unique
	suffix, _ := generateRandomString(3)
	return fmt.Sprintf("%s_%s", username, suffix)
}
