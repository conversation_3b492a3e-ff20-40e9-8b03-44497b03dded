# Server Configuration
PORT=8300
HOST=0.0.0.0
ENVIRONMENT=development

# Database Configuration
DATABASE_URL=postgres://username:password@localhost:5432/adc_multi_languages?sslmode=disable
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=your_password
DB_NAME=adc_multi_languages
DB_SSL_MODE=disable

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRATION_HOURS=24
JWT_REFRESH_EXPIRATION_DAYS=30

# API Configuration
API_VERSION=1.0

# Frontend Configuration
FRONTEND_URL=http://localhost:3300

# Logging Configuration
LOG_LEVEL=info

# External Services
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
GOOGLE_CLOUD_PROJECT_ID=your-gcp-project-id
GOOGLE_CLOUD_KEY_PATH=path/to/service-account-key.json

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
FROM_EMAIL=<EMAIL>
