package config

import (
	"fmt"
	"os"
	"strconv"

	"github.com/joho/godotenv"
)

// Config holds all configuration for the application
type Config struct {
	// Server configuration
	Port string
	Host string
	Env  string

	// Database configuration
	DatabaseURL      string
	DatabaseHost     string
	DatabasePort     string
	DatabaseUser     string
	DatabasePassword string
	DatabaseName     string
	DatabaseSSLMode  string

	// JWT configuration
	JWTSecret           string
	JWTExpirationHours  int
	JWTRefreshExpirationDays int

	// API configuration
	APIVersion string

	// CORS configuration
	AllowedOrigins []string

	// Logging configuration
	LogLevel string

	// External services
	StripeSecretKey      string
	StripeWebhookSecret  string
	GoogleCloudProjectID string
	GoogleCloudKeyPath   string
	
	// Email configuration
	SMTPHost     string
	SMTPPort     string
	SMTPUsername string
	SMTPPassword string
	FromEmail    string
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	// Load .env file if it exists
	_ = godotenv.Load()

	config := &Config{
		// Server defaults
		Port: getEnv("PORT", "8300"),
		Host: getEnv("HOST", "0.0.0.0"),
		Env:  getEnv("ENVIRONMENT", "development"),

		// Database configuration
		DatabaseURL:      getEnv("DATABASE_URL", ""),
		DatabaseHost:     getEnv("DB_HOST", "localhost"),
		DatabasePort:     getEnv("DB_PORT", "5432"),
		DatabaseUser:     getEnv("DB_USER", "postgres"),
		DatabasePassword: getEnv("DB_PASSWORD", ""),
		DatabaseName:     getEnv("DB_NAME", "adc_multi_languages"),
		DatabaseSSLMode:  getEnv("DB_SSL_MODE", "disable"),

		// JWT configuration
		JWTSecret:                getEnv("JWT_SECRET", "your-secret-key"),
		JWTExpirationHours:       getEnvAsInt("JWT_EXPIRATION_HOURS", 24),
		JWTRefreshExpirationDays: getEnvAsInt("JWT_REFRESH_EXPIRATION_DAYS", 30),

		// API configuration
		APIVersion: getEnv("API_VERSION", "1.0"),

		// CORS configuration
		AllowedOrigins: []string{
			getEnv("FRONTEND_URL", "http://localhost:3300"),
			"http://localhost:3000",
		},

		// Logging configuration
		LogLevel: getEnv("LOG_LEVEL", "info"),

		// External services
		StripeSecretKey:      getEnv("STRIPE_SECRET_KEY", ""),
		StripeWebhookSecret:  getEnv("STRIPE_WEBHOOK_SECRET", ""),
		GoogleCloudProjectID: getEnv("GOOGLE_CLOUD_PROJECT_ID", ""),
		GoogleCloudKeyPath:   getEnv("GOOGLE_CLOUD_KEY_PATH", ""),

		// Email configuration
		SMTPHost:     getEnv("SMTP_HOST", ""),
		SMTPPort:     getEnv("SMTP_PORT", "587"),
		SMTPUsername: getEnv("SMTP_USERNAME", ""),
		SMTPPassword: getEnv("SMTP_PASSWORD", ""),
		FromEmail:    getEnv("FROM_EMAIL", "<EMAIL>"),
	}

	// Validate required configuration
	if err := config.validate(); err != nil {
		return nil, err
	}

	return config, nil
}

// validate checks that required configuration is present
func (c *Config) validate() error {
	if c.JWTSecret == "your-secret-key" && c.Env == "production" {
		return fmt.Errorf("JWT_SECRET must be set in production")
	}

	if c.DatabaseURL == "" {
		// Build database URL from components
		c.DatabaseURL = fmt.Sprintf(
			"postgres://%s:%s@%s:%s/%s?sslmode=%s",
			c.DatabaseUser,
			c.DatabasePassword,
			c.DatabaseHost,
			c.DatabasePort,
			c.DatabaseName,
			c.DatabaseSSLMode,
		)
	}

	return nil
}

// GetDatabaseURL returns the complete database URL
func (c *Config) GetDatabaseURL() string {
	return c.DatabaseURL
}

// IsDevelopment returns true if the environment is development
func (c *Config) IsDevelopment() bool {
	return c.Env == "development"
}

// IsProduction returns true if the environment is production
func (c *Config) IsProduction() bool {
	return c.Env == "production"
}

// getEnv gets an environment variable with a fallback value
func getEnv(key, fallback string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return fallback
}

// getEnvAsInt gets an environment variable as an integer with a fallback value
func getEnvAsInt(key string, fallback int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return fallback
}
