# ADC Multi-Languages Backend (Go)

This is the Go backend for the ADC Multi-Languages application, migrated from the original Rust implementation. It maintains the same API structure and patterns while leveraging Go's ecosystem.

## Features

- **Consistent API Response Format**: Standardized JSON responses with success/error states, metadata, and pagination
- **Comprehensive Logging**: Structured logging with request tracking and audit trails
- **JWT Authentication**: Secure user authentication with refresh tokens
- **API Key Management**: Support for API key-based authentication with permission auditing
- **Database Integration**: PostgreSQL with GORM ORM and automatic migrations
- **CORS Support**: Configurable cross-origin resource sharing
- **Graceful Shutdown**: Proper server shutdown handling
- **Environment Configuration**: Flexible configuration management

## Architecture

The application follows a clean architecture pattern:

```
backend-go/
├── config/          # Configuration management
├── database/        # Database connection and utilities
├── handlers/        # HTTP request handlers
├── middleware/      # HTTP middleware (auth, CORS, logging)
├── models/          # Data models and structures
├── routes/          # Route definitions
├── utils/           # Utility functions (response, pagination, logging)
├── main.go          # Application entry point
├── go.mod           # Go module definition
└── .env.example     # Environment variables example
```

## Getting Started

### Prerequisites

- Go 1.21 or higher
- PostgreSQL 12 or higher
- Git

### Installation

1. **Clone the repository** (if not already done):
   ```bash
   git clone <repository-url>
   cd adc-multi-languages/backend-go
   ```

2. **Install dependencies**:
   ```bash
   go mod download
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Set up the database**:
   ```bash
   # Create database
   createdb adc_multi_languages
   
   # The application will automatically run migrations on startup
   ```

5. **Run the application**:
   ```bash
   go run main.go
   ```

The server will start on `http://localhost:8300` by default.

### Development

For development with auto-reload, you can use `air`:

```bash
# Install air
go install github.com/cosmtrek/air@latest

# Run with auto-reload
air
```

## API Documentation

### Response Format

All API responses follow this standardized format:

```json
{
  "success": true,
  "status": 200,
  "message": "Optional message",
  "data": {
    // Response data
  },
  "meta": {
    "timestamp": "2024-01-01T00:00:00Z",
    "request_id": "uuid",
    "version": "1.0",
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10
    }
  }
}
```

### Pagination

Pagination is supported through query parameters:

- `page`: Page number (default: 1)
- `per_page` or `limit`: Items per page (default: 10, max: 100)
- `offset`: Alternative to page-based pagination

### Authentication

The API supports two authentication methods:

1. **JWT Tokens**: For user authentication
   ```
   Authorization: Bearer <jwt-token>
   ```

2. **API Keys**: For programmatic access
   ```
   X-API-Key: <api-key>
   ```

### Available Endpoints

#### Public Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `GET /api/hello` - Hello world
- `GET /api/hello/:name` - Personalized hello
- `POST /api/echo` - Echo request body
- `GET /api/error-example` - Error examples for testing

#### Authentication Endpoints

- `POST /api/auth/signup` - User registration
- `POST /api/auth/signin` - User login
- `POST /api/auth/refresh-token` - Refresh JWT token
- `POST /api/auth/request-password-reset` - Request password reset
- `POST /api/auth/confirm-password-reset` - Confirm password reset
- `POST /api/auth/request-email-verification` - Request email verification
- `POST /api/auth/verify-email` - Verify email
- `POST /api/auth/google-auth` - Google OAuth

#### Protected Endpoints (require authentication)

- **Users**: `/api/users/*`
- **Organizations**: `/api/organizations/*`
- **Projects**: `/api/projects/*`
- **Locales**: `/api/locales/*`
- **Translations**: `/api/translations/*`
- **API Keys**: `/api/api-keys/*`
- **Admin**: `/api/admin/*`

## Configuration

The application can be configured through environment variables:

| Variable | Description | Default |
|----------|-------------|---------|
| `PORT` | Server port | `8300` |
| `HOST` | Server host | `0.0.0.0` |
| `ENVIRONMENT` | Environment (development/production) | `development` |
| `DATABASE_URL` | Complete database URL | - |
| `JWT_SECRET` | JWT signing secret | - |
| `LOG_LEVEL` | Logging level (debug/info/warn/error) | `info` |

See `.env.example` for a complete list of configuration options.

## Database

The application uses PostgreSQL with GORM ORM. Database migrations are automatically applied on startup.

### Models

- **User**: User accounts and profiles
- **Organization**: Organizations/teams
- **Project**: Translation projects
- **Locale**: Supported languages/locales
- **TranslationKey**: Translation keys
- **Translation**: Actual translations
- **APIKey**: API keys for programmatic access
- **APICallLog**: API usage audit logs
- **PermissionAuditLog**: Permission check audit logs

## Logging

The application uses structured JSON logging with the following features:

- Request/response logging with timing
- Error tracking with context
- API call auditing
- Permission check auditing
- Request ID tracking for correlation

## Testing

```bash
# Run tests
go test ./...

# Run tests with coverage
go test -cover ./...

# Run tests with verbose output
go test -v ./...
```

## Deployment

### Docker

```bash
# Build Docker image
docker build -t adc-multi-languages-go .

# Run container
docker run -p 8300:8300 --env-file .env adc-multi-languages-go
```

### Production Considerations

1. Set `ENVIRONMENT=production`
2. Use a strong `JWT_SECRET`
3. Configure proper database credentials
4. Set up SSL/TLS termination
5. Configure log aggregation
6. Set up monitoring and health checks

## Migration from Rust

This Go implementation maintains API compatibility with the original Rust backend:

- Same endpoint structure and naming
- Identical response format
- Compatible authentication mechanisms
- Equivalent error handling
- Same pagination approach

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

[Your License Here]
