package utils

import (
	"os"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// Logger is the global logger instance
var Logger *logrus.Logger

// InitLogger initializes the global logger
func InitLogger(level string) {
	Logger = logrus.New()

	// Set log level
	switch level {
	case "debug":
		Logger.SetLevel(logrus.DebugLevel)
	case "info":
		Logger.SetLevel(logrus.InfoLevel)
	case "warn":
		Logger.SetLevel(logrus.WarnLevel)
	case "error":
		Logger.SetLevel(logrus.ErrorLevel)
	default:
		Logger.SetLevel(logrus.InfoLevel)
	}

	// Set output format
	Logger.SetFormatter(&logrus.JSONFormatter{
		TimestampFormat: time.RFC3339,
	})

	// Set output destination
	Logger.SetOutput(os.Stdout)
}

// LoggerMiddleware creates a Gin middleware for request logging
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate request ID
		requestID := uuid.New().String()
		c.Set("request_id", requestID)

		// Start timer
		start := time.Now()

		// Process request
		c.Next()

		// Calculate latency
		latency := time.Since(start)

		// Get client IP
		clientIP := c.ClientIP()

		// Get user agent
		userAgent := c.Request.UserAgent()

		// Get request size
		requestSize := c.Request.ContentLength

		// Get response size (approximate)
		responseSize := c.Writer.Size()

		// Log the request
		Logger.WithFields(logrus.Fields{
			"request_id":    requestID,
			"method":        c.Request.Method,
			"path":          c.Request.URL.Path,
			"query":         c.Request.URL.RawQuery,
			"status":        c.Writer.Status(),
			"latency_ms":    latency.Milliseconds(),
			"client_ip":     clientIP,
			"user_agent":    userAgent,
			"request_size":  requestSize,
			"response_size": responseSize,
		}).Info("HTTP Request")
	}
}

// LogInfo logs an info message with context
func LogInfo(c *gin.Context, message string, fields logrus.Fields) {
	if fields == nil {
		fields = logrus.Fields{}
	}
	
	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		fields["request_id"] = requestID
	}

	Logger.WithFields(fields).Info(message)
}

// LogError logs an error message with context
func LogError(c *gin.Context, message string, err error, fields logrus.Fields) {
	if fields == nil {
		fields = logrus.Fields{}
	}
	
	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		fields["request_id"] = requestID
	}

	// Add error details
	if err != nil {
		fields["error"] = err.Error()
	}

	Logger.WithFields(fields).Error(message)
}

// LogWarn logs a warning message with context
func LogWarn(c *gin.Context, message string, fields logrus.Fields) {
	if fields == nil {
		fields = logrus.Fields{}
	}
	
	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		fields["request_id"] = requestID
	}

	Logger.WithFields(fields).Warn(message)
}

// LogDebug logs a debug message with context
func LogDebug(c *gin.Context, message string, fields logrus.Fields) {
	if fields == nil {
		fields = logrus.Fields{}
	}
	
	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		fields["request_id"] = requestID
	}

	Logger.WithFields(fields).Debug(message)
}

// LogAPICall logs API call details for audit purposes
func LogAPICall(c *gin.Context, apiKeyID, organizationID *string, endpoint, method string, statusCode int, responseTime time.Duration) {
	fields := logrus.Fields{
		"endpoint":      endpoint,
		"method":        method,
		"status_code":   statusCode,
		"response_time": responseTime.Milliseconds(),
		"client_ip":     c.ClientIP(),
		"user_agent":    c.Request.UserAgent(),
	}

	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		fields["request_id"] = requestID
	}

	// Add API key and organization info if available
	if apiKeyID != nil {
		fields["api_key_id"] = *apiKeyID
	}
	if organizationID != nil {
		fields["organization_id"] = *organizationID
	}

	Logger.WithFields(fields).Info("API Call")
}

// LogPermissionCheck logs permission check for audit purposes
func LogPermissionCheck(c *gin.Context, apiKeyID, organizationID, permissionKey string, granted bool) {
	fields := logrus.Fields{
		"api_key_id":      apiKeyID,
		"organization_id": organizationID,
		"permission_key":  permissionKey,
		"granted":         granted,
		"path":            c.Request.URL.Path,
		"method":          c.Request.Method,
		"client_ip":       c.ClientIP(),
	}

	// Add request ID if available
	if requestID, exists := c.Get("request_id"); exists {
		fields["request_id"] = requestID
	}

	Logger.WithFields(fields).Info("Permission Check")
}
