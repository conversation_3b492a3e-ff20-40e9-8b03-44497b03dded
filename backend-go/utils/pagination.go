package utils

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaginationParams holds pagination parameters from request
type PaginationParams struct {
	Page    uint32 `json:"page"`
	PerPage uint32 `json:"per_page"`
	Offset  uint32 `json:"offset"`
}

// DefaultPaginationParams returns default pagination parameters
func DefaultPaginationParams() PaginationParams {
	return PaginationParams{
		Page:    1,
		PerPage: 10,
		Offset:  0,
	}
}

// GetPaginationParams extracts pagination parameters from request query
func GetPaginationParams(c *gin.Context) PaginationParams {
	params := DefaultPaginationParams()

	// Parse page parameter
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.ParseUint(pageStr, 10, 32); err == nil && page > 0 {
			params.Page = uint32(page)
		}
	}

	// Parse per_page parameter
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if perPage, err := strconv.ParseUint(perPageStr, 10, 32); err == nil && perPage > 0 {
			params.PerPage = uint32(perPage)
			// Limit per_page to reasonable maximum
			if params.PerPage > 100 {
				params.PerPage = 100
			}
		}
	}

	// Parse limit parameter (alternative to per_page)
	if limitStr := c.Query("limit"); limitStr != "" && c.Query("per_page") == "" {
		if limit, err := strconv.ParseUint(limitStr, 10, 32); err == nil && limit > 0 {
			params.PerPage = uint32(limit)
			// Limit per_page to reasonable maximum
			if params.PerPage > 100 {
				params.PerPage = 100
			}
		}
	}

	// Parse offset parameter (alternative to page-based pagination)
	if offsetStr := c.Query("offset"); offsetStr != "" {
		if offset, err := strconv.ParseUint(offsetStr, 10, 32); err == nil {
			params.Offset = uint32(offset)
			// If offset is provided, calculate the page
			if params.PerPage > 0 {
				params.Page = (params.Offset / params.PerPage) + 1
			}
		}
	} else {
		// Calculate offset from page and per_page
		params.Offset = (params.Page - 1) * params.PerPage
	}

	return params
}

// PaginatedResponse creates a paginated response
func PaginatedResponse(c *gin.Context, data interface{}, params PaginationParams, total uint64) {
	SuccessWithPagination(c, data, params.Page, params.PerPage, total)
}

// GetLimitOffset returns limit and offset for database queries
func (p PaginationParams) GetLimitOffset() (limit, offset uint32) {
	return p.PerPage, p.Offset
}

// GetPageInfo returns page information for response
func (p PaginationParams) GetPageInfo(total uint64) (page, perPage uint32, totalCount uint64) {
	return p.Page, p.PerPage, total
}

// HasNextPage returns true if there are more pages
func (p PaginationParams) HasNextPage(total uint64) bool {
	return uint64(p.Offset+p.PerPage) < total
}

// HasPreviousPage returns true if there are previous pages
func (p PaginationParams) HasPreviousPage() bool {
	return p.Page > 1
}

// GetNextPage returns the next page number
func (p PaginationParams) GetNextPage(total uint64) *uint32 {
	if p.HasNextPage(total) {
		nextPage := p.Page + 1
		return &nextPage
	}
	return nil
}

// GetPreviousPage returns the previous page number
func (p PaginationParams) GetPreviousPage() *uint32 {
	if p.HasPreviousPage() {
		prevPage := p.Page - 1
		return &prevPage
	}
	return nil
}

// ValidatePaginationParams validates pagination parameters
func ValidatePaginationParams(params PaginationParams) []FieldError {
	var errors []FieldError

	if params.Page < 1 {
		errors = append(errors, FieldError{
			Field:   "page",
			Message: "Page must be greater than 0",
		})
	}

	if params.PerPage < 1 {
		errors = append(errors, FieldError{
			Field:   "per_page",
			Message: "Per page must be greater than 0",
		})
	}

	if params.PerPage > 100 {
		errors = append(errors, FieldError{
			Field:   "per_page",
			Message: "Per page cannot exceed 100",
		})
	}

	return errors
}
