package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// BaseModel contains common fields for all models
type BaseModel struct {
	ID        uuid.UUID      `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`
}

// BeforeCreate sets the ID before creating a record
func (base *BaseModel) BeforeCreate(tx *gorm.DB) error {
	if base.ID == uuid.Nil {
		base.ID = uuid.New()
	}
	return nil
}

// User represents a user in the system
type User struct {
	BaseModel
	Email             string     `json:"email" gorm:"uniqueIndex;not null"`
	PasswordHash      string     `json:"-" gorm:"not null"`
	FirstName         *string    `json:"first_name"`
	LastName          *string    `json:"last_name"`
	EmailVerified     bool       `json:"email_verified" gorm:"default:false"`
	EmailVerifiedAt   *time.Time `json:"email_verified_at"`
	LastLoginAt       *time.Time `json:"last_login_at"`
	IsActive          bool       `json:"is_active" gorm:"default:true"`
	ProfilePictureURL *string    `json:"profile_picture_url"`
	Timezone          *string    `json:"timezone"`
	Language          *string    `json:"language" gorm:"default:'en'"`
}

// Organization represents an organization in the system
type Organization struct {
	BaseModel
	Name        string  `json:"name" gorm:"not null"`
	Description *string `json:"description"`
	Website     *string `json:"website"`
	LogoURL     *string `json:"logo_url"`
	IsActive    bool    `json:"is_active" gorm:"default:true"`
	OwnerID     uuid.UUID `json:"owner_id" gorm:"type:uuid;not null"`
	Owner       User    `json:"owner" gorm:"foreignKey:OwnerID"`
}

// Project represents a project in the system
type Project struct {
	BaseModel
	Name           string    `json:"name" gorm:"not null"`
	Description    *string   `json:"description"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	IsActive       bool      `json:"is_active" gorm:"default:true"`
	DefaultLocale  string    `json:"default_locale" gorm:"default:'en'"`
}

// Locale represents a locale/language in the system
type Locale struct {
	BaseModel
	Code        string  `json:"code" gorm:"uniqueIndex;not null"` // e.g., "en", "es", "fr"
	Name        string  `json:"name" gorm:"not null"`             // e.g., "English", "Spanish", "French"
	NativeName  string  `json:"native_name" gorm:"not null"`      // e.g., "English", "Español", "Français"
	IsActive    bool    `json:"is_active" gorm:"default:true"`
	Direction   string  `json:"direction" gorm:"default:'ltr'"` // "ltr" or "rtl"
}

// TranslationKey represents a translation key in the system
type TranslationKey struct {
	BaseModel
	Key         string    `json:"key" gorm:"not null"`
	ProjectID   uuid.UUID `json:"project_id" gorm:"type:uuid;not null"`
	Project     Project   `json:"project" gorm:"foreignKey:ProjectID"`
	Description *string   `json:"description"`
	Context     *string   `json:"context"`
	IsActive    bool      `json:"is_active" gorm:"default:true"`
}

// Translation represents a translation in the system
type Translation struct {
	BaseModel
	TranslationKeyID uuid.UUID      `json:"translation_key_id" gorm:"type:uuid;not null"`
	TranslationKey   TranslationKey `json:"translation_key" gorm:"foreignKey:TranslationKeyID"`
	LocaleID         uuid.UUID      `json:"locale_id" gorm:"type:uuid;not null"`
	Locale           Locale         `json:"locale" gorm:"foreignKey:LocaleID"`
	Value            string         `json:"value" gorm:"not null"`
	IsApproved       bool           `json:"is_approved" gorm:"default:false"`
	ApprovedBy       *uuid.UUID     `json:"approved_by" gorm:"type:uuid"`
	ApprovedAt       *time.Time     `json:"approved_at"`
	TranslatedBy     *uuid.UUID     `json:"translated_by" gorm:"type:uuid"`
}

// APIKey represents an API key in the system
type APIKey struct {
	BaseModel
	Name           string    `json:"name" gorm:"not null"`
	Key            string    `json:"key" gorm:"uniqueIndex;not null"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	IsActive       bool      `json:"is_active" gorm:"default:true"`
	LastUsedAt     *time.Time `json:"last_used_at"`
	ExpiresAt      *time.Time `json:"expires_at"`
	CreatedBy      uuid.UUID `json:"created_by" gorm:"type:uuid;not null"`
	Creator        User      `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// APICallLog represents an API call log entry
type APICallLog struct {
	BaseModel
	APIKeyID           uuid.UUID `json:"api_key_id" gorm:"type:uuid;not null"`
	APIKey             APIKey    `json:"api_key" gorm:"foreignKey:APIKeyID"`
	OrganizationID     uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	Organization       Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	Endpoint           string    `json:"endpoint" gorm:"not null"`
	Method             string    `json:"method" gorm:"not null"`
	StatusCode         int       `json:"status_code" gorm:"not null"`
	ResponseTimeMs     int       `json:"response_time_ms" gorm:"not null"`
	RequestSizeBytes   *int      `json:"request_size_bytes"`
	ResponseSizeBytes  *int      `json:"response_size_bytes"`
	IPAddress          *string   `json:"ip_address"`
	UserAgent          *string   `json:"user_agent"`
}

// PermissionAuditLog represents a permission audit log entry
type PermissionAuditLog struct {
	BaseModel
	APIKeyID       uuid.UUID `json:"api_key_id" gorm:"type:uuid;not null"`
	APIKey         APIKey    `json:"api_key" gorm:"foreignKey:APIKeyID"`
	OrganizationID uuid.UUID `json:"organization_id" gorm:"type:uuid;not null"`
	Organization   Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	ResourcePath   string    `json:"resource_path" gorm:"not null"`
	Method         string    `json:"method" gorm:"not null"`
	PermissionKey  string    `json:"permission_key" gorm:"not null"`
	Granted        bool      `json:"granted" gorm:"not null"`
}
