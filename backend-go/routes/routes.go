package routes

import (
	"adc-multi-languages/config"
	"adc-multi-languages/handlers"
	"adc-multi-languages/middleware"
	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
)

// SetupRoutes configures all routes for the application
func SetupRoutes(cfg *config.Config) *gin.Engine {
	// Set Gin mode based on environment
	if cfg.IsProduction() {
		gin.SetMode(gin.ReleaseMode)
	}

	// Create Gin router
	router := gin.New()

	// Add middleware
	router.Use(gin.Recovery())
	router.Use(utils.LoggerMiddleware())

	// CORS configuration
	corsConfig := middleware.CORSConfig{
		AllowedOrigins:   cfg.AllowedOrigins,
		AllowedMethods:   []string{"GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Origin", "Content-Type", "Accept", "Authorization", "X-Requested-With", "X-API-Key"},
		ExposedHeaders:   []string{"Content-Length", "Content-Type"},
		AllowCredentials: true,
		MaxAge:           86400,
	}
	router.Use(middleware.CORS(corsConfig))

	// Auth configuration
	authConfig := middleware.AuthConfig{
		JWTSecret:                cfg.JWTSecret,
		JWTExpirationHours:       cfg.JWTExpirationHours,
		JWTRefreshExpirationDays: cfg.JWTRefreshExpirationDays,
	}

	// Initialize handlers
	apiHandler := handlers.NewAPIHandler()
	authHandler := handlers.NewAuthHandler(authConfig)
	userHandler := handlers.NewUserHandler()
	organizationHandler := handlers.NewOrganizationHandler()

	// Root routes
	router.GET("/", apiHandler.Index)
	router.GET("/health", apiHandler.Health)

	// API routes
	api := router.Group("/api")
	{
		// Public API routes
		api.GET("/hello", apiHandler.Hello)
		api.GET("/hello/:name", apiHandler.HelloWithName)
		api.POST("/echo", apiHandler.Echo)
		api.GET("/error-example", apiHandler.ErrorExample)

		// Auth routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/signup", authHandler.SignUp)
			auth.POST("/signin", authHandler.SignIn)
			auth.POST("/refresh-token", authHandler.RefreshToken)
			auth.POST("/request-password-reset", authHandler.RequestPasswordReset)
			auth.POST("/confirm-password-reset", authHandler.ConfirmPasswordReset)
			auth.POST("/request-email-verification", authHandler.RequestEmailVerification)
			auth.POST("/verify-email", authHandler.VerifyEmail)
			auth.POST("/google-auth", authHandler.GoogleAuth)
		}

		// Protected routes (require JWT authentication)
		protected := api.Group("")
		protected.Use(middleware.JWTAuth(authConfig))
		{
			// Users routes
			users := protected.Group("/users")
			{
				users.GET("/me", userHandler.GetProfile)
				users.PUT("/me", userHandler.UpdateProfile)
				users.GET("/:id", userHandler.GetUser)
			}

			// Organizations routes
			organizations := protected.Group("/organizations")
			{
				organizations.GET("", organizationHandler.ListOrganizations)
				organizations.POST("", organizationHandler.CreateOrganization)
				organizations.GET("/:id", organizationHandler.GetOrganization)
				organizations.PUT("/:id", organizationHandler.UpdateOrganization)
				organizations.DELETE("/:id", organizationHandler.DeleteOrganization)
			}

			// Projects routes
			projects := protected.Group("/projects")
			{
				projects.GET("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				projects.POST("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				projects.GET("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				projects.PUT("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				projects.DELETE("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
			}

			// Locales routes
			locales := protected.Group("/locales")
			{
				locales.GET("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				locales.POST("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				locales.GET("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				locales.PUT("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				locales.DELETE("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
			}

			// Translations routes
			translations := protected.Group("/translations")
			{
				translations.GET("/keys", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.POST("/keys", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.GET("/keys/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.POST("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.GET("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.GET("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.PUT("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				translations.GET("/:id/history", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
			}

			// Admin routes
			admin := protected.Group("/admin")
			{
				admin.GET("/dashboard", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				admin.GET("/users", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
			}

			// API Keys routes
			apiKeys := protected.Group("/api-keys")
			{
				apiKeys.GET("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				apiKeys.POST("", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				apiKeys.GET("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				apiKeys.PUT("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				apiKeys.DELETE("/:id", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
			}
		}

		// Webhook routes (public, but with special authentication)
		webhooks := api.Group("/webhooks")
		{
			webhooks.POST("/stripe", func(c *gin.Context) {
				c.JSON(501, gin.H{"message": "Not implemented yet"})
			})
		}

		// Test routes (development only)
		if cfg.IsDevelopment() {
			test := api.Group("/test")
			{
				test.POST("/email", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
				test.POST("/verification-email", func(c *gin.Context) {
					c.JSON(501, gin.H{"message": "Not implemented yet"})
				})
			}
		}
	}

	return router
}
