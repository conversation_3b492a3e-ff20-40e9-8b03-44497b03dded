package middleware

import (
	"fmt"
	"strings"
	"time"

	"adc-multi-languages/utils"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

// JWTClaims represents the JWT claims structure
type JWTClaims struct {
	UserID         string `json:"user_id"`
	Email          string `json:"email"`
	OrganizationID string `json:"organization_id,omitempty"`
	jwt.RegisteredClaims
}

// AuthConfig holds authentication configuration
type AuthConfig struct {
	JWTSecret                string
	JWTExpirationHours       int
	JWTRefreshExpirationDays int
}

// AuthUser represents an authenticated user
type AuthUser struct {
	ID             string `json:"id"`
	Email          string `json:"email"`
	OrganizationID string `json:"organization_id,omitempty"`
}

// J<PERSON>TAuth creates a JWT authentication middleware
func JWTAuth(config AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			utils.Unauthorized(c, "Authorization header is required")
			c.Abort()
			return
		}

		// Check if it's a Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			utils.Unauthorized(c, "Invalid authorization header format")
			c.Abort()
			return
		}

		tokenString := tokenParts[1]

		// Parse and validate the token
		token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
			// Validate the signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(config.JWTSecret), nil
		})

		if err != nil {
			utils.Unauthorized(c, "Invalid token")
			c.Abort()
			return
		}

		// Extract claims
		claims, ok := token.Claims.(*JWTClaims)
		if !ok || !token.Valid {
			utils.Unauthorized(c, "Invalid token claims")
			c.Abort()
			return
		}

		// Check if token is expired
		if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
			utils.Unauthorized(c, "Token has expired")
			c.Abort()
			return
		}

		// Set user information in context
		authUser := AuthUser{
			ID:             claims.UserID,
			Email:          claims.Email,
			OrganizationID: claims.OrganizationID,
		}
		c.Set("user", authUser)
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		if claims.OrganizationID != "" {
			c.Set("organization_id", claims.OrganizationID)
		}

		c.Next()
	}
}

// OptionalJWTAuth creates an optional JWT authentication middleware
// This middleware will set user context if a valid token is provided, but won't abort if no token is present
func OptionalJWTAuth(config AuthConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Get token from Authorization header
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		// Check if it's a Bearer token
		tokenParts := strings.Split(authHeader, " ")
		if len(tokenParts) != 2 || tokenParts[0] != "Bearer" {
			c.Next()
			return
		}

		tokenString := tokenParts[1]

		// Parse and validate the token
		token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
			// Validate the signing method
			if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
				return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
			}
			return []byte(config.JWTSecret), nil
		})

		if err != nil {
			c.Next()
			return
		}

		// Extract claims
		claims, ok := token.Claims.(*JWTClaims)
		if !ok || !token.Valid {
			c.Next()
			return
		}

		// Check if token is expired
		if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
			c.Next()
			return
		}

		// Set user information in context
		authUser := AuthUser{
			ID:             claims.UserID,
			Email:          claims.Email,
			OrganizationID: claims.OrganizationID,
		}
		c.Set("user", authUser)
		c.Set("user_id", claims.UserID)
		c.Set("user_email", claims.Email)
		if claims.OrganizationID != "" {
			c.Set("organization_id", claims.OrganizationID)
		}

		c.Next()
	}
}

// GenerateJWT generates a new JWT token for a user
func GenerateJWT(config AuthConfig, userID, email, organizationID string) (string, error) {
	// Create claims
	claims := JWTClaims{
		UserID:         userID,
		Email:          email,
		OrganizationID: organizationID,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(config.JWTExpirationHours) * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "adc-multi-languages",
			Subject:   userID,
			ID:        uuid.New().String(),
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString([]byte(config.JWTSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign token: %w", err)
	}

	return tokenString, nil
}

// GenerateRefreshToken generates a refresh token
func GenerateRefreshToken(config AuthConfig, userID string) (string, error) {
	// Create claims for refresh token with "refresh" role
	claims := JWTClaims{
		UserID:         userID,
		Email:          "", // Empty for refresh tokens
		OrganizationID: "",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Duration(config.JWTRefreshExpirationDays) * 24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "adc-multi-languages",
			Subject:   userID,
			ID:        uuid.New().String(),
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString([]byte(config.JWTSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return tokenString, nil
}

// GetAuthUser extracts the authenticated user from the context
func GetAuthUser(c *gin.Context) (*AuthUser, bool) {
	user, exists := c.Get("user")
	if !exists {
		return nil, false
	}

	authUser, ok := user.(AuthUser)
	return &authUser, ok
}

// RequireAuth is a helper function to check if user is authenticated
func RequireAuth(c *gin.Context) (*AuthUser, bool) {
	user, exists := GetAuthUser(c)
	if !exists {
		utils.Unauthorized(c, "Authentication required")
		return nil, false
	}
	return user, true
}

// ValidateToken validates a JWT token and returns the claims
func ValidateToken(config AuthConfig, tokenString string) (*JWTClaims, error) {
	// Parse and validate the token
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// Validate the signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(config.JWTSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	// Extract claims
	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("invalid token claims")
	}

	// Check if token is expired
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, fmt.Errorf("token has expired")
	}

	return claims, nil
}

// GeneratePasswordResetToken generates a password reset token
func GeneratePasswordResetToken(config AuthConfig, userID, email string) (string, error) {
	// Create claims for password reset token (1 hour expiration)
	claims := JWTClaims{
		UserID:         userID,
		Email:          email,
		OrganizationID: "",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(1 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "adc-multi-languages",
			Subject:   userID,
			ID:        uuid.New().String(),
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString([]byte(config.JWTSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign password reset token: %w", err)
	}

	return tokenString, nil
}

// GenerateEmailVerificationToken generates an email verification token
func GenerateEmailVerificationToken(config AuthConfig, userID, email string) (string, error) {
	// Create claims for email verification token (24 hour expiration)
	claims := JWTClaims{
		UserID:         userID,
		Email:          email,
		OrganizationID: "",
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "adc-multi-languages",
			Subject:   userID,
			ID:        uuid.New().String(),
		},
	}

	// Create token
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Sign token
	tokenString, err := token.SignedString([]byte(config.JWTSecret))
	if err != nil {
		return "", fmt.Errorf("failed to sign email verification token: %w", err)
	}

	return tokenString, nil
}
