# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Environment variables
.env
.env.local
.env.production

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Build artifacts
/tmp/
/build/
/dist/
adc-multi-languages
adc-multi-languages_unix

# Logs
*.log
build-errors.log

# Air temporary files
tmp/

# Test coverage
coverage.out
coverage.html

# Service account keys
*.json
!.env.example

# Database files
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
