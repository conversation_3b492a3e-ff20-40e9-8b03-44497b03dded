# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod
BINARY_NAME=adc-multi-languages
BINARY_UNIX=$(BINARY_NAME)_unix

# Default target
.PHONY: all
all: test build

# Build the application
.PHONY: build
build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./...

# Test the application
.PHONY: test
test:
	$(GOTEST) -v ./...

# Test with coverage
.PHONY: test-coverage
test-coverage:
	$(GOTEST) -v -cover ./...

# Clean build artifacts
.PHONY: clean
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)
	rm -f $(BINARY_UNIX)

# Run the application
.PHONY: run
run:
	$(GOCMD) run main.go

# Install dependencies
.PHONY: deps
deps:
	$(GOMOD) download
	$(GOMOD) verify

# Update dependencies
.PHONY: deps-update
deps-update:
	$(GOMOD) tidy
	$(GOGET) -u ./...

# Format code
.PHONY: fmt
fmt:
	$(GOCMD) fmt ./...

# Lint code
.PHONY: lint
lint:
	golangci-lint run

# Build for Linux
.PHONY: build-linux
build-linux:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 $(GOBUILD) -o $(BINARY_UNIX) -v

# Docker build
.PHONY: docker-build
docker-build:
	docker build -t $(BINARY_NAME) .

# Docker run
.PHONY: docker-run
docker-run:
	docker run -p 8300:8300 --env-file .env $(BINARY_NAME)

# Development with auto-reload (requires air)
.PHONY: dev
dev:
	air

# Install development tools
.PHONY: install-tools
install-tools:
	$(GOGET) github.com/cosmtrek/air@latest
	$(GOGET) github.com/golangci/golangci-lint/cmd/golangci-lint@latest

# Database operations
.PHONY: db-create
db-create:
	createdb adc_multi_languages

.PHONY: db-drop
db-drop:
	dropdb adc_multi_languages

.PHONY: db-reset
db-reset: db-drop db-create

# Help
.PHONY: help
help:
	@echo "Available targets:"
	@echo "  build         - Build the application"
	@echo "  test          - Run tests"
	@echo "  test-coverage - Run tests with coverage"
	@echo "  clean         - Clean build artifacts"
	@echo "  run           - Run the application"
	@echo "  dev           - Run with auto-reload (requires air)"
	@echo "  deps          - Install dependencies"
	@echo "  deps-update   - Update dependencies"
	@echo "  fmt           - Format code"
	@echo "  lint          - Lint code"
	@echo "  build-linux   - Build for Linux"
	@echo "  docker-build  - Build Docker image"
	@echo "  docker-run    - Run Docker container"
	@echo "  install-tools - Install development tools"
	@echo "  db-create     - Create database"
	@echo "  db-drop       - Drop database"
	@echo "  db-reset      - Reset database"
	@echo "  help          - Show this help"
