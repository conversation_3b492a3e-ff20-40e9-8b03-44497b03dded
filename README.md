# ADC Multi-Languages Monorepo

This is a monorepo for the ADC Multi-Languages project, containing both frontend and backend services.

## Repository Structure

```
adc-muti-languages/
├── frontend/                  # Next.js frontend
├── backend/                   # Rust backend
├── Makefile                   # Build and run commands
├── package.json               # Root package.json for JS/TS projects
├── Cargo.toml                 # Root Cargo.toml for Rust projects
└── turbo.json                 # Turborepo configuration
```

## Getting Started

### Prerequisites

- Node.js (v18 or later)
- Bun
- Rust (stable)
- Cargo Watch (`cargo install cargo-watch`)

### Setup

1. Clone the repository:
   ```
   git clone <repository-url>
   cd adc-muti-languages
   ```

2. Install dependencies and set up the projects:
   ```
   make setup
   ```

### Development

Run both frontend and backend:
```
make dev
```

Run only the frontend:
```
make frontend
```

Run only the backend:
```
make backend
```

### Cleaning

Clean build artifacts:
```
make clean
```

## License

[Your License]
