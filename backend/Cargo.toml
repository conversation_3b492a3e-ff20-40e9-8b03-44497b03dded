[package]
name = "adc-muti-languages"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A multi-language web application using Rocket framework"
default-run = "adc-muti-languages"

[features]
default = []
use_mock_stripe = []

[dependencies]
rocket = { version = "0.5.0", features = ["json"] }
rocket_cors = "0.6.0"
rocket_dyn_templates = { version = "0.1.0", features = ["handlebars"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
uuid = { version = "1.3", features = ["v4", "serde"] }
dotenv = "0.15"
diesel = { version = "2.1.0", features = ["postgres", "chrono", "uuid", "r2d2", "serde_json", "numeric"] }
diesel_migrations = "2.1.0"
bigdecimal = { version = "0.3", features = ["serde"] }
diesel-derive-newtype = "2.1.0"
r2d2 = "0.8"
bcrypt = "0.14"
jsonwebtoken = "8.3"
reqwest = { version = "0.11", features = ["json", "multipart"] }
tokio = { version = "1", features = ["full"] }
thiserror = "1.0"
anyhow = "1.0"
log = "0.4"
env_logger = "0.10"
base64 = "0.21"
handlebars = "4.3"
google-cloud-storage = "0.15.0"
gcp_auth = "0.10.0"
rand = "0.8"
sha2 = "0.10"
clap = { version = "4.4", features = ["derive"] }
lazy_static = "1.4.0"
