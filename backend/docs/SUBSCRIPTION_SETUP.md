# Subscription Plans Setup Guide

This guide explains how to set up and manage subscription plans for the ADC Multi-Languages application.

## Overview

The application uses a tiered subscription model with different plans (Free, Standard, Premium, Enterprise). Each plan has different features, limits, and pricing.

Subscription plans are stored in the database in the `subscription_tiers` table, which allows for flexible management of plans without code changes.

## Setting Up Subscription Plans

### Using the CLI Tool

We've created a CLI tool to manage subscription plans. This tool allows you to list, add, update, and delete subscription plans directly from the command line.

#### Building the CLI Tool

First, build the CLI tool:

```bash
cd adc-muti-languages-service
cargo build --bin manage_subscription_plans
```

#### Using the CLI Tool

The CLI tool provides the following commands:

1. **List all subscription plans**:
   ```bash
   ./target/debug/manage_subscription_plans list
   ```

2. **Add a new subscription plan**:
   ```bash
   ./target/debug/manage_subscription_plans add \
     --tier-name "premium" \
     --display-name "Premium" \
     --description "For professional translation needs" \
     --monthly-price 79 \
     --yearly-price 790 \
     --is-active true \
     --sort-order 3 \
     --features '["Unlimited projects", "Unlimited team members", "5,000 AI translation credits/month", "24/7 priority support", "Advanced analytics", "Custom export formats", "API access", "Custom integrations"]' \
     --limits '{"projects": -1, "team_members": -1, "ai_credits": 5000}' \
     --is-popular false
   ```

3. **Update an existing subscription plan**:
   ```bash
   ./target/debug/manage_subscription_plans update \
     --id "plan-uuid-here" \
     --display-name "New Name" \
     --monthly-price 89
   ```

4. **Delete a subscription plan**:
   ```bash
   ./target/debug/manage_subscription_plans delete \
     --id "plan-uuid-here"
   ```

### Using the Setup Script

For convenience, we've created a setup script that adds the default subscription plans:

```bash
cd adc-muti-languages-service
./scripts/setup_subscription_plans.sh
```

This script will:
1. Build the CLI tool if needed
2. Show existing plans
3. Ask for confirmation before proceeding
4. Add the default subscription plans (Free, Standard, Premium, Enterprise)

## Subscription Plan Structure

Each subscription plan has the following attributes:

- `id`: Unique identifier (UUID)
- `tier_name`: Internal name used in code (e.g., "free", "standard")
- `display_name`: User-friendly name shown in the UI (e.g., "Free", "Standard")
- `description`: Description of the plan
- `monthly_price`: Price for monthly billing
- `yearly_price`: Price for yearly billing (usually discounted)
- `limits`: JSON object containing plan limits (e.g., number of projects, team members)
- `features`: JSON array of feature descriptions shown in the UI
- `is_active`: Whether the plan is active and should be shown to users
- `sort_order`: Order in which plans should be displayed (lower values first)
- `created_at`: When the plan was created
- `updated_at`: When the plan was last updated

### Special Fields

- In the `limits` JSON, you can add an `is_popular: true` field to highlight a plan in the UI
- Use `-1` for unlimited values in the `limits` JSON

## API Integration

The subscription plans are exposed through the API at `/api/subscription-plans`. The frontend uses this API to display available plans to users.

## Stripe Integration

The application uses Stripe for payment processing. When a user subscribes to a plan, the application creates a subscription in Stripe and associates it with the user's organization.

Make sure the Stripe environment variables are set in the `.env` file:

```
STRIPE_SECRET_KEY=sk_test_your_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
```
