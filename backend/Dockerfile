#---------------------------------------------------------------------
# Stage 1: Builder
#---------------------------------------------------------------------
FROM rust:1.82-bullseye as builder

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libssl-dev \
    pkg-config \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /usr/src/app

# Set application names
ARG MAIN_APP=adc-muti-languages
ARG MIGRATIONS_APP=run_migrations

# Copy manifests and fetch dependencies (cached layer)
COPY Cargo.toml Cargo.lock ./
COPY diesel.toml ./diesel.toml

# Create dummy src files needed by cargo fetch
RUN mkdir -p src/bin && \
    echo "fn main(){}" > src/main.rs && \
    echo "fn main(){}" > src/bin/run_migrations.rs && \
    cargo fetch && \
    rm -f src/main.rs src/bin/run_migrations.rs

# Copy source code and build files
COPY ./src ./src
COPY ./migrations ./migrations
COPY ./db ./db
COPY ./templates ./templates
# Create static directory if it doesn't exist
RUN mkdir -p static

# Build applications in release mode
RUN cargo build --release --locked --bin ${MAIN_APP} --bin ${MIGRATIONS_APP}

#---------------------------------------------------------------------
# Stage 2: Development Runner
#---------------------------------------------------------------------
FROM debian:bullseye-slim as dev

ARG MAIN_APP=adc-muti-languages
ARG MIGRATIONS_APP=run_migrations

WORKDIR /app

# Install runtime dependencies and development tools
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    libssl1.1 \
    libpq5 \
    tzdata \
    bash \
    curl \
    postgresql-client \
    jq \
    file \
    && rm -rf /var/lib/apt/lists/* && \
    # Create non-root user
    groupadd -r app && \
    useradd -r -g app -d /app app && \
    chown -R app:app /app

# Copy compiled binaries and assets
COPY --from=builder --chown=app:app /usr/src/app/target/release/${MAIN_APP} /app/${MAIN_APP}
COPY --from=builder --chown=app:app /usr/src/app/target/release/${MIGRATIONS_APP} /app/${MIGRATIONS_APP}
COPY --from=builder --chown=app:app /usr/src/app/static /app/static
COPY --from=builder --chown=app:app /usr/src/app/templates /app/templates

# Ensure binaries are executable
RUN chmod +x /app/${MAIN_APP} /app/${MIGRATIONS_APP}

# Set up environment
EXPOSE 8080
ENV ROCKET_ADDRESS=0.0.0.0 \
    ROCKET_PORT=8080 \
    ROCKET_LOG_LEVEL=debug \
    RUST_BACKTRACE=1 \
    ROCKET_STATIC_DIR=/app/static \
    ROCKET_TEMPLATE_DIR=/app/templates

# Run as non-root user
USER app

# Command to run migrations before app starts if RUN_MIGRATIONS=true
CMD ["sh", "-c", "if [ \"${RUN_MIGRATIONS}\" = \"true\" ]; then echo \"Running database migrations...\" && /app/run_migrations; fi && echo \"Starting application on ${ROCKET_ADDRESS}:${ROCKET_PORT}...\" && exec /app/adc-muti-languages"]

#---------------------------------------------------------------------
# Stage 3: Production Runner
#---------------------------------------------------------------------
FROM debian:bullseye-slim as prod

ARG MAIN_APP=adc-muti-languages
ARG MIGRATIONS_APP=run_migrations

WORKDIR /app

# Install runtime dependencies (minimal set)
RUN apt-get update && apt-get install -y --no-install-recommends \
    ca-certificates \
    libssl1.1 \
    libpq5 \
    tzdata \
    && rm -rf /var/lib/apt/lists/* && \
    # Create non-root user
    groupadd -r app && \
    useradd -r -g app -d /app app && \
    chown -R app:app /app

# Copy compiled binaries and assets
COPY --from=builder --chown=app:app /usr/src/app/target/release/${MAIN_APP} /app/${MAIN_APP}
COPY --from=builder --chown=app:app /usr/src/app/target/release/${MIGRATIONS_APP} /app/${MIGRATIONS_APP}
COPY --from=builder --chown=app:app /usr/src/app/static /app/static
COPY --from=builder --chown=app:app /usr/src/app/templates /app/templates

# Ensure binaries are executable
RUN chmod +x /app/${MAIN_APP} /app/${MIGRATIONS_APP}

# Set up environment
EXPOSE 8080
ENV ROCKET_ADDRESS=0.0.0.0 \
    ROCKET_PORT=8080 \
    ROCKET_LOG_LEVEL=normal \
    ROCKET_STATIC_DIR=/app/static \
    ROCKET_TEMPLATE_DIR=/app/templates

# Run as non-root user
USER app

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${ROCKET_PORT}/health || exit 1

# Command to run migrations before app starts if RUN_MIGRATIONS=true
CMD ["sh", "-c", "if [ \"${RUN_MIGRATIONS}\" = \"true\" ]; then echo \"Running database migrations...\" && if [ -z \"${DATABASE_URL}\" ]; then echo \"ERROR: DATABASE_URL environment variable is not set\" && exit 1; fi && /app/run_migrations; fi && echo \"Starting application on ${ROCKET_ADDRESS}:${ROCKET_PORT}...\" && exec /app/adc-muti-languages"]
