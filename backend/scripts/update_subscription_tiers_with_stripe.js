#!/usr/bin/env node

// This script updates subscription tiers with Stripe product and price IDs
// Usage: node update_subscription_tiers_with_stripe.js

require('dotenv').config();
const { Client } = require('pg');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Check if DATABASE_URL is set
if (!process.env.DATABASE_URL) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Check if Stripe key is set
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('STRIPE_SECRET_KEY environment variable is not set');
  process.exit(1);
}

// Define the subscription tiers and their corresponding Stripe price IDs
const tiers = [
  {
    tier_name: 'free',
    stripe_price_monthly: null, // Free tier doesn't have a price
    stripe_price_yearly: null
  },
  {
    tier_name: 'starter',
    stripe_price_monthly: process.env.STRIPE_PRICE_ID_STANDARD_MONTHLY, // Map Standard to Starter
    stripe_price_yearly: process.env.STRIPE_PRICE_ID_STANDARD_YEARLY
  },
  {
    tier_name: 'professional',
    stripe_price_monthly: process.env.STRIPE_PRICE_ID_PREMIUM_MONTHLY, // Map Premium to Professional
    stripe_price_yearly: process.env.STRIPE_PRICE_ID_PREMIUM_YEARLY
  },
  {
    tier_name: 'enterprise',
    stripe_price_monthly: process.env.STRIPE_PRICE_ID_ENTERPRISE_MONTHLY,
    stripe_price_yearly: process.env.STRIPE_PRICE_ID_ENTERPRISE_YEARLY
  }
];

async function main() {
  // Connect to the database
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // First, check if the stripe_price_monthly and stripe_price_yearly columns exist
    const checkColumnsQuery = `
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'subscription_tiers'
      AND column_name IN ('stripe_price_monthly', 'stripe_price_yearly');
    `;

    const columnsResult = await client.query(checkColumnsQuery);

    // If columns don't exist, add them
    if (columnsResult.rows.length < 2) {
      console.log('Adding Stripe price columns to subscription_tiers table...');

      const addColumnsQuery = `
        ALTER TABLE subscription_tiers
        ADD COLUMN IF NOT EXISTS stripe_price_monthly VARCHAR(255),
        ADD COLUMN IF NOT EXISTS stripe_price_yearly VARCHAR(255);
      `;

      await client.query(addColumnsQuery);
      console.log('Columns added successfully');
    } else {
      console.log('Stripe price columns already exist');
    }

    // Update each tier with its Stripe price IDs
    for (const tier of tiers) {
      console.log(`Updating ${tier.tier_name} tier with Stripe price IDs...`);

      const updateQuery = `
        UPDATE subscription_tiers
        SET
          stripe_price_monthly = $1,
          stripe_price_yearly = $2,
          updated_at = NOW()
        WHERE tier_name = $3;
      `;

      const result = await client.query(updateQuery, [
        tier.stripe_price_monthly,
        tier.stripe_price_yearly,
        tier.tier_name
      ]);

      if (result.rowCount > 0) {
        console.log(`Updated ${tier.tier_name} tier successfully`);
      } else {
        console.log(`No tier found with name: ${tier.tier_name}`);
      }
    }

    // Verify the updates
    const verifyQuery = `
      SELECT tier_name, stripe_price_monthly, stripe_price_yearly
      FROM subscription_tiers
      ORDER BY sort_order;
    `;

    const verifyResult = await client.query(verifyQuery);

    console.log('\nUpdated subscription tiers:');
    console.table(verifyResult.rows);

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

main().catch(console.error);
