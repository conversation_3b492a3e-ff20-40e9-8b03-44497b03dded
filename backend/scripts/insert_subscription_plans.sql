-- Insert default subscription plans
-- Run this script to populate the subscription_tiers table with default plans

-- First, delete any existing plans (optional)
-- DELETE FROM subscription_tiers;

-- Free tier
INSERT INTO subscription_tiers (
    id, 
    tier_name, 
    display_name, 
    description, 
    monthly_price, 
    yearly_price, 
    limits, 
    features, 
    is_active, 
    sort_order
)
VALUES (
    uuid_generate_v4(), 
    'free', 
    'Free', 
    'Basic features for small projects', 
    0, 
    0, 
    '{
        "projects": 3,
        "team_members": 2,
        "ai_credits": 100,
        "storage_mb": 100
    }'::jsonb, 
    '[
        "Up to 3 projects",
        "2 team members",
        "100 AI translation credits/month",
        "Basic support"
    ]'::jsonb, 
    true, 
    1
);

-- Standard tier
INSERT INTO subscription_tiers (
    id, 
    tier_name, 
    display_name, 
    description, 
    monthly_price, 
    yearly_price, 
    limits, 
    features, 
    is_active, 
    sort_order
)
VALUES (
    uuid_generate_v4(), 
    'standard', 
    'Standard', 
    'Perfect for growing teams', 
    29, 
    290, 
    '{
        "projects": -1,
        "team_members": 10,
        "ai_credits": 1000,
        "storage_mb": 1000
    }'::jsonb, 
    '[
        "Unlimited projects",
        "10 team members",
        "1,000 AI translation credits/month",
        "Priority support",
        "Advanced analytics",
        "Custom export formats"
    ]'::jsonb, 
    true, 
    2
);

-- Premium tier
INSERT INTO subscription_tiers (
    id, 
    tier_name, 
    display_name, 
    description, 
    monthly_price, 
    yearly_price, 
    limits, 
    features, 
    is_active, 
    sort_order
)
VALUES (
    uuid_generate_v4(), 
    'premium', 
    'Premium', 
    'For professional translation needs', 
    79, 
    790, 
    '{
        "projects": -1,
        "team_members": -1,
        "ai_credits": 5000,
        "storage_mb": 5000
    }'::jsonb, 
    '[
        "Unlimited projects",
        "Unlimited team members",
        "5,000 AI translation credits/month",
        "24/7 priority support",
        "Advanced analytics",
        "Custom export formats",
        "API access",
        "Custom integrations"
    ]'::jsonb, 
    true, 
    3
);

-- Enterprise tier
INSERT INTO subscription_tiers (
    id, 
    tier_name, 
    display_name, 
    description, 
    monthly_price, 
    yearly_price, 
    limits, 
    features, 
    is_active, 
    sort_order
)
VALUES (
    uuid_generate_v4(), 
    'enterprise', 
    'Enterprise', 
    'Custom solutions for large organizations', 
    299, 
    2990, 
    '{
        "projects": -1,
        "team_members": -1,
        "ai_credits": 20000,
        "storage_mb": 20000
    }'::jsonb, 
    '[
        "Unlimited projects",
        "Unlimited team members",
        "20,000 AI translation credits/month",
        "24/7 dedicated support",
        "Advanced analytics",
        "Custom export formats",
        "API access",
        "Custom integrations",
        "Dedicated account manager",
        "Custom SLA",
        "On-premise deployment option"
    ]'::jsonb, 
    true, 
    4
);
