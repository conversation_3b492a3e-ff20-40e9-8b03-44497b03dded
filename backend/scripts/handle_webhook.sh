#!/bin/bash

# This script handles Stripe webhook events
# Usage: ./handle_webhook.sh <event_type> <event_data>

# Check if all required arguments are provided
if [ "$#" -ne 2 ]; then
    echo "Usage: $0 <event_type> <event_data>"
    exit 1
fi

EVENT_TYPE=$1
EVENT_DATA=$2

# Log the event
echo "Received webhook event: $EVENT_TYPE"
echo "Event data: $EVENT_DATA"

# Handle different event types
case "$EVENT_TYPE" in
    "checkout.session.completed")
        # Extract subscription ID and customer ID from the event data
        SUBSCRIPTION_ID=$(echo "$EVENT_DATA" | jq -r '.subscription')
        CUSTOMER_ID=$(echo "$EVENT_DATA" | jq -r '.customer')
        CLIENT_REFERENCE_ID=$(echo "$EVENT_DATA" | jq -r '.client_reference_id')
        
        echo "Subscription created: $SUBSCRIPTION_ID"
        echo "Customer: $CUSTOMER_ID"
        echo "Organization ID: $CLIENT_REFERENCE_ID"
        
        # Here you would update your database with the subscription information
        # For now, we'll just log it
        ;;
        
    "invoice.payment_succeeded")
        # Extract subscription ID and customer ID from the event data
        SUBSCRIPTION_ID=$(echo "$EVENT_DATA" | jq -r '.subscription')
        CUSTOMER_ID=$(echo "$EVENT_DATA" | jq -r '.customer')
        
        echo "Payment succeeded for subscription: $SUBSCRIPTION_ID"
        echo "Customer: $CUSTOMER_ID"
        
        # Here you would update your database with the payment information
        # For now, we'll just log it
        ;;
        
    "customer.subscription.updated")
        # Extract subscription ID and status from the event data
        SUBSCRIPTION_ID=$(echo "$EVENT_DATA" | jq -r '.id')
        STATUS=$(echo "$EVENT_DATA" | jq -r '.status')
        
        echo "Subscription updated: $SUBSCRIPTION_ID"
        echo "New status: $STATUS"
        
        # Here you would update your database with the subscription status
        # For now, we'll just log it
        ;;
        
    "customer.subscription.deleted")
        # Extract subscription ID from the event data
        SUBSCRIPTION_ID=$(echo "$EVENT_DATA" | jq -r '.id')
        
        echo "Subscription deleted: $SUBSCRIPTION_ID"
        
        # Here you would update your database to mark the subscription as canceled
        # For now, we'll just log it
        ;;
        
    *)
        echo "Unhandled event type: $EVENT_TYPE"
        ;;
esac
