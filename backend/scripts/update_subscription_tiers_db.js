#!/usr/bin/env node

// This script updates the subscription_tiers table with Stripe product and price IDs
// Usage: node update_subscription_tiers_db.js

require('dotenv').config();
const { Client } = require('pg');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Check if DATABASE_URL is set
if (!process.env.DATABASE_URL) {
  console.error('DATABASE_URL environment variable is not set');
  process.exit(1);
}

// Check if Stripe key is set
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('STRIPE_SECRET_KEY environment variable is not set');
  process.exit(1);
}

// Define the mapping between tier names and Stripe price IDs
const tierMapping = [
  {
    tier_name: 'free',
    stripe_price_monthly: null,
    stripe_price_yearly: null
  },
  {
    tier_name: 'starter',
    stripe_price_monthly: process.env.STRIPE_PRICE_ID_STANDARD_MONTHLY,
    stripe_price_yearly: process.env.STRIPE_PRICE_ID_STANDARD_YEARLY
  },
  {
    tier_name: 'professional',
    stripe_price_monthly: process.env.STRIPE_PRICE_ID_PREMIUM_MONTHLY,
    stripe_price_yearly: process.env.STRIPE_PRICE_ID_PREMIUM_YEARLY
  },
  {
    tier_name: 'enterprise',
    stripe_price_monthly: process.env.STRIPE_PRICE_ID_ENTERPRISE_MONTHLY,
    stripe_price_yearly: process.env.STRIPE_PRICE_ID_ENTERPRISE_YEARLY
  }
];

async function main() {
  // Connect to the database
  const client = new Client({
    connectionString: process.env.DATABASE_URL
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // First, update the schema to add Stripe-related columns if they don't exist
    const updateSchemaQuery = `
      ALTER TABLE subscription_tiers 
      ADD COLUMN IF NOT EXISTS stripe_product_id VARCHAR(255),
      ADD COLUMN IF NOT EXISTS stripe_price_monthly VARCHAR(255),
      ADD COLUMN IF NOT EXISTS stripe_price_yearly VARCHAR(255);
    `;
    
    await client.query(updateSchemaQuery);
    console.log('Schema updated successfully');

    // Get all products from Stripe
    const products = await stripe.products.list({ active: true, limit: 100 });
    console.log(`Found ${products.data.length} products in Stripe`);

    // Get all prices from Stripe
    const prices = await stripe.prices.list({ active: true, limit: 100 });
    console.log(`Found ${prices.data.length} prices in Stripe`);

    // Create a map of product IDs to their prices
    const productPrices = {};
    for (const price of prices.data) {
      if (!productPrices[price.product]) {
        productPrices[price.product] = {
          monthly: null,
          yearly: null
        };
      }

      if (price.recurring && price.recurring.interval === 'month') {
        productPrices[price.product].monthly = price.id;
      } else if (price.recurring && price.recurring.interval === 'year') {
        productPrices[price.product].yearly = price.id;
      }
    }

    // Update each tier with its Stripe product and price IDs
    for (const tier of tierMapping) {
      console.log(`Updating ${tier.tier_name} tier with Stripe price IDs...`);
      
      // Find the product that matches this tier
      let productId = null;
      for (const product of products.data) {
        const tierNameMeta = product.metadata && product.metadata.tier_name;
        if (tierNameMeta && tierNameMeta.toLowerCase() === tier.tier_name) {
          productId = product.id;
          break;
        }
        
        // If no tier_name metadata, try to match by name
        if (!productId && product.name.toLowerCase() === tier.tier_name) {
          productId = product.id;
          break;
        }
      }

      // If we found a product, use its price IDs
      let monthlyPriceId = tier.stripe_price_monthly;
      let yearlyPriceId = tier.stripe_price_yearly;
      
      if (productId && productPrices[productId]) {
        if (!monthlyPriceId) {
          monthlyPriceId = productPrices[productId].monthly;
        }
        if (!yearlyPriceId) {
          yearlyPriceId = productPrices[productId].yearly;
        }
      }
      
      const updateQuery = `
        UPDATE subscription_tiers 
        SET 
          stripe_product_id = $1,
          stripe_price_monthly = $2,
          stripe_price_yearly = $3,
          updated_at = NOW()
        WHERE tier_name = $4;
      `;
      
      const result = await client.query(updateQuery, [
        productId,
        monthlyPriceId,
        yearlyPriceId,
        tier.tier_name
      ]);
      
      if (result.rowCount > 0) {
        console.log(`Updated ${tier.tier_name} tier successfully`);
      } else {
        console.log(`No tier found with name: ${tier.tier_name}`);
      }
    }

    // Verify the updates
    const verifyQuery = `
      SELECT tier_name, display_name, stripe_product_id, stripe_price_monthly, stripe_price_yearly 
      FROM subscription_tiers 
      ORDER BY sort_order;
    `;
    
    const verifyResult = await client.query(verifyQuery);
    
    console.log('\nUpdated subscription tiers:');
    console.table(verifyResult.rows);

  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  } finally {
    await client.end();
    console.log('Database connection closed');
  }
}

main().catch(console.error);
