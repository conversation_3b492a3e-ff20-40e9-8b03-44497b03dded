#!/bin/bash

# This script creates a Stripe Checkout session for subscription products
# Usage: ./create_checkout_session.sh <price_id> <organization_id> <success_url> <cancel_url>

# Check if all required arguments are provided
if [ "$#" -ne 4 ]; then
    echo "Usage: $0 <price_id> <organization_id> <success_url> <cancel_url>"
    exit 1
fi

PRICE_ID=$1
ORGANIZATION_ID=$2
SUCCESS_URL=$3
CANCEL_URL=$4

# Create the checkout session
stripe checkout.sessions create \
  --success_url="$SUCCESS_URL" \
  --cancel_url="$CANCEL_URL" \
  --mode=subscription \
  --line_items[][price]="$PRICE_ID" \
  --line_items[][quantity]=1 \
  --client_reference_id="$ORGANIZATION_ID" \
  --metadata[organization_id]="$ORGANIZATION_ID"
