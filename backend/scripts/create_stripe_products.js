#!/usr/bin/env node

// This script creates Stripe products and prices for subscription plans
// Usage: node create_stripe_products.js

require('dotenv').config();
console.log('Stripe Key:', process.env.STRIPE_SECRET_KEY ? 'Found' : 'Not found');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

const plans = [
  {
    name: 'Standard',
    description: 'For small teams and startups',
    features: [
      'Up to 5 projects',
      'Up to 10 team members',
      'Basic translation features',
      'Email support'
    ],
    priceMonthly: 1999, // $19.99
    priceYearly: 19990, // $199.90 (2 months free)
    isPopular: false
  },
  {
    name: 'Premium',
    description: 'For growing businesses',
    features: [
      'Up to 20 projects',
      'Up to 50 team members',
      'Advanced translation features',
      'AI-powered translations',
      'Priority support'
    ],
    priceMonthly: 4999, // $49.99
    priceYearly: 49990, // $499.90 (2 months free)
    isPopular: true
  },
  {
    name: 'Enterprise',
    description: 'For large organizations',
    features: [
      'Unlimited projects',
      'Unlimited team members',
      'All premium features',
      'Custom integrations',
      'Dedicated account manager',
      '24/7 support'
    ],
    priceMonthly: 9999, // $99.99
    priceYearly: 99990, // $999.90 (2 months free)
    isPopular: false
  }
];

async function createProducts() {
  console.log('Creating Stripe products and prices...');

  for (const plan of plans) {
    try {
      // Create product
      console.log(`Creating product: ${plan.name}`);
      const product = await stripe.products.create({
        name: plan.name,
        description: plan.description,
        metadata: {
          features: JSON.stringify(plan.features),
          is_popular: plan.isPopular.toString()
        }
      });

      console.log(`Product created: ${product.id}`);

      // Create monthly price
      console.log(`Creating monthly price for ${plan.name}`);
      const monthlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.priceMonthly,
        currency: 'usd',
        recurring: {
          interval: 'month'
        },
        metadata: {
          tier_name: plan.name.toLowerCase(),
          is_yearly: 'false'
        }
      });

      console.log(`Monthly price created: ${monthlyPrice.id}`);

      // Create yearly price
      console.log(`Creating yearly price for ${plan.name}`);
      const yearlyPrice = await stripe.prices.create({
        product: product.id,
        unit_amount: plan.priceYearly,
        currency: 'usd',
        recurring: {
          interval: 'year'
        },
        metadata: {
          tier_name: plan.name.toLowerCase(),
          is_yearly: 'true'
        }
      });

      console.log(`Yearly price created: ${yearlyPrice.id}`);

      // Output environment variables
      console.log('\nAdd these to your .env file:');
      console.log(`STRIPE_PRICE_ID_${plan.name.toUpperCase()}_MONTHLY=${monthlyPrice.id}`);
      console.log(`STRIPE_PRICE_ID_${plan.name.toUpperCase()}_YEARLY=${yearlyPrice.id}`);
      console.log('');
    } catch (error) {
      console.error(`Error creating product ${plan.name}:`, error);
    }
  }

  console.log('Done!');
}

createProducts().catch(error => {
  console.error('Error:', error);
  process.exit(1);
});
