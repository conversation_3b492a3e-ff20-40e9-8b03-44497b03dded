#!/bin/bash

# This script sets up the Stripe environment variables for the application
# It should be run from the root of the project

# Check if .env file exists
if [ ! -f .env ]; then
    echo "Error: .env file not found. Please create it first."
    exit 1
fi

# Prompt for Stripe API key
read -p "Enter your Stripe API key (starts with sk_): " STRIPE_API_KEY

# Validate Stripe API key format
if [[ ! $STRIPE_API_KEY =~ ^sk_ ]]; then
    echo "Error: Invalid Stripe API key format. It should start with 'sk_'."
    exit 1
fi

# Prompt for Stripe webhook secret
read -p "Enter your Stripe webhook secret (starts with whsec_): " STRIPE_WEBHOOK_SECRET

# Validate webhook secret format
if [[ ! $STRIPE_WEBHOOK_SECRET =~ ^whsec_ ]]; then
    echo "Error: Invalid Stripe webhook secret format. It should start with 'whsec_'."
    exit 1
fi

# Check if the variables already exist in .env
if grep -q "STRIPE_SECRET_KEY" .env; then
    # Update existing variables
    sed -i '' "s|STRIPE_SECRET_KEY=.*|STRIPE_SECRET_KEY=$STRIPE_API_KEY|g" .env
    echo "Updated STRIPE_SECRET_KEY in .env"
else
    # Add new variables
    echo "" >> .env
    echo "# Stripe configuration" >> .env
    echo "STRIPE_SECRET_KEY=$STRIPE_API_KEY" >> .env
    echo "Added STRIPE_SECRET_KEY to .env"
fi

if grep -q "STRIPE_WEBHOOK_SECRET" .env; then
    sed -i '' "s|STRIPE_WEBHOOK_SECRET=.*|STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET|g" .env
    echo "Updated STRIPE_WEBHOOK_SECRET in .env"
else
    echo "STRIPE_WEBHOOK_SECRET=$STRIPE_WEBHOOK_SECRET" >> .env
    echo "Added STRIPE_WEBHOOK_SECRET to .env"
fi

# Prompt for price IDs
echo ""
echo "Now, let's set up the price IDs for each subscription tier."
echo "You can find these in your Stripe dashboard under Products > [Product] > Pricing."
echo ""

# Function to prompt for price ID and add/update it in .env
add_price_id() {
    local var_name=$1
    local description=$2
    
    read -p "Enter the price ID for $description (starts with price_): " PRICE_ID
    
    # Validate price ID format
    if [[ ! $PRICE_ID =~ ^price_ ]]; then
        echo "Warning: Price ID doesn't start with 'price_'. Are you sure this is correct? (y/n)"
        read confirm
        if [[ ! $confirm =~ ^[Yy] ]]; then
            echo "Skipping $var_name"
            return
        fi
    fi
    
    if grep -q "$var_name" .env; then
        sed -i '' "s|$var_name=.*|$var_name=$PRICE_ID|g" .env
        echo "Updated $var_name in .env"
    else
        echo "$var_name=$PRICE_ID" >> .env
        echo "Added $var_name to .env"
    fi
}

# Add price IDs for each tier
add_price_id "STRIPE_PRICE_ID_FREE" "Free tier"
add_price_id "STRIPE_PRICE_ID_STANDARD" "Standard tier (monthly)"
add_price_id "STRIPE_PRICE_ID_STANDARD_YEARLY" "Standard tier (yearly)"
add_price_id "STRIPE_PRICE_ID_PREMIUM" "Premium tier (monthly)"
add_price_id "STRIPE_PRICE_ID_PREMIUM_YEARLY" "Premium tier (yearly)"
add_price_id "STRIPE_PRICE_ID_ENTERPRISE" "Enterprise tier (monthly)"
add_price_id "STRIPE_PRICE_ID_ENTERPRISE_YEARLY" "Enterprise tier (yearly)"

echo ""
echo "Stripe environment variables have been set up successfully!"
echo "You can now restart the application to apply the changes."
