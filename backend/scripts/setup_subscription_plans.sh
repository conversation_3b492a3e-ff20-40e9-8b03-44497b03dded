#!/bin/bash

# <PERSON>ript to set up subscription plans in the database
# This script reads the DATABASE_URL from the .env file and runs the SQL script

# Ensure the script exits on any error
set -e

# Load environment variables from .env file
if [ -f .env ]; then
    export $(grep -v '^#' .env | xargs)
else
    echo "Error: .env file not found"
    exit 1
fi

# Check if DATABASE_URL is set
if [ -z "$DATABASE_URL" ]; then
    echo "Error: DATABASE_URL is not set in .env file"
    exit 1
fi

echo "Setting up subscription plans..."

# Ask for confirmation before proceeding
read -p "Do you want to add the default subscription plans? (y/n) " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Operation cancelled."
    exit 0
fi

# Run the SQL script
echo "Inserting subscription plans into the database..."
psql "$DATABASE_URL" -f scripts/insert_subscription_plans.sql

# Check if the command was successful
if [ $? -eq 0 ]; then
    echo "Subscription plans inserted successfully"
else
    echo "Error: Failed to insert subscription plans"
    exit 1
fi

echo "Subscription plans setup complete!"
