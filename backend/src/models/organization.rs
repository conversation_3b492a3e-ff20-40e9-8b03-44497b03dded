use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::{organizations, organization_members};

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = organizations)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct Organization {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub owner_id: Uuid,
    pub subscription_tier: String,
    pub subscription_status: Option<String>,
    pub billing_period_start: Option<DateTime<Utc>>,
    pub billing_period_end: Option<DateTime<Utc>>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub deleted_at: Option<DateTime<Utc>>,
    pub stripe_customer_id: Option<String>,
    pub stripe_subscription_id: Option<String>,
    pub ai_credits_monthly_allowance: Option<i32>,
    pub ai_credits_remaining: Option<i32>,
    pub ai_credits_reset_date: Option<DateTime<Utc>>,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub website: Option<String>,
    pub subscription_tier_id: Option<Uuid>,
    pub subscription_auto_renew: Option<bool>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = organizations)]
pub struct NewOrganization {
    pub name: String,
    pub slug: String,
    pub owner_id: Uuid,
    pub subscription_tier: String,
    pub subscription_status: Option<String>,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub website: Option<String>,
    pub stripe_customer_id: Option<String>,
    pub stripe_subscription_id: Option<String>,
    pub subscription_tier_id: Option<Uuid>,
    pub subscription_auto_renew: Option<bool>,
    pub billing_period_start: Option<DateTime<Utc>>,
    pub billing_period_end: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, AsChangeset)]
#[diesel(table_name = organizations)]
pub struct UpdateOrganization {
    pub name: Option<String>,
    pub slug: Option<String>,
    pub subscription_tier: Option<String>,
    pub subscription_status: Option<String>,
    pub description: Option<String>,
    pub logo_url: Option<String>,
    pub website: Option<String>,
    pub updated_at: Option<DateTime<Utc>>,
    pub stripe_customer_id: Option<String>,
    pub stripe_subscription_id: Option<String>,
    pub subscription_tier_id: Option<Uuid>,
    pub subscription_auto_renew: Option<bool>,
    pub billing_period_start: Option<DateTime<Utc>>,
    pub billing_period_end: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = organization_members)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct OrganizationMember {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub user_id: Uuid,
    pub role: String,
    pub invited_at: Option<DateTime<Utc>>,
    pub joined_at: Option<DateTime<Utc>>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = organization_members)]
pub struct NewOrganizationMember {
    pub organization_id: Uuid,
    pub user_id: Uuid,
    pub role: String,
}

// Commented out due to Numeric type compatibility issues
// #[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
// #[diesel(table_name = subscription_tiers)]
// #[diesel(check_for_backend(diesel::pg::Pg))]
// pub struct SubscriptionTier {
//     pub id: Uuid,
//     pub tier_name: String,
//     pub display_name: String,
//     pub description: Option<String>,
//     pub monthly_price: BigDecimal,
//     pub yearly_price: Option<BigDecimal>,
//     pub limits: serde_json::Value,
//     pub features: serde_json::Value,
//     pub is_active: Option<bool>,
//     pub sort_order: Option<i32>,
//     pub created_at: Option<DateTime<Utc>>,
//     pub updated_at: Option<DateTime<Utc>>,
// }
