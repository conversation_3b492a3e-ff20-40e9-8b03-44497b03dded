use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Commented out because the table doesn't exist in the database
// use crate::schema::payment_methods;

// Commented out diesel annotations because the table doesn't exist
#[derive(Debug, Serialize, Deserialize)]
// #[diesel(table_name = payment_methods)]
pub struct PaymentMethod {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub stripe_payment_method_id: String,
    pub payment_type: String,
    pub last_four: Option<String>,
    pub card_brand: Option<String>,
    pub expiry_month: Option<i32>,
    pub expiry_year: Option<i32>,
    pub is_default: bool,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
// #[diesel(table_name = payment_methods)]
pub struct NewPaymentMethod {
    pub organization_id: Uuid,
    pub stripe_payment_method_id: String,
    pub payment_type: String,
    pub last_four: Option<String>,
    pub card_brand: Option<String>,
    pub expiry_month: Option<i32>,
    pub expiry_year: Option<i32>,
    pub is_default: bool,
}

#[derive(Debug, Deserialize)]
// #[diesel(table_name = payment_methods)]
pub struct UpdatePaymentMethod {
    pub payment_type: Option<String>,
    pub last_four: Option<String>,
    pub card_brand: Option<String>,
    pub expiry_month: Option<i32>,
    pub expiry_year: Option<i32>,
    pub is_default: Option<bool>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct PaymentMethodResponse {
    pub id: Uuid,
    pub stripe_payment_method_id: String,
    pub payment_type: String,
    pub card_brand: Option<String>,
    pub last_four: Option<String>,
    pub expiry_month: Option<i32>,
    pub expiry_year: Option<i32>,
    pub is_default: bool,
}

impl From<PaymentMethod> for PaymentMethodResponse {
    fn from(pm: PaymentMethod) -> Self {
        Self {
            id: pm.id,
            stripe_payment_method_id: pm.stripe_payment_method_id,
            payment_type: pm.payment_type,
            card_brand: pm.card_brand,
            last_four: pm.last_four,
            expiry_month: pm.expiry_month,
            expiry_year: pm.expiry_year,
            is_default: pm.is_default,
        }
    }
}
