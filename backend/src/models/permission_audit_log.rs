use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::permission_audit_logs;

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = permission_audit_logs)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct PermissionAuditLog {
    pub id: Uuid,
    pub api_key_id: Uuid,
    pub organization_id: Uuid,
    pub permission_key: String,
    pub resource_path: String,
    pub method: String,
    pub granted: bool,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = permission_audit_logs)]
pub struct NewPermissionAuditLog {
    pub api_key_id: Uuid,
    pub organization_id: Uuid,
    pub permission_key: String,
    pub resource_path: String,
    pub method: String,
    pub granted: bool,
}

#[derive(Debug, Serialize)]
pub struct PermissionAuditLogResponse {
    pub id: String,
    pub api_key_id: String,
    pub permission_key: String,
    pub resource_path: String,
    pub method: String,
    pub granted: bool,
    pub created_at: DateTime<Utc>,
}

impl From<PermissionAuditLog> for PermissionAuditLogResponse {
    fn from(log: PermissionAuditLog) -> Self {
        PermissionAuditLogResponse {
            id: log.id.to_string(),
            api_key_id: log.api_key_id.to_string(),
            permission_key: log.permission_key,
            resource_path: log.resource_path,
            method: log.method,
            granted: log.granted,
            created_at: log.created_at,
        }
    }
}
