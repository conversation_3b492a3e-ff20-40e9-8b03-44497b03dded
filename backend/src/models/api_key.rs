use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::api_keys;

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable, Clone)]
#[diesel(table_name = api_keys)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct ApiKey {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub name: String,
    pub key_hash: String,
    pub prefix: String,
    pub permissions: serde_json::Value,
    pub rate_limit: Option<i32>,
    pub expires_at: Option<DateTime<Utc>>,
    pub last_used_at: Option<DateTime<Utc>>,
    pub created_by: Uuid,
    pub created_at: Option<DateTime<Utc>>,
    pub revoked_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = api_keys)]
pub struct NewApiKey {
    pub organization_id: Uuid,
    pub name: String,
    pub key_hash: String,
    pub prefix: String,
    pub permissions: serde_json::Value,
    pub rate_limit: Option<i32>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_by: Uuid,
}

#[derive(Debug, Deserialize)]
pub struct CreateApiKeyRequest {
    pub name: String,
    pub permissions: Option<Vec<String>>,
    pub rate_limit: Option<i32>,
    pub rate_limit_period: Option<String>, // "minute", "hour", "day"
    pub expires_at: Option<DateTime<Utc>>,
    pub permission_group_id: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ApiKeyResponse {
    pub id: String,
    pub name: String,
    pub prefix: String,
    pub permissions: Vec<String>,
    pub rate_limit: Option<i32>,
    pub rate_limit_period: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub last_used_at: Option<DateTime<Utc>>,
    pub created_at: Option<DateTime<Utc>>,
    pub revoked_at: Option<DateTime<Utc>>,
    pub is_active: bool,
    pub permission_group_id: Option<String>,
    pub permission_group_name: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ApiKeyCreatedResponse {
    pub id: String,
    pub name: String,
    pub key: String,
    pub prefix: String,
    pub permissions: Vec<String>,
    pub rate_limit: Option<i32>,
    pub rate_limit_period: Option<String>,
    pub expires_at: Option<DateTime<Utc>>,
    pub created_at: Option<DateTime<Utc>>,
    pub permission_group_id: Option<String>,
    pub permission_group_name: Option<String>,
}

impl From<ApiKey> for ApiKeyResponse {
    fn from(api_key: ApiKey) -> Self {
        let permissions = match api_key.permissions.as_object() {
            Some(obj) => obj.keys()
                .filter(|k| *k != "rate_limit_period") // Filter out internal fields
                .map(|k| k.to_string())
                .collect(),
            None => Vec::new(),
        };

        // Extract rate limit period from JSON if available
        let rate_limit_period = match api_key.permissions.get("rate_limit_period") {
            Some(period) => period.as_str().map(|s| s.to_string()),
            None => Some("minute".to_string()), // Default to minute
        };

        ApiKeyResponse {
            id: api_key.id.to_string(),
            name: api_key.name,
            prefix: api_key.prefix,
            permissions,
            rate_limit: api_key.rate_limit,
            rate_limit_period,
            expires_at: api_key.expires_at,
            last_used_at: api_key.last_used_at,
            created_at: api_key.created_at,
            revoked_at: api_key.revoked_at,
            is_active: api_key.revoked_at.is_none() &&
                      (api_key.expires_at.is_none() || api_key.expires_at.unwrap() > Utc::now()),
            permission_group_id: None,
            permission_group_name: None, // This will be filled in by the repository
        }
    }
}
