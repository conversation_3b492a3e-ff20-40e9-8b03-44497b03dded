use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::permission_groups;

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = permission_groups)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct PermissionGroup {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub name: String,
    pub description: Option<String>,
    pub permissions: serde_json::Value,
    pub created_by: Uuid,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = permission_groups)]
pub struct NewPermissionGroup {
    pub organization_id: Uuid,
    pub name: String,
    pub description: String,
    pub permissions: serde_json::Value,
    pub created_by: Uuid,
}

#[derive(Debug, Deserialize)]
pub struct CreatePermissionGroupRequest {
    pub name: String,
    pub description: String,
    pub permissions: Vec<String>,
}

#[derive(Debug, Deserialize)]
pub struct UpdatePermissionGroupRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub permissions: Option<Vec<String>>,
}

#[derive(Debug, Serialize)]
pub struct PermissionGroupResponse {
    pub id: String,
    pub name: String,
    pub description: String,
    pub permissions: Vec<String>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

impl From<PermissionGroup> for PermissionGroupResponse {
    fn from(group: PermissionGroup) -> Self {
        let permissions = match group.permissions.as_object() {
            Some(obj) => obj.keys().map(|k| k.to_string()).collect(),
            None => Vec::new(),
        };

        PermissionGroupResponse {
            id: group.id.to_string(),
            name: group.name,
            description: group.description.unwrap_or_default(),
            permissions,
            created_at: group.created_at,
            updated_at: group.updated_at,
        }
    }
}
