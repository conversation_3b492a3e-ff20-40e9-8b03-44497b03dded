use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::users;

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = users)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct User {
    pub id: Uuid,
    pub email: String,
    pub username: String,
    #[serde(skip_serializing)]
    pub password_hash: String,
    pub full_name: Option<String>,
    pub profile_image_url: Option<String>,
    pub preferred_language: Option<String>,
    pub is_active: Option<bool>,
    pub email_verified: Option<bool>,
    pub last_login_at: Option<DateTime<Utc>>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = users)]
pub struct NewUser {
    pub email: String,
    pub username: String,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub profile_image_url: Option<String>,
    pub preferred_language: Option<String>,
    pub is_active: Option<bool>,
    pub email_verified: Option<bool>,
}

#[derive(Debug, Deserialize, AsChangeset)]
#[diesel(table_name = users)]
pub struct UpdateUser {
    pub username: Option<String>,
    pub full_name: Option<String>,
    pub profile_image_url: Option<String>,
    pub preferred_language: Option<String>,
    pub is_active: Option<bool>,
    pub email_verified: Option<bool>,
    pub updated_at: Option<DateTime<Utc>>,
    pub password_hash: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserProfile {
    pub id: Uuid,
    pub email: String,
    pub username: String,
    pub full_name: Option<String>,
    pub profile_image_url: Option<String>,
    pub preferred_language: Option<String>,
}

impl From<User> for UserProfile {
    fn from(user: User) -> Self {
        Self {
            id: user.id,
            email: user.email,
            username: user.username,
            full_name: user.full_name,
            profile_image_url: user.profile_image_url,
            preferred_language: user.preferred_language,
        }
    }
}
