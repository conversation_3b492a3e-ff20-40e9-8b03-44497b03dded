use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::locales;

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = locales)]
pub struct Locale {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub native_name: String,
    pub text_direction: Option<String>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = locales)]
pub struct NewLocale {
    pub code: String,
    pub name: String,
    pub native_name: String,
    pub text_direction: Option<String>,
    pub is_active: Option<bool>,
}
