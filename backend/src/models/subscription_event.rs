use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::subscription_events;

#[derive(Debug, Queryable, Serialize)]
pub struct SubscriptionEvent {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub event_type: String,
    pub details: serde_json::Value,
    pub created_at: DateTime<Utc>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Insertable, Deserialize)]
#[diesel(table_name = subscription_events)]
pub struct NewSubscriptionEvent {
    pub organization_id: Uuid,
    pub event_type: String,
    pub details: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct SubscriptionEventResponse {
    pub id: String,
    pub organization_id: String,
    pub event_type: String,
    pub details: serde_json::Value,
    pub created_at: String,
}

#[derive(Debug, Serialize)]
pub struct SubscriptionHistoryResponse {
    pub events: Vec<SubscriptionEventResponse>,
}

#[derive(Debug, Serialize)]
pub struct AICreditUsageResponse {
    pub total: i32,
    pub used: i32,
    pub remaining: i32,
    pub reset_date: String,
}
