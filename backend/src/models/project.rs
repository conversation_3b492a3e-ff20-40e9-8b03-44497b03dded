use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::{projects, project_locales, resources};

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = projects)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct Project {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub default_locale: Option<String>,
    pub is_public: Option<bool>,
    pub created_by: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub deleted_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = projects)]
pub struct NewProject {
    pub organization_id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub default_locale: Option<String>,
    pub is_public: Option<bool>,
    pub created_by: Option<Uuid>,
}

#[derive(Debug, Deserialize, AsChangeset)]
#[diesel(table_name = projects)]
pub struct UpdateProject {
    pub name: Option<String>,
    pub slug: Option<String>,
    pub description: Option<String>,
    pub default_locale: Option<String>,
    pub is_public: Option<bool>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = project_locales)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct ProjectLocale {
    pub id: Uuid,
    pub project_id: Uuid,
    pub locale_id: Uuid,
    pub is_source: Option<bool>,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = project_locales)]
pub struct NewProjectLocale {
    pub project_id: Uuid,
    pub locale_id: Uuid,
    pub is_source: Option<bool>,
    pub is_active: Option<bool>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = resources)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct Resource {
    pub id: Uuid,
    pub project_id: Uuid,
    pub name: String,
    pub type_: String,
    pub path: Option<String>,
    pub description: Option<String>,
    pub created_by: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub deleted_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = resources)]
pub struct NewResource {
    pub project_id: Uuid,
    pub name: String,
    pub type_: String,
    pub path: Option<String>,
    pub description: Option<String>,
    pub created_by: Option<Uuid>,
}
