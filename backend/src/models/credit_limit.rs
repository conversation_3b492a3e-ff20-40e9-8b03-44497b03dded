use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Commented out because the table doesn't exist in the database
// use crate::schema::credit_limit;

// Commented out diesel annotations because the table doesn't exist
#[derive(Debug, Serialize, Deserialize)]
// #[diesel(table_name = credit_limit)]
pub struct CreditLimit {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub monthly_limit: i32,
    pub warning_threshold: i32,
    pub auto_purchase_enabled: bool,
    pub auto_purchase_amount: i32,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize)]
// #[diesel(table_name = credit_limit)]
pub struct NewCreditLimit {
    pub organization_id: Uuid,
    pub monthly_limit: i32,
    pub warning_threshold: i32,
    pub auto_purchase_enabled: bool,
    pub auto_purchase_amount: i32,
}

#[derive(Debug, Deserialize)]
// #[diesel(table_name = credit_limit)]
pub struct UpdateCreditLimit {
    pub monthly_limit: Option<i32>,
    pub warning_threshold: Option<i32>,
    pub auto_purchase_enabled: Option<bool>,
    pub auto_purchase_amount: Option<i32>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct CreditLimitRequest {
    pub monthly_limit: Option<i32>,
    pub warning_threshold: Option<i32>,
    pub auto_purchase_enabled: Option<bool>,
    pub auto_purchase_amount: Option<i32>,
}

#[derive(Debug, Serialize)]
pub struct CreditLimitResponse {
    pub id: String,
    pub organization_id: String,
    pub monthly_limit: i32,
    pub warning_threshold: i32,
    pub auto_purchase_enabled: bool,
    pub auto_purchase_amount: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl From<CreditLimit> for CreditLimitResponse {
    fn from(limit: CreditLimit) -> Self {
        CreditLimitResponse {
            id: limit.id.to_string(),
            organization_id: limit.organization_id.to_string(),
            monthly_limit: limit.monthly_limit,
            warning_threshold: limit.warning_threshold,
            auto_purchase_enabled: limit.auto_purchase_enabled,
            auto_purchase_amount: limit.auto_purchase_amount,
            created_at: limit.created_at.unwrap_or_else(Utc::now),
            updated_at: limit.updated_at.unwrap_or_else(Utc::now),
        }
    }
}
