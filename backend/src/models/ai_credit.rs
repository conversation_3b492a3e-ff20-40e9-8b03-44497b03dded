use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::{ai_credit_transactions, ai_credit_usage, ai_credit_pricing};

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = ai_credit_transactions)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct AICreditTransaction {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub amount: i32,
    pub transaction_type: String,
    pub description: Option<String>,
    pub stripe_payment_intent_id: Option<String>,
    pub stripe_invoice_id: Option<String>,
    pub created_by: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = ai_credit_transactions)]
pub struct NewAICreditTransaction {
    pub organization_id: Uuid,
    pub amount: i32,
    pub transaction_type: String,
    pub description: Option<String>,
    pub stripe_payment_intent_id: Option<String>,
    pub stripe_invoice_id: Option<String>,
    pub created_by: Option<Uuid>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = ai_credit_usage)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct AICreditUsage {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub user_id: Option<Uuid>,
    pub credits_used: i32,
    pub operation: String,
    pub source_locale: Option<String>,
    pub target_locale: Option<String>,
    pub text_length: Option<i32>,
    pub model_used: Option<String>,
    pub created_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = ai_credit_usage)]
pub struct NewAICreditUsage {
    pub organization_id: Uuid,
    pub user_id: Option<Uuid>,
    pub credits_used: i32,
    pub operation: String,
    pub source_locale: Option<String>,
    pub target_locale: Option<String>,
    pub text_length: Option<i32>,
    pub model_used: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable)]
#[diesel(table_name = ai_credit_pricing)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct AICreditPricing {
    pub id: Uuid,
    pub min_credits: i32,
    pub max_credits: Option<i32>,
    pub price_per_credit: f64,
    pub currency: String,
    pub is_active: Option<bool>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize)]
pub struct AICreditBalance {
    pub total: i32,
    pub used: i32,
    pub remaining: i32,
    pub reset_date: Option<DateTime<Utc>>,
    pub subscription_tier: String,
    pub monthly_allowance: i32,
}

#[derive(Debug, Serialize)]
pub struct AICreditPurchase {
    pub credits_purchased: i32,
    pub total_credits: i32,
    pub transaction_id: String,
    pub transaction_date: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct AICreditUsageResponse {
    pub id: String,
    pub credits_used: i32,
    pub feature: String,
    pub operation: Option<String>,
    pub source_locale: Option<String>,
    pub target_locale: Option<String>,
    pub text_length: Option<i32>,
    pub user_id: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize)]
pub struct AICreditPricingResponse {
    pub price_per_credit: f64,
    pub currency: String,
    pub minimum_purchase: i32,
    pub tiers: Vec<AICreditPricingTier>,
}

#[derive(Debug, Serialize)]
pub struct AICreditPricingTier {
    pub min_credits: i32,
    pub max_credits: Option<i32>,
    pub price_per_credit: f64,
}

#[derive(Debug, Serialize)]
pub struct AITranslationResponse {
    pub translated_text: String,
    pub model_used: String,
    pub credits_used: i32,
    pub credits_remaining: i32,
}
