use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::api_call_logs;

#[derive(Debug, Queryable, Serialize, Deserialize, Identifiable)]
#[diesel(table_name = api_call_logs)]
pub struct ApiCallLog {
    pub id: Uuid,
    pub api_key_id: Uuid,
    pub organization_id: Uuid,
    pub endpoint: String,
    pub method: String,
    pub status_code: i32,
    pub response_time_ms: i32,
    pub request_size_bytes: Option<i32>,
    pub response_size_bytes: Option<i32>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Insertable)]
#[diesel(table_name = api_call_logs)]
pub struct NewApiCallLog {
    pub api_key_id: Uuid,
    pub organization_id: Uuid,
    pub endpoint: String,
    pub method: String,
    pub status_code: i32,
    pub response_time_ms: i32,
    pub request_size_bytes: Option<i32>,
    pub response_size_bytes: Option<i32>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiCallLogSummary {
    pub date: String,
    pub count: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiCallLogEndpointSummary {
    pub endpoint: String,
    pub count: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiCallLogStatusSummary {
    pub status_code: i32,
    pub count: i64,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct ApiCallLogResponse {
    pub id: String,
    pub api_key_id: String,
    pub organization_id: String,
    pub endpoint: String,
    pub method: String,
    pub status_code: i32,
    pub response_time_ms: i32,
    pub request_size_bytes: Option<i32>,
    pub response_size_bytes: Option<i32>,
    pub ip_address: Option<String>,
    pub user_agent: Option<String>,
    pub created_at: DateTime<Utc>,
}
