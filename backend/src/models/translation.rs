use chrono::{DateTime, Utc};
use diesel::prelude::*;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::schema::{translation_keys, translations, translation_history};

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = translation_keys)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct TranslationKey {
    pub id: Uuid,
    pub resource_id: Option<Uuid>,
    pub key_name: String,
    pub description: Option<String>,
    pub context: Option<String>,
    pub is_plural: Option<bool>,
    pub max_length: Option<i32>,
    pub screenshot_url: Option<String>,
    pub created_by: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
    pub deleted_at: Option<DateTime<Utc>>,
    pub project_id: Option<Uuid>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = translation_keys)]
pub struct NewTranslationKey {
    pub resource_id: Option<Uuid>,
    pub key_name: String,
    pub description: Option<String>,
    pub context: Option<String>,
    pub is_plural: Option<bool>,
    pub max_length: Option<i32>,
    pub screenshot_url: Option<String>,
    pub created_by: Option<Uuid>,
    pub project_id: Option<Uuid>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = translations)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct Translation {
    pub id: Uuid,
    pub key_id: Uuid,
    pub locale_id: Uuid,
    pub content: String,
    pub is_fuzzy: Option<bool>,
    pub is_reviewed: Option<bool>,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub created_by: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = translations)]
pub struct NewTranslation {
    pub key_id: Uuid,
    pub locale_id: Uuid,
    pub content: String,
    pub is_fuzzy: Option<bool>,
    pub is_reviewed: Option<bool>,
    pub reviewed_by: Option<Uuid>,
    pub created_by: Option<Uuid>,
}

#[derive(Debug, Deserialize, AsChangeset)]
#[diesel(table_name = translations)]
pub struct UpdateTranslation {
    pub content: Option<String>,
    pub is_fuzzy: Option<bool>,
    pub is_reviewed: Option<bool>,
    pub reviewed_by: Option<Uuid>,
    pub reviewed_at: Option<DateTime<Utc>>,
    pub updated_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize, Queryable, Identifiable, Selectable)]
#[diesel(table_name = translation_history)]
#[diesel(check_for_backend(diesel::pg::Pg))]
pub struct TranslationHistory {
    pub id: Uuid,
    pub translation_id: Uuid,
    pub content: String,
    pub action: String,
    pub performed_by: Option<Uuid>,
    pub created_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Deserialize, Insertable)]
#[diesel(table_name = translation_history)]
pub struct NewTranslationHistory {
    pub translation_id: Uuid,
    pub content: String,
    pub action: String,
    pub performed_by: Option<Uuid>,
}
