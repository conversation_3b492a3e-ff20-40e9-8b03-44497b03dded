use diesel::pg::PgConnection;
use diesel::r2d2::{self, ConnectionManager};
use diesel::RunQueryDsl;
use dotenv::dotenv;
use rocket::request::{self, FromRequest, Outcome};
use rocket::Request;
use std::env;
use std::ops::{Deref, DerefMut};

// Type alias for the connection pool
pub type Pool = r2d2::Pool<ConnectionManager<PgConnection>>;

// Type alias for a pooled connection
pub type PooledConn = r2d2::PooledConnection<ConnectionManager<PgConnection>>;

// Initialize the connection pool
pub fn init_pool() -> Pool {
    dotenv().ok();
    let database_url = env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let manager = ConnectionManager::<PgConnection>::new(database_url);
    r2d2::Pool::builder()
        .max_size(1) // Use only one connection to avoid prepared statement conflicts
        .build(manager)
        .expect("Failed to create pool")
}

// Connection request guard type: a wrapper around an r2d2 pooled connection
pub struct DbConn(pub r2d2::PooledConnection<ConnectionManager<PgConnection>>);

impl Clone for DbConn {
    fn clone(&self) -> Self {
        // This is a bit of a hack, but it's the simplest way to clone a connection
        // We're essentially getting a new connection from the pool
        let database_url = env::var("DATABASE_URL").expect("DATABASE_URL must be set");
        let manager = ConnectionManager::<PgConnection>::new(database_url);
        let pool = r2d2::Pool::builder()
            .max_size(1)
            .build(manager)
            .expect("Failed to create pool");
        DbConn(pool.get().expect("Failed to get connection from pool"))
    }
}

// For the convenience of using an &DbConn as an &PooledConn.
impl Deref for DbConn {
    type Target = PooledConn;

    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

// For the convenience of using a &mut DbConn as a &mut PooledConn.
impl DerefMut for DbConn {
    fn deref_mut(&mut self) -> &mut Self::Target {
        &mut self.0
    }
}

// Rocket request guard implementation
#[rocket::async_trait]
impl<'r> FromRequest<'r> for DbConn {
    type Error = ();

    async fn from_request(request: &'r Request<'_>) -> request::Outcome<Self, Self::Error> {
        let pool = request.rocket().state::<Pool>().unwrap();
        match pool.get() {
            Ok(conn) => Outcome::Success(DbConn(conn)),
            Err(_) => Outcome::Error((rocket::http::Status::ServiceUnavailable, ())),
        }
    }
}
