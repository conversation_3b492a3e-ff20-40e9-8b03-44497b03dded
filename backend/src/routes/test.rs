use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use serde_json::json;

use crate::email::cloud_notification::CloudNotificationService;
use crate::email::templates::{render_email_verification, EmailVerificationContext};
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct TestEmailRequest {
    pub to_email: String,
    pub to_name: Option<String>,
    pub subject: String,
    pub html_content: String,
    pub text_content: String,
}

#[derive(Debug, Serialize)]
pub struct TestEmailResponse {
    pub message: String,
    pub message_id: Option<String>,
}

#[post("/email", format = "json", data = "<request>")]
pub async fn test_email(
    request: Json<TestEmailRequest>,
    email_service: &State<CloudNotificationService>
) -> J<PERSON><ApiResponse<TestEmailResponse>> {
    // Send email
    let result = email_service.send_email(
        &request.to_email,
        request.to_name.as_deref(),
        &request.subject,
        &request.html_content,
        &request.text_content,
        None,
    ).await;

    match result {
        Ok(message_id) => {
            ApiResponse::success_with_message(
                TestEmailResponse {
                    message: "Email sent successfully".to_string(),
                    message_id: Some(message_id),
                },
                "Email was sent successfully"
            )
        },
        Err(e) => {
            eprintln!("Failed to send email: {}", e);

            // Check if this is an IP whitelist error
            if e.to_string().contains("IP Whitelist Error") {
                return ApiResponse::error_with_details(
                    Status::ServiceUnavailable,
                    ErrorCode::Custom("EMAIL_SERVICE_ERROR".to_string()),
                    "IP Whitelist Error: Please whitelist your IP address in the Brevo dashboard",
                    json!({
                        "service": "Brevo",
                        "error_type": "IP_WHITELIST",
                        "original_error": e.to_string()
                    })
                );
            }

            ApiResponse::error_with_details(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to send email",
                json!({
                    "service": "Brevo",
                    "original_error": e.to_string()
                })
            )
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct TestVerificationEmailRequest {
    pub to_email: String,
    pub to_name: String,
    pub verification_url: String,
}

#[post("/verification-email", format = "json", data = "<request>")]
pub async fn test_verification_email(
    request: Json<TestVerificationEmailRequest>,
    email_service: &State<CloudNotificationService>
) -> Json<ApiResponse<TestEmailResponse>> {
    // Get app name from environment or use default
    let app_name = std::env::var("APP_NAME").unwrap_or_else(|_| "ADC Multi-Languages".to_string());

    // Create verification context
    let context = EmailVerificationContext {
        username: request.to_name.clone(),
        verification_link: request.verification_url.clone(),
        app_name: app_name.clone(),
    };

    // Render email content
    let (html_content, text_content) = match render_email_verification(&context) {
        Ok(content) => content,
        Err(e) => {
            eprintln!("Failed to render email template: {}", e);
            return ApiResponse::error_with_details(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to render email template",
                json!({
                    "template": "email_verification",
                    "original_error": e.to_string()
                })
            );
        }
    };

    // Send email
    let subject = format!("Verify your email address for {}", app_name);
    let result = email_service.send_email(
        &request.to_email,
        Some(&request.to_name),
        &subject,
        &html_content,
        &text_content,
        None,
    ).await;

    match result {
        Ok(message_id) => {
            ApiResponse::success_with_message(
                TestEmailResponse {
                    message: "Verification email sent successfully".to_string(),
                    message_id: Some(message_id),
                },
                "Verification email was sent successfully"
            )
        },
        Err(e) => {
            eprintln!("Failed to send verification email: {}", e);

            // Check if this is an IP whitelist error
            if e.to_string().contains("IP Whitelist Error") {
                return ApiResponse::error_with_details(
                    Status::ServiceUnavailable,
                    ErrorCode::Custom("EMAIL_SERVICE_ERROR".to_string()),
                    "IP Whitelist Error: Please whitelist your IP address in the Brevo dashboard",
                    json!({
                        "service": "Brevo",
                        "error_type": "IP_WHITELIST",
                        "original_error": e.to_string()
                    })
                );
            }

            ApiResponse::error_with_details(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to send verification email",
                json!({
                    "service": "Brevo",
                    "original_error": e.to_string()
                })
            )
        }
    }
}
