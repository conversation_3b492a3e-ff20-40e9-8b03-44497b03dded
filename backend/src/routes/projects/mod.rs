pub mod slug;

use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::Utc;
use serde_json::json;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::project::{Project, NewProject, UpdateProject, ProjectLocale, NewProjectLocale, Resource, NewResource};
use crate::repositories::project_repository::ProjectRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::locale_repository::LocaleRepository;
use crate::utils::response::{ApiResponse, ErrorCode, field_error};

pub use self::slug::*;

#[derive(Debug, Deserialize)]
pub struct CreateProjectRequest {
    pub organization_id: String,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    pub default_locale: Option<String>,
    pub is_public: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct UpdateProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub default_locale: Option<String>,
    pub is_public: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct AddLocaleRequest {
    pub locale_id: Option<String>,
    pub locale_code: Option<String>,
    pub is_source: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct ProjectResponse {
    pub id: Uuid,
    pub organization_id: Uuid,
    pub name: String,
    pub slug: String,
    pub description: Option<String>,
    // These fields are not in the model anymore but kept in the response for backward compatibility
    pub default_locale: Option<String>,
    pub is_public: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct ProjectLocaleResponse {
    pub id: Uuid,
    pub project_id: Uuid,
    pub locale_id: Uuid,
    pub is_source: Option<bool>,
    pub locale_code: Option<String>,
    pub locale_name: Option<String>,
    pub locale_native_name: Option<String>,
    pub text_direction: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct ResourceResponse {
    pub id: Uuid,
    pub project_id: Uuid,
    pub name: String,
    pub type_: String,
    pub path: Option<String>,
    pub description: Option<String>,
}

impl From<Project> for ProjectResponse {
    fn from(project: Project) -> Self {
        Self {
            id: project.id,
            organization_id: project.organization_id,
            name: project.name,
            slug: project.slug,
            description: project.description,
            // Default values for backward compatibility
            default_locale: None,
            is_public: None,
        }
    }
}

impl From<ProjectLocale> for ProjectLocaleResponse {
    fn from(locale: ProjectLocale) -> Self {
        Self {
            id: locale.id,
            project_id: locale.project_id,
            locale_id: locale.locale_id,
            is_source: locale.is_source,
            locale_code: None,
            locale_name: None,
            locale_native_name: None,
            text_direction: None,
        }
    }
}

impl From<Resource> for ResourceResponse {
    fn from(resource: Resource) -> Self {
        Self {
            id: resource.id,
            project_id: resource.project_id,
            name: resource.name,
            type_: resource.type_,
            path: resource.path,
            description: resource.description,
        }
    }
}

#[get("/?<organization_id>")]
pub fn list_projects(
    auth: AuthUser,
    organization_id: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<ProjectResponse>>> {
    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            );
        }
    };

    let org_ids: Vec<Uuid> = organizations.iter().map(|org| org.id).collect();

    let projects = if let Some(org_id_str) = organization_id {
        // Parse organization_id
        let org_id = match Uuid::parse_str(&org_id_str) {
            Ok(id) => id,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::ValidationError,
                    "Invalid organization_id format"
                );
            }
        };

        // Check if user has access to the organization
        if !org_ids.contains(&org_id) {
            return ApiResponse::error(
                Status::Forbidden,
                ErrorCode::Forbidden,
                "You don't have access to this organization"
            );
        }

        // Get projects for the organization
        match ProjectRepository::list_by_organization(&mut conn, org_id) {
            Ok(projects) => projects,
            Err(e) => {
                eprintln!("Error listing projects: {}", e);
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to retrieve projects"
                );
            }
        }
    } else {
        // If no organization_id is provided, return projects from all organizations the user has access to
        let mut all_projects = Vec::new();

        for org_id in org_ids {
            match ProjectRepository::list_by_organization(&mut conn, org_id) {
                Ok(org_projects) => {
                    all_projects.extend(org_projects);
                },
                Err(e) => {
                    eprintln!("Error listing projects for organization {}: {}", org_id, e);
                    // Continue with other organizations even if one fails
                }
            }
        }

        all_projects
    };

    let response = projects.into_iter()
        .map(ProjectResponse::from)
        .collect();

    ApiResponse::success(response)
}

#[post("/", format = "json", data = "<request>")]
pub fn create_project(
    auth: AuthUser,
    request: Json<CreateProjectRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<ProjectResponse>> {
    // Validate organization_id
    let org_id = match Uuid::parse_str(&request.organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Invalid organization_id format"
            );
        }
    };

    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == org_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this organization"
        );
    }

    // Validate request fields
    let mut field_errors = Vec::new();

    if request.name.trim().is_empty() {
        field_errors.push(field_error("name", "required", "Project name is required"));
    }

    if request.slug.trim().is_empty() {
        field_errors.push(field_error("slug", "required", "Project slug is required"));
    } else if !request.slug.chars().all(|c| c.is_ascii_alphanumeric() || c == '-' || c == '_') {
        field_errors.push(field_error(
            "slug",
            "invalid_format",
            "Slug can only contain alphanumeric characters, hyphens, and underscores"
        ));
    }

    // Check if a project with the same slug already exists in the organization
    if let Ok(_) = ProjectRepository::find_by_slug(&mut conn, org_id, &request.slug) {
        field_errors.push(field_error(
            "slug",
            "already_exists",
            &format!("Project with slug '{}' already exists in this organization", request.slug)
        ));
    }

    // If default_locale is provided, validate it exists
    if let Some(locale_code) = &request.default_locale {
        if locale_code.trim().is_empty() {
            field_errors.push(field_error(
                "default_locale",
                "invalid",
                "Default locale cannot be empty if provided"
            ));
        } else if LocaleRepository::find_by_code(&mut conn, locale_code).is_err() {
            field_errors.push(field_error(
                "default_locale",
                "not_found",
                &format!("Locale with code '{}' not found", locale_code)
            ));
        }
    }

    // Return validation errors if any
    if !field_errors.is_empty() {
        return ApiResponse::validation_error(field_errors);
    }

    // Create the project
    let new_project = NewProject {
        organization_id: org_id,
        name: request.name.clone(),
        slug: request.slug.clone(),
        description: request.description.clone(),
        default_locale: request.default_locale.clone(),
        is_public: request.is_public,
        created_by: Some(auth.user_id),
    };

    let project = match ProjectRepository::create(&mut conn, &new_project) {
        Ok(project) => project,
        Err(e) => {
            eprintln!("Error creating project: {}", e);
            // Check if the error is due to a unique constraint violation
            if e.to_string().contains("duplicate key") || e.to_string().contains("unique constraint") {
                return ApiResponse::error(
                    Status::Conflict,
                    ErrorCode::Conflict,
                    "Project with this name or slug already exists"
                );
            } else {
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to create project"
                );
            }
        }
    };

    // If default_locale is provided, add it as a source locale
    if let Some(locale_code) = &request.default_locale {
        if let Ok(locale) = LocaleRepository::find_by_code(&mut conn, locale_code) {
            let new_project_locale = NewProjectLocale {
                project_id: project.id,
                locale_id: locale.id,
                is_source: Some(true),
                is_active: Some(true),
            };

            if let Err(e) = ProjectRepository::add_locale(&mut conn, &new_project_locale) {
                eprintln!("Error adding locale to project: {}", e);
                // We don't fail the whole request if adding the locale fails
                // Just log the error and continue
            }
        }
    }

    // Return success response with 201 Created status
    ApiResponse::success_with_status(project.into(), Status::Created)
}

#[get("/<id>")]
pub fn get_project(
    auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Json<ApiResponse<ProjectResponse>> {
    // Parse project_id
    let project_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Invalid project ID format"
            );
        }
    };

    // Get the project
    let project = match ProjectRepository::find_by_id(&mut conn, project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Project with ID '{}' not found", id)
            );
        }
    };

    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this project"
        );
    }

    ApiResponse::success(project.into())
}

#[put("/<id>", format = "json", data = "<request>")]
pub fn update_project(
    auth: AuthUser,
    id: String,
    request: Json<UpdateProjectRequest>,
    mut conn: DbConn
) -> Result<Json<ProjectResponse>, Status> {
    let project_id = Uuid::parse_str(&id).map_err(|_| Status::BadRequest)?;

    // Get the project
    let project = ProjectRepository::find_by_id(&mut conn, project_id)
        .map_err(|_| Status::NotFound)?;

    // Check if user has access to the organization
    let organizations = OrganizationRepository::list_by_user(&mut conn, auth.user_id)
        .map_err(|_| Status::InternalServerError)?;

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return Err(Status::Forbidden);
    }

    // Update the project
    let update_project = UpdateProject {
        name: request.name.clone(),
        slug: None,
        description: request.description.clone(),
        default_locale: request.default_locale.clone(),
        is_public: request.is_public,
        updated_at: Some(Utc::now()),
    };

    let updated_project = ProjectRepository::update(&mut conn, project_id, &update_project)
        .map_err(|_| Status::InternalServerError)?;

    Ok(Json(updated_project.into()))
}

#[delete("/<id>")]
pub fn delete_project(
    auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Result<Json<ProjectResponse>, Status> {
    let project_id = Uuid::parse_str(&id).map_err(|_| Status::BadRequest)?;

    // Get the project
    let project = ProjectRepository::find_by_id(&mut conn, project_id)
        .map_err(|_| Status::NotFound)?;

    // Check if user has access to the organization
    let organizations = OrganizationRepository::list_by_user(&mut conn, auth.user_id)
        .map_err(|_| Status::InternalServerError)?;

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return Err(Status::Forbidden);
    }

    // Delete the project (soft delete)
    let deleted_project = ProjectRepository::delete(&mut conn, project_id)
        .map_err(|_| Status::InternalServerError)?;

    Ok(Json(deleted_project.into()))
}

#[get("/<id>/locales")]
pub fn list_project_locales(
    auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<ProjectLocaleResponse>>> {
    // Parse project_id
    let project_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Invalid project ID format"
            );
        }
    };

    // Get the project
    let project = match ProjectRepository::find_by_id(&mut conn, project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Project with ID '{}' not found", id)
            );
        }
    };

    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this project"
        );
    }

    // Get project locales
    let project_locales = match ProjectRepository::list_locales(&mut conn, project_id) {
        Ok(locales) => locales,
        Err(e) => {
            eprintln!("Error listing project locales: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve project locales"
            );
        }
    };

    // Create response with locale details
    let mut response_locales = Vec::new();
    for project_locale in project_locales {
        let mut response = ProjectLocaleResponse::from(project_locale);

        // Get locale details
        if let Ok(locale) = LocaleRepository::find_by_id(&mut conn, response.locale_id) {
            response.locale_code = Some(locale.code);
            response.locale_name = Some(locale.name);
            response.locale_native_name = Some(locale.native_name);
            response.text_direction = locale.text_direction.clone();
        }

        response_locales.push(response);
    }

    ApiResponse::success(response_locales)
}

#[post("/<id>/locales", format = "json", data = "<request>")]
pub fn add_project_locale(
    auth: AuthUser,
    id: String,
    request: Json<AddLocaleRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<ProjectLocaleResponse>> {
    // Parse project_id
    let project_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Invalid project ID format"
            );
        }
    };

    // Get locale by ID or code
    let locale = if let Some(locale_id_str) = &request.locale_id {
        // Parse locale_id
        let locale_id = match Uuid::parse_str(locale_id_str) {
            Ok(id) => id,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::ValidationError,
                    "Invalid locale ID format"
                );
            }
        };

        // Find locale by ID
        match LocaleRepository::find_by_id(&mut conn, locale_id) {
            Ok(locale) => locale,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Locale with ID '{}' not found", locale_id_str)
                );
            }
        }
    } else if let Some(locale_code) = &request.locale_code {
        // Find locale by code
        match LocaleRepository::find_by_code(&mut conn, locale_code) {
            Ok(locale) => locale,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Locale with code '{}' not found", locale_code)
                );
            }
        }
    } else {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::ValidationError,
            "Either locale_id or locale_code must be provided"
        );
    };

    // Get the project
    let project = match ProjectRepository::find_by_id(&mut conn, project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Project with ID '{}' not found", id)
            );
        }
    };

    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this project"
        );
    }

    // Locale is already validated and retrieved above

    // Check if locale is already added to the project
    let existing_locales = match ProjectRepository::list_locales(&mut conn, project_id) {
        Ok(locales) => locales,
        Err(e) => {
            eprintln!("Error listing project locales: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve project locales"
            );
        }
    };

    if existing_locales.iter().any(|l| l.locale_id == locale.id) {
        return ApiResponse::error(
            Status::Conflict,
            ErrorCode::Conflict,
            &format!("Locale '{}' is already added to the project", locale.code)
        );
    }

    // Add locale to project
    let new_project_locale = NewProjectLocale {
        project_id,
        locale_id: locale.id,
        is_source: request.is_source,
        is_active: Some(true),
    };

    match ProjectRepository::add_locale(&mut conn, &new_project_locale) {
        Ok(project_locale) => {
            // Create response with locale details
            let mut response = ProjectLocaleResponse::from(project_locale);

            // Add locale details to response
            response.locale_code = Some(locale.code);
            response.locale_name = Some(locale.name);
            response.locale_native_name = Some(locale.native_name);
            response.text_direction = locale.text_direction.clone();

            ApiResponse::success_with_status(response, Status::Created)
        },
        Err(e) => {
            eprintln!("Error adding locale to project: {}", e);
            // Check if the error is due to a unique constraint violation
            if e.to_string().contains("duplicate key") || e.to_string().contains("unique constraint") {
                ApiResponse::error(
                    Status::Conflict,
                    ErrorCode::Conflict,
                    "This locale is already added to the project"
                )
            } else {
                ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to add locale to project"
                )
            }
        }
    }
}

#[derive(Debug, Deserialize)]
pub struct CreateResourceRequest {
    pub name: String,
    pub type_: String,
    pub path: Option<String>,
    pub description: Option<String>,
}

#[post("/<id>/resources", format = "json", data = "<request>")]
pub fn create_resource(
    auth: AuthUser,
    id: String,
    request: Json<CreateResourceRequest>,
    mut conn: DbConn
) -> Result<Json<ResourceResponse>, Status> {
    let project_id = Uuid::parse_str(&id).map_err(|_| Status::BadRequest)?;

    // Get the project
    let project = ProjectRepository::find_by_id(&mut conn, project_id)
        .map_err(|_| Status::NotFound)?;

    // Check if user has access to the organization
    let organizations = OrganizationRepository::list_by_user(&mut conn, auth.user_id)
        .map_err(|_| Status::InternalServerError)?;

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return Err(Status::Forbidden);
    }

    // Create resource
    let new_resource = NewResource {
        project_id,
        name: request.name.clone(),
        type_: request.type_.clone(),
        path: request.path.clone(),
        description: request.description.clone(),
        created_by: Some(auth.user_id),
    };

    let resource = ProjectRepository::create_resource(&mut conn, &new_resource)
        .map_err(|_| Status::InternalServerError)?;

    Ok(Json(resource.into()))
}

#[get("/<id>/resources")]
pub fn list_resources(
    auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Result<Json<Vec<ResourceResponse>>, Status> {
    let project_id = Uuid::parse_str(&id).map_err(|_| Status::BadRequest)?;

    // Get the project
    let project = ProjectRepository::find_by_id(&mut conn, project_id)
        .map_err(|_| Status::NotFound)?;

    // Check if user has access to the organization
    let organizations = OrganizationRepository::list_by_user(&mut conn, auth.user_id)
        .map_err(|_| Status::InternalServerError)?;

    if !organizations.iter().any(|org| org.id == project.organization_id) {
        return Err(Status::Forbidden);
    }

    // Get resources
    let resources = ProjectRepository::list_resources(&mut conn, project_id)
        .map_err(|_| Status::InternalServerError)?;

    let response = resources.into_iter()
        .map(ResourceResponse::from)
        .collect();

    Ok(Json(response))
}
