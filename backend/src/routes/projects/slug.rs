use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::repositories::project_repository::ProjectRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::utils::response::{ApiResponse, ErrorCode};
use crate::routes::projects::ProjectResponse;

#[get("/<slug>?<organization_id>")]
pub fn get_project_by_slug(
    auth: AuthUser,
    slug: String,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<ProjectResponse>> {
    // Parse organization_id
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Invalid organization_id format"
            );
        }
    };

    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == org_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this organization"
        );
    }

    // Get the project by slug
    let project = match ProjectRepository::find_by_slug(&mut conn, org_id, &slug) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Project with slug '{}' not found in this organization", slug)
            );
        }
    };

    ApiResponse::success(project.into())
}
