use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::payment::stripe::StripeService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Serialize, Clone)]
pub struct InvoiceResponse {
    pub id: String,
    pub organization_id: String,
    pub number: String,
    pub status: String,
    pub amount_due: i64,
    pub amount_paid: i64,
    pub currency: String,
    pub description: String,
    pub created: i64,
    pub due_date: i64,
    pub paid_at: Option<i64>,
}

#[derive(Debug, Serialize)]
pub struct InvoiceDetailResponse {
    pub id: String,
    pub organization_id: String,
    pub number: String,
    pub status: String,
    pub amount_due: i64,
    pub amount_paid: i64,
    pub currency: String,
    pub description: String,
    pub created: i64,
    pub due_date: i64,
    pub paid_at: Option<i64>,
    pub subtotal: i64,
    pub tax: i64,
    pub total: i64,
    pub customer_name: String,
    pub customer_email: String,
    pub payment_method: Option<PaymentMethodInfo>,
}

#[derive(Debug, Serialize)]
pub struct PaymentMethodInfo {
    pub brand: String,
    pub last4: String,
}

#[derive(Debug, Serialize)]
pub struct InvoiceDownloadResponse {
    pub download_url: String,
}

#[derive(Debug, Serialize)]
pub struct InvoicePayResponse {
    pub id: String,
    pub number: String,
    pub status: String,
    pub paid_at: String,
}

#[derive(Debug, Deserialize)]
pub struct PayInvoiceRequest {
    pub payment_method_id: String,
}

#[derive(Debug, Serialize)]
pub struct PaginationMeta {
    pub page: i64,
    pub per_page: i64,
    pub total: i64,
    pub total_pages: i64,
}

#[derive(Debug, Serialize)]
pub struct ApiResponseWithPagination<T> {
    pub success: bool,
    pub status: u16,
    pub data: T,
    pub meta: ResponseMetaWithPagination,
}

#[derive(Debug, Serialize)]
pub struct ResponseMetaWithPagination {
    pub timestamp: String,
    pub request_id: String,
    pub version: String,
    pub pagination: PaginationMeta,
}

/// List Invoices API
///
/// Returns a list of invoices for an organization
#[get("/<organization_id>/invoices?<page>&<per_page>&<status>")]
pub fn list_invoices(
    auth: AuthUser,
    organization_id: String,
    page: Option<i64>,
    per_page: Option<i64>,
    status: Option<String>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponseWithPagination<Vec<InvoiceResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return Json(ApiResponseWithPagination {
                success: false,
                status: 400,
                data: Vec::new(),
                meta: ResponseMetaWithPagination {
                    timestamp: Utc::now().to_rfc3339(),
                    request_id: Uuid::new_v4().to_string(),
                    version: "1.0".to_string(),
                    pagination: PaginationMeta {
                        page: 1,
                        per_page: 10,
                        total: 0,
                        total_pages: 0,
                    },
                },
            });
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return Json(ApiResponseWithPagination {
            success: false,
            status: 403,
            data: Vec::new(),
            meta: ResponseMetaWithPagination {
                timestamp: Utc::now().to_rfc3339(),
                request_id: Uuid::new_v4().to_string(),
                version: "1.0".to_string(),
                pagination: PaginationMeta {
                    page: 1,
                    per_page: 10,
                    total: 0,
                    total_pages: 0,
                },
            },
        });
    }

    // In a real implementation, you would fetch invoices from Stripe
    // For now, we'll return mock data
    let page = page.unwrap_or(1);
    let per_page = per_page.unwrap_or(10).min(100);

    // Generate mock invoices
    let mut invoices = Vec::new();

    // Add a paid invoice
    invoices.push(InvoiceResponse {
        id: "inv_123456789".to_string(),
        organization_id: org_id.to_string(),
        number: "INV-001".to_string(),
        status: "paid".to_string(),
        amount_due: 2999,
        amount_paid: 2999,
        currency: "USD".to_string(),
        description: "Pro plan subscription".to_string(),
        created: 1689413400,
        due_date: 1689413400,
        paid_at: Some(1689413400),
    });

    // Add an open invoice
    invoices.push(InvoiceResponse {
        id: "inv_987654321".to_string(),
        organization_id: org_id.to_string(),
        number: "INV-002".to_string(),
        status: "open".to_string(),
        amount_due: 1000,
        amount_paid: 0,
        currency: "USD".to_string(),
        description: "AI Credits purchase".to_string(),
        created: 1689499800,
        due_date: 1690104600,
        paid_at: None,
    });

    // Filter by status if provided
    if let Some(status_filter) = status {
        invoices.retain(|invoice| invoice.status == status_filter);
    }

    // Calculate pagination
    let total = invoices.len() as i64;
    let total_pages = (total as f64 / per_page as f64).ceil() as i64;

    // Apply pagination
    let start = ((page - 1) * per_page) as usize;
    let end = (start + per_page as usize).min(total as usize);
    let paginated_invoices = if start < total as usize {
        invoices[start..end].to_vec()
    } else {
        Vec::new()
    };

    Json(ApiResponseWithPagination {
        success: true,
        status: 200,
        data: paginated_invoices,
        meta: ResponseMetaWithPagination {
            timestamp: Utc::now().to_rfc3339(),
            request_id: Uuid::new_v4().to_string(),
            version: "1.0".to_string(),
            pagination: PaginationMeta {
                page,
                per_page,
                total,
                total_pages,
            },
        },
    })
}

/// Get Invoice API
///
/// Returns details of a specific invoice
#[get("/<organization_id>/invoices/<invoice_id>")]
pub fn get_invoice(
    auth: AuthUser,
    organization_id: String,
    invoice_id: String,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<InvoiceDetailResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // In a real implementation, you would fetch the invoice from Stripe
    // For now, we'll return mock data based on the invoice ID
    let invoice = if invoice_id == "inv_123456789" {
        InvoiceDetailResponse {
            id: "inv_123456789".to_string(),
            organization_id: org_id.to_string(),
            number: "INV-001".to_string(),
            status: "paid".to_string(),
            amount_due: 2999,
            amount_paid: 2999,
            currency: "USD".to_string(),
            description: "Pro plan subscription".to_string(),
            created: 1689413400,
            due_date: 1689413400,
            paid_at: Some(1689413400),
            subtotal: 2999,
            tax: 0,
            total: 2999,
            customer_name: "Acme Inc.".to_string(),
            customer_email: "<EMAIL>".to_string(),
            payment_method: Some(PaymentMethodInfo {
                brand: "visa".to_string(),
                last4: "4242".to_string(),
            }),
        }
    } else if invoice_id == "inv_987654321" {
        InvoiceDetailResponse {
            id: "inv_987654321".to_string(),
            organization_id: org_id.to_string(),
            number: "INV-002".to_string(),
            status: "open".to_string(),
            amount_due: 1000,
            amount_paid: 0,
            currency: "USD".to_string(),
            description: "AI Credits purchase".to_string(),
            created: 1689499800,
            due_date: 1690104600,
            paid_at: None,
            subtotal: 1000,
            tax: 0,
            total: 1000,
            customer_name: "Acme Inc.".to_string(),
            customer_email: "<EMAIL>".to_string(),
            payment_method: None,
        }
    } else {
        return ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            "Invoice not found"
        );
    };

    ApiResponse::success(invoice)
}

/// Download Invoice API
///
/// Returns a download URL for an invoice
#[get("/<organization_id>/invoices/<invoice_id>/download")]
pub fn download_invoice(
    auth: AuthUser,
    organization_id: String,
    invoice_id: String,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<InvoiceDownloadResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // In a real implementation, you would generate a download URL for the invoice
    // For now, we'll return a mock URL
    let download_url = format!("https://example.com/invoices/{}.pdf", invoice_id);

    ApiResponse::success(InvoiceDownloadResponse { download_url })
}

/// Pay Invoice API
///
/// Pays an open invoice
#[post("/<organization_id>/invoices/<invoice_id>/pay", format = "json", data = "<request>")]
pub async fn pay_invoice(
    auth: AuthUser,
    organization_id: String,
    invoice_id: String,
    request: Json<PayInvoiceRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<InvoicePayResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // In a real implementation, you would:
    // 1. Check if the invoice exists and is open
    // 2. Process the payment through Stripe
    // 3. Update the invoice status

    // For now, we'll return mock data
    if invoice_id != "inv_987654321" {
        return ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            "Invoice not found or is not in an open state"
        );
    }

    let now = Utc::now();

    ApiResponse::success(InvoicePayResponse {
        id: invoice_id,
        number: "INV-002".to_string(),
        status: "paid".to_string(),
        paid_at: now.to_rfc3339(),
    })
}
