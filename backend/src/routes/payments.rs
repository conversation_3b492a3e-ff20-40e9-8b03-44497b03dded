use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{Datelike, Timelike, Utc};

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::organization::UpdateOrganization;
use crate::models::payment::{NewPaymentMethod, PaymentMethodResponse};
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::payment_repository::PaymentRepository;
use crate::repositories::user_repository::UserRepository;
use crate::repositories::ai_credit_repository::AICreditRepository;
use crate::payment::stripe::StripeService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct CreateSubscriptionRequest {
    pub organization_id: String,
    pub tier_name: String,
    pub is_yearly: bool,
    pub payment_method_id: String,
}

#[derive(Debug, Serialize)]
pub struct SubscriptionResponse {
    pub id: String,
    pub status: String,
    pub current_period_end: i64,
    pub cancel_at_period_end: bool,
}

#[derive(Debug, Deserialize)]
pub struct AddPaymentMethodRequest {
    pub organization_id: String,
    pub stripe_payment_method_id: String,
    pub set_as_default: bool,
}

#[post("/subscriptions", format = "json", data = "<request>")]
pub async fn create_subscription(
    auth: AuthUser,
    request: Json<CreateSubscriptionRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>,
) -> Json<ApiResponse<SubscriptionResponse>> {
    // Parse organization ID
    let org_id = match Uuid::parse_str(&request.organization_id) {
        Ok(id) => id,
        Err(_) => return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Invalid organization ID format"
        )
    };

    // Check if user has admin access to the organization
    let members = match OrganizationRepository::list_members(&mut conn, org_id) {
        Ok(members) => members,
        Err(e) => {
            eprintln!("Error listing organization members: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organization members"
            );
        }
    };

    let is_admin = members.iter().any(|member|
        member.user_id == auth.user_id && member.role == "admin"
    );

    if !is_admin {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have permission to manage subscriptions for this organization"
        );
    }

    // Get organization
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Get user for customer creation
    let user = match UserRepository::find_by_id(&mut conn, auth.user_id) {
        Ok(user) => user,
        Err(e) => {
            eprintln!("Error finding user: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve user information"
            );
        }
    };

    // Create or retrieve Stripe customer
    let customer_id = match organization.stripe_customer_id {
        Some(id) => id,
        None => {
            // Create new customer
            match stripe_service.create_customer(&user.email, &user.full_name.unwrap_or_else(|| user.username.clone()), org_id).await {
                Ok(customer) => customer.id,
                Err(e) => {
                    eprintln!("Error creating Stripe customer: {}", e);
                    return ApiResponse::error(
                        Status::InternalServerError,
                        ErrorCode::InternalError,
                        &format!("Failed to create Stripe customer: {}", e)
                    );
                }
            }
        }
    };

    // Get price ID for the requested tier
    let price_id = match stripe_service.get_price_id_for_tier(&request.tier_name, request.is_yearly) {
        Ok(id) => id,
        Err(e) => {
            eprintln!("Error getting price ID: {}", e);
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                &format!("Invalid tier or pricing option: {}", e)
            );
        }
    };

    // Create subscription
    let subscription = match stripe_service.create_subscription(&customer_id, &price_id, Some(&request.payment_method_id)).await {
        Ok(sub) => sub,
        Err(e) => {
            eprintln!("Error creating subscription: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to create subscription: {}", e)
            );
        }
    };

    // Determine AI credits monthly allowance based on tier
    let ai_credits_monthly_allowance = match request.tier_name.as_str() {
        "free" => 100,
        "starter" => 1000,
        "professional" => 5000,
        "enterprise" => 10000,
        _ => 0,
    };

    // Calculate reset date (first day of next month)
    let now = Utc::now();
    let next_month = if now.month() == 12 {
        Utc::now().with_year(now.year() + 1).unwrap().with_month(1).unwrap()
    } else {
        Utc::now().with_month(now.month() + 1).unwrap()
    };
    let reset_date = next_month.with_day(1).unwrap().with_hour(0).unwrap().with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();

    // Update organization with subscription info
    let update = UpdateOrganization {
        name: None,
        subscription_tier: Some(request.tier_name.clone()),
        subscription_status: Some("active".to_string()),
        billing_period_start: Some(Utc::now()),
        billing_period_end: subscription.current_period_end.map(|ts| Utc::now() + chrono::Duration::seconds(ts as i64)),
        updated_at: Some(Utc::now()),
        deleted_at: None,
        stripe_customer_id: Some(customer_id.clone()),
        stripe_subscription_id: Some(subscription.id.clone()),
        ai_credits_monthly_allowance: Some(ai_credits_monthly_allowance),
        ai_credits_remaining: Some(ai_credits_monthly_allowance),
        ai_credits_reset_date: Some(reset_date),
    };

    match OrganizationRepository::update(&mut conn, org_id, &update) {
        Ok(_) => (),
        Err(e) => {
            eprintln!("Error updating organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update organization with subscription information"
            );
        }
    };

    // Record the initial credit allocation
    match AICreditRepository::update_organization_credits(
        &mut conn,
        org_id,
        ai_credits_monthly_allowance,
        "subscription_allocation",
        Some(&format!("Initial credit allocation for {} subscription", request.tier_name)),
        Some(auth.user_id),
        None,
        None,
    ) {
        Ok(_) => (),
        Err(e) => {
            eprintln!("Error allocating initial AI credits: {}", e);
            // Continue anyway, as the organization update already succeeded
        }
    };

    // Return subscription details
    ApiResponse::success_with_message(
        SubscriptionResponse {
            id: subscription.id,
            status: subscription.status.to_string(),
            current_period_end: subscription.current_period_end.unwrap_or(0) as i64,
            cancel_at_period_end: subscription.cancel_at_period_end.unwrap_or(false),
        },
        "Subscription created successfully"
    )
}

#[post("/payment-methods", format = "json", data = "<request>")]
pub fn add_payment_method(
    auth: AuthUser,
    request: Json<AddPaymentMethodRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<PaymentMethodResponse>> {
    // Parse organization ID
    let org_id = match Uuid::parse_str(&request.organization_id) {
        Ok(id) => id,
        Err(_) => return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Invalid organization ID format"
        )
    };

    // Check if user has admin access to the organization
    let members = match OrganizationRepository::list_members(&mut conn, org_id) {
        Ok(members) => members,
        Err(e) => {
            eprintln!("Error listing organization members: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organization members"
            );
        }
    };

    let is_admin = members.iter().any(|member|
        member.user_id == auth.user_id && member.role == "admin"
    );

    if !is_admin {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have permission to manage payment methods for this organization"
        );
    }

    // Create new payment method
    // In a real implementation, you would fetch card details from Stripe
    let new_payment_method = NewPaymentMethod {
        organization_id: org_id,
        stripe_payment_method_id: request.stripe_payment_method_id.clone(),
        payment_type: "card".to_string(),
        card_brand: Some("visa".to_string()),
        last_four: Some("4242".to_string()),
        expiry_month: Some(12),
        expiry_year: Some(2025),
        is_default: request.set_as_default,
    };

    match PaymentRepository::create(&mut conn, &new_payment_method) {
        Ok(payment_method) => ApiResponse::success_with_message(
            payment_method.into(),
            "Payment method added successfully"
        ),
        Err(e) => {
            eprintln!("Error creating payment method: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to add payment method"
            )
        }
    }
}

#[get("/payment-methods/<org_id>")]
pub fn list_payment_methods(
    auth: AuthUser,
    org_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<PaymentMethodResponse>>> {
    // Parse organization ID
    let org_id = match Uuid::parse_str(&org_id) {
        Ok(id) => id,
        Err(_) => return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Invalid organization ID format"
        )
    };

    // Check if user has access to the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing user organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve user organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == org_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this organization"
        );
    }

    // List payment methods
    match PaymentRepository::list_by_organization(&mut conn, org_id) {
        Ok(payment_methods) => {
            let response = payment_methods.into_iter()
                .map(PaymentMethodResponse::from)
                .collect();

            ApiResponse::success(response)
        },
        Err(e) => {
            eprintln!("Error listing payment methods: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve payment methods"
            )
        }
    }
}
