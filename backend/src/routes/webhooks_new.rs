use rocket::http::Status;
use rocket::data::{Data, ToByteUnit};
use rocket::post;
use serde::{Deserialize, Serialize};
use serde_json::{Value, json};
use std::env;
use chrono::{Datelike, Timelike, Utc};
use uuid::Uuid;

use crate::db::DbConn;
use crate::models::organization::UpdateOrganization;
use crate::models::ai_credit::NewAICreditTransaction;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::ai_credit_repository::AICreditRepository;

// Webhook event structure
#[derive(Debug, Deserialize, Serialize)]
pub struct WebhookEvent {
    pub id: String,
    #[serde(rename = "type")]
    pub event_type: String,
    pub data: WebhookEventData,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct WebhookEventData {
    pub object: Value,
}

// Helper functions to extract data from webhook events
fn extract_subscription_data(event: &WebhookEvent) -> Result<SubscriptionData, String> {
    let subscription = &event.data.object;

    let subscription_id = subscription.get("id")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing subscription ID".to_string())?
        .to_string();

    let status = subscription.get("status")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing subscription status".to_string())?
        .to_string();

    let current_period_end = subscription.get("current_period_end")
        .and_then(|v| v.as_i64())
        .map(|ts| Utc::now() + chrono::Duration::seconds(ts));

    Ok(SubscriptionData {
        subscription_id,
        status,
        current_period_end,
    })
}

fn extract_invoice_data(event: &WebhookEvent) -> Result<InvoiceData, String> {
    let invoice = &event.data.object;

    let invoice_id = invoice.get("id")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing invoice ID".to_string())?
        .to_string();

    let customer_id = invoice.get("customer")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing customer ID".to_string())?
        .to_string();

    let subscription_id = invoice.get("subscription")
        .and_then(|v| v.as_str())
        .map(|s| s.to_string());

    let status = invoice.get("status")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing invoice status".to_string())?
        .to_string();

    let payment_intent_id = invoice.get("payment_intent")
        .and_then(|v| v.as_str())
        .map(|s| s.to_string());

    let metadata = invoice.get("metadata")
        .and_then(|v| v.as_object())
        .map(|m| m.clone())
        .unwrap_or_default();

    Ok(InvoiceData {
        invoice_id,
        customer_id,
        subscription_id,
        status,
        payment_intent_id,
        metadata,
    })
}

fn extract_payment_intent_data(event: &WebhookEvent) -> Result<PaymentIntentData, String> {
    let payment_intent = &event.data.object;

    let payment_intent_id = payment_intent.get("id")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing payment intent ID".to_string())?
        .to_string();

    let status = payment_intent.get("status")
        .and_then(|v| v.as_str())
        .ok_or_else(|| "Missing payment intent status".to_string())?
        .to_string();

    let metadata = payment_intent.get("metadata")
        .and_then(|v| v.as_object())
        .map(|m| m.clone())
        .unwrap_or_default();

    Ok(PaymentIntentData {
        payment_intent_id,
        status,
        metadata,
    })
}

// Data structures for extracted webhook data
struct SubscriptionData {
    subscription_id: String,
    status: String,
    current_period_end: Option<chrono::DateTime<Utc>>,
}

struct InvoiceData {
    invoice_id: String,
    customer_id: String,
    subscription_id: Option<String>,
    status: String,
    payment_intent_id: Option<String>,
    metadata: serde_json::Map<String, Value>,
}

struct PaymentIntentData {
    payment_intent_id: String,
    status: String,
    metadata: serde_json::Map<String, Value>,
}

#[post("/stripe", data = "<body>")]
pub async fn stripe_webhook(
    body: Data<'_>,
    mut conn: DbConn,
) -> Status {
    // Read the request body
    let body_str = match body.open(5.mebibytes()).into_string().await {
        Ok(string) => string,
        Err(_) => return Status::BadRequest,
    };

    // Parse the event
    let event: WebhookEvent = match serde_json::from_str(&body_str) {
        Ok(event) => event,
        Err(e) => {
            eprintln!("Error parsing webhook event: {}", e);
            return Status::BadRequest;
        }
    };

    // Process the event based on its type
    match event.event_type.as_str() {
        "customer.subscription.updated" => {
            // Handle subscription update
            let subscription_data = match extract_subscription_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting subscription data: {}", e);
                    return Status::BadRequest;
                }
            };

            // Find organization by subscription ID
            let organizations = match OrganizationRepository::find_by_stripe_subscription_id(&mut conn, &subscription_data.subscription_id) {
                Ok(orgs) => orgs,
                Err(e) => {
                    eprintln!("Error finding organization by subscription ID: {}", e);
                    return Status::InternalServerError;
                }
            };

            if let Some(org) = organizations.first() {
                // Map status to our status
                let org_status = match subscription_data.status.as_str() {
                    "active" => "active",
                    "past_due" => "past_due",
                    "unpaid" => "past_due",
                    "canceled" => "cancelled",
                    "incomplete" => "incomplete",
                    "incomplete_expired" => "cancelled",
                    "trialing" => "active",
                    _ => "active",
                };

                // Update organization subscription status
                let update = UpdateOrganization {
                    name: None,
                    slug: None,
                    subscription_tier: None,
                    subscription_status: Some(org_status.to_string()),
                    description: None,
                    logo_url: None,
                    website: None,
                    updated_at: Some(Utc::now()),
                    stripe_customer_id: None,
                    stripe_subscription_id: None,
                    subscription_tier_id: None,
                    subscription_auto_renew: None,
                    billing_period_start: None,
                    billing_period_end: subscription_data.current_period_end,
                };

                if let Err(e) = OrganizationRepository::update(&mut conn, org.id, &update) {
                    eprintln!("Error updating organization: {}", e);
                    return Status::InternalServerError;
                }
            }
        },
        "invoice.payment_succeeded" => {
            // Handle successful invoice payment
            let invoice_data = match extract_invoice_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting invoice data: {}", e);
                    return Status::BadRequest;
                }
            };

            // Check if this is a subscription renewal
            if let Some(subscription_id) = &invoice_data.subscription_id {
                if invoice_data.status == "paid" {
                    // Find the organization with this subscription
                    let orgs = match OrganizationRepository::find_by_stripe_subscription_id(&mut conn, subscription_id) {
                        Ok(orgs) => orgs,
                        Err(_) => {
                            eprintln!("No organization found with subscription ID: {}", subscription_id);
                            return Status::BadRequest;
                        }
                    };

                    if let Some(org) = orgs.first() {
                        // Calculate reset date (first day of next month)
                        let now = Utc::now();
                        let next_month = if now.month() == 12 {
                            now.with_year(now.year() + 1).unwrap().with_month(1).unwrap()
                        } else {
                            now.with_month(now.month() + 1).unwrap()
                        };
                        let reset_date = next_month.with_day(1).unwrap().with_hour(0).unwrap()
                            .with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();

                        // Get the monthly allowance
                        // For now, we'll use a hardcoded value since we've removed the field from the Organization model
                        let monthly_allowance = 1000; // Default monthly allowance

                        // Update the organization with new credits
                        let update = UpdateOrganization {
                            name: None,
                            slug: None,
                            subscription_tier: None,
                            subscription_status: None,
                            description: None,
                            logo_url: None,
                            website: None,
                            updated_at: Some(Utc::now()),
                            stripe_customer_id: None,
                            stripe_subscription_id: None,
                            subscription_tier_id: None,
                            subscription_auto_renew: None,
                            billing_period_start: Some(Utc::now()),
                            billing_period_end: None,
                        };

                        if let Err(e) = OrganizationRepository::update(&mut conn, org.id, &update) {
                            eprintln!("Error updating organization credits: {}", e);
                            return Status::InternalServerError;
                        }

                        // Record the credit reset
                        if let Err(e) = AICreditRepository::update_organization_credits(
                            &mut conn,
                            org.id,
                            monthly_allowance,
                            "subscription_renewal",
                            Some("Monthly credit reset"),
                            None,
                            None,
                            Some(&invoice_data.invoice_id),
                        ) {
                            eprintln!("Error recording credit reset: {}", e);
                            return Status::InternalServerError;
                        }
                    }
                }
            }

            // Check if this is related to AI credits purchase
            let metadata = &invoice_data.metadata;
            if let Some(payment_type) = metadata.get("type").and_then(|v| v.as_str()) {
                if payment_type == "ai_credits_purchase" {
                    // Get the amount of credits purchased
                    let credits_amount = match metadata.get("credits_amount").and_then(|v| v.as_str()) {
                        Some(amount) => amount.parse::<i32>().unwrap_or(0),
                        None => {
                            eprintln!("Invalid credits amount in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Get the organization ID
                    let organization_id = match metadata.get("organization_id").and_then(|v| v.as_str()) {
                        Some(id) => match Uuid::parse_str(id) {
                            Ok(uuid) => uuid,
                            Err(_) => {
                                eprintln!("Invalid organization ID in webhook");
                                return Status::BadRequest;
                            }
                        },
                        None => {
                            eprintln!("Missing organization ID in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Get the payment intent ID
                    let payment_intent_id = match invoice_data.payment_intent_id {
                        Some(id) => id,
                        None => {
                            eprintln!("Invalid payment intent ID in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Update the organization's credits
                    if let Err(e) = AICreditRepository::update_organization_credits(
                        &mut conn,
                        organization_id,
                        credits_amount,
                        "purchase",
                        Some("AI Credits purchase via webhook"),
                        None,
                        Some(&payment_intent_id),
                        Some(&invoice_data.invoice_id),
                    ) {
                        eprintln!("Error updating organization credits: {}", e);
                        return Status::InternalServerError;
                    }
                }
            }
        },
        "payment_intent.succeeded" => {
            // Handle successful payment intent
            let payment_intent_data = match extract_payment_intent_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting payment intent data: {}", e);
                    return Status::BadRequest;
                }
            };

            // Check if this is related to AI credits
            let metadata = &payment_intent_data.metadata;
            if let Some(payment_type) = metadata.get("type").and_then(|v| v.as_str()) {
                if payment_type == "ai_credits_purchase" {
                    println!("Payment intent succeeded for AI credits purchase: {}", payment_intent_data.payment_intent_id);
                    // The actual credit update will happen in the invoice.payment_succeeded event
                }
            }
        },
        _ => {
            // Ignore other events
            println!("Ignoring webhook event: {}", event.event_type);
        }
    }

    Status::Ok
}
