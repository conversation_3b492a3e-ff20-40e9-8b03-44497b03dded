use rocket::serde::json::Json;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use std::collections::HashMap;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::payment::stripe::StripeService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct CreatePaymentIntentRequest {
    pub amount: u64,
    pub currency: String,
    pub description: Option<String>,
    pub metadata: Option<HashMap<String, String>>,
}

#[derive(Debug, Serialize)]
pub struct PaymentIntentResponse {
    pub payment_intent: PaymentIntentData,
}

#[derive(Debug, Serialize)]
pub struct PaymentIntentData {
    pub id: String,
    pub client_secret: String,
    pub amount: u64,
    pub currency: String,
    pub status: String,
    pub created: i64,
}

// Create Payment Intent API
#[post("/<organization_id>/payment-methods/payment-intent", format = "json", data = "<request>")]
pub async fn create_payment_intent(
    auth: AuthUser,
    organization_id: String,
    request: Json<CreatePaymentIntentRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<PaymentIntentResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get organization
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to find organization: {}", e)
            );
        }
    };

    // Get or create Stripe customer ID
    let customer_id = match organization.stripe_customer_id {
        Some(id) => id,
        None => {
            // Get user for customer creation
            let user = match crate::repositories::user_repository::UserRepository::find_by_id(&mut conn, auth.user_id) {
                Ok(user) => user,
                Err(e) => {
                    eprintln!("Error finding user: {}", e);
                    return ApiResponse::error(
                        Status::InternalServerError,
                        ErrorCode::InternalError,
                        &format!("Failed to find user: {}", e)
                    );
                }
            };

            // Create Stripe customer
            match stripe_service.create_customer(&user.email, &user.full_name.unwrap_or_else(|| user.username.clone()), org_id).await {
                Ok(customer) => {
                    // Update organization with customer ID
                    let update = crate::models::organization::UpdateOrganization {
                        stripe_customer_id: Some(customer.id.clone()),
                        name: None,
                        subscription_tier: None,
                        subscription_status: None,
                        billing_period_start: None,
                        billing_period_end: None,
                        updated_at: None,
                        deleted_at: None,
                        stripe_subscription_id: None,
                        ai_credits_monthly_allowance: None,
                        ai_credits_remaining: None,
                        ai_credits_reset_date: None,
                    };

                    match OrganizationRepository::update(&mut conn, org_id, &update) {
                        Ok(_) => customer.id,
                        Err(e) => {
                            eprintln!("Error updating organization with Stripe customer ID: {}", e);
                            return ApiResponse::error(
                                Status::InternalServerError,
                                ErrorCode::InternalError,
                                &format!("Failed to update organization: {}", e)
                            );
                        }
                    }
                },
                Err(e) => {
                    eprintln!("Error creating Stripe customer: {}", e);
                    return ApiResponse::error(
                        Status::InternalServerError,
                        ErrorCode::InternalError,
                        &format!("Failed to create Stripe customer: {}", e)
                    );
                }
            }
        }
    };

    // Get default payment method if available
    let payment_method_id = match crate::repositories::payment_repository::PaymentRepository::get_default(&mut conn, org_id) {
        Ok(payment_method) => Some(payment_method.stripe_payment_method_id),
        Err(_) => None,
    };

    // Create payment intent
    match stripe_service.create_payment_intent(
        request.amount,
        &request.currency,
        &customer_id,
        payment_method_id.as_deref(),
        request.description.as_deref(),
        request.metadata.clone()
    ).await {
        Ok(payment_intent) => {
            // Store the payment intent in the database for future reference
            // In a real implementation, you would store this in a payment_intents table
            // For now, we'll just log it
            eprintln!("Created payment intent: {} for organization: {}", payment_intent.id, org_id);

            // If this is for AI credits, we could pre-allocate them here
            if let Some(metadata) = &request.metadata {
                if let Some(purpose) = metadata.get("purpose") {
                    if purpose == "ai_credits" {
                        if let Some(amount_str) = metadata.get("credits_amount") {
                            if let Ok(credits_amount) = amount_str.parse::<i32>() {
                                // Log the pre-allocation
                                eprintln!("Pre-allocating {} AI credits for organization: {}", credits_amount, org_id);

                                // In a real implementation, you would create a pending transaction
                                // that would be confirmed when the payment is successful
                            }
                        }
                    }
                }
            }

            let response = PaymentIntentResponse {
                payment_intent: PaymentIntentData {
                    id: payment_intent.id.clone(),
                    client_secret: payment_intent.client_secret,
                    amount: payment_intent.amount,
                    currency: payment_intent.currency,
                    status: format!("{:?}", payment_intent.status).to_lowercase(),
                    created: chrono::Utc::now().timestamp(),
                },
            };

            ApiResponse::success(response)
        },
        Err(e) => {
            eprintln!("Error creating payment intent: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to create payment intent: {}", e)
            )
        }
    }
}
