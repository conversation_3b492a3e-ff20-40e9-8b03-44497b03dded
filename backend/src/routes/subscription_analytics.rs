use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, Datelike};
use diesel::prelude::*;
use diesel::sql_query;
use diesel::deserialize::QueryableByName;
use diesel::sql_types::{Text, Int4};
use diesel::pg::Pg;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Serialize)]
pub struct SubscriptionAnalyticsResponse {
    pub total_spend: i32,
    pub current_plan: PlanInfo,
    pub billing_history: Vec<BillingHistoryItem>,
    pub usage_trends: UsageTrends,
}

#[derive(Debug, Serialize)]
pub struct PlanInfo {
    pub id: String,
    pub name: String,
    pub monthly_price: i32,
}

#[derive(Debug, Serialize)]
pub struct BillingHistoryItem {
    pub date: String,
    pub amount: i32,
    pub description: String,
}

#[derive(Debug, Serialize)]
pub struct UsageTrends {
    pub projects_created: Vec<UsageDataPoint>,
    pub translations_created: Vec<UsageDataPoint>,
    pub ai_credits_used: Vec<UsageDataPoint>,
}

#[derive(Debug, Serialize)]
pub struct UsageDataPoint {
    pub date: String,
    pub count: i32,
}

// Get Subscription Analytics API
#[get("/<organization_id>/subscription/analytics?<period>")]
pub fn get_subscription_analytics(
    auth: AuthUser,
    organization_id: String,
    period: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<SubscriptionAnalyticsResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Validate period
    let period = period.unwrap_or_else(|| "monthly".to_string());
    if !["monthly", "quarterly", "yearly"].contains(&period.as_str()) {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Invalid period. Valid options are: monthly, quarterly, yearly"
        );
    }

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to find organization: {}", e)
            );
        }
    };

    // Get subscription tier details
    let subscription_tier = organization.subscription_tier.clone();

    // Get subscription tier price
    let tier_price = match subscription_tier.as_str() {
        "free" => 0,
        "starter" => 2900,
        "pro" => 9900,
        "enterprise" => 29900,
        _ => 0,
    };

    // Determine time period format based on the requested period
    let period_format = match period.as_str() {
        "quarterly" => "YYYY-Q",
        "yearly" => "YYYY",
        _ => "YYYY-MM", // default to monthly
    };

    // Calculate total spend from transactions
    // In a real implementation, you would query the payment history
    // For now, we'll estimate based on the subscription duration
    let subscription_start_date = organization.created_at.unwrap_or_else(Utc::now);
    let now = Utc::now();
    let months_subscribed = ((now.year() - subscription_start_date.year()) * 12 +
                             (now.month() as i32 - subscription_start_date.month() as i32)) as i32;
    let total_spend = tier_price * months_subscribed.max(1);

    // Get billing history
    // In a real implementation, you would query the payment history
    // For now, we'll generate it based on the subscription duration
    let mut billing_history = Vec::new();
    let mut current_date = now;
    for _ in 0..months_subscribed.min(12) { // Limit to last 12 months
        billing_history.push(BillingHistoryItem {
            date: format!("{}-{:02}", current_date.year(), current_date.month()),
            amount: tier_price,
            description: format!("{} Plan (Monthly)", subscription_tier.to_string().to_uppercase()),
        });

        // Move to previous month
        if current_date.month() == 1 {
            current_date = current_date.with_year(current_date.year() - 1).unwrap()
                                      .with_month(12).unwrap();
        } else {
            current_date = current_date.with_month(current_date.month() - 1).unwrap();
        }
    }

    // Get projects created trend
    let projects_created_query = format!("
        SELECT
            TO_CHAR(created_at, '{}') as date,
            COUNT(*) as count
        FROM projects
        WHERE organization_id = '{}'
        GROUP BY date
        ORDER BY date DESC
        LIMIT 12
    ", period_format, org_id);

    #[derive(QueryableByName)]
    #[diesel(check_for_backend(diesel::pg::Pg))]
    struct UsageDataResult {
        #[diesel(sql_type = Text)]
        date: String,
        #[diesel(sql_type = Int4)]
        count: i32,
    }

    let projects_created: Vec<UsageDataPoint> = match diesel::sql_query(projects_created_query)
        .load::<UsageDataResult>(&mut *conn) {
        Ok(results) => results.into_iter()
            .map(|r| UsageDataPoint { date: r.date, count: r.count })
            .collect(),
        Err(e) => {
            eprintln!("Error getting projects created trend: {}", e);
            Vec::new() // Continue with empty data rather than failing
        }
    };

    // Get translations created trend
    let translations_created_query = format!("
        SELECT
            TO_CHAR(created_at, '{}') as date,
            COUNT(*) as count
        FROM translations
        WHERE id IN (
            SELECT translation_id
            FROM translation_keys
            WHERE resource_id IN (
                SELECT id
                FROM resources
                WHERE project_id IN (
                    SELECT id
                    FROM projects
                    WHERE organization_id = '{}'
                )
            )
        )
        GROUP BY date
        ORDER BY date DESC
        LIMIT 12
    ", period_format, org_id);

    let translations_created: Vec<UsageDataPoint> = match diesel::sql_query(translations_created_query)
        .load::<UsageDataResult>(&mut *conn) {
        Ok(results) => results.into_iter()
            .map(|r| UsageDataPoint { date: r.date, count: r.count })
            .collect(),
        Err(e) => {
            eprintln!("Error getting translations created trend: {}", e);
            Vec::new() // Continue with empty data rather than failing
        }
    };

    // Get AI credits used trend
    let ai_credits_used_query = format!("
        SELECT
            TO_CHAR(created_at, '{}') as date,
            SUM(credits_used) as count
        FROM ai_credit_usage
        WHERE organization_id = '{}'
        GROUP BY date
        ORDER BY date DESC
        LIMIT 12
    ", period_format, org_id);

    let ai_credits_used: Vec<UsageDataPoint> = match diesel::sql_query(ai_credits_used_query)
        .load::<UsageDataResult>(&mut *conn) {
        Ok(results) => results.into_iter()
            .map(|r| UsageDataPoint { date: r.date, count: r.count })
            .collect(),
        Err(e) => {
            eprintln!("Error getting AI credits used trend: {}", e);
            Vec::new() // Continue with empty data rather than failing
        }
    };

    let response = SubscriptionAnalyticsResponse {
        total_spend,
        current_plan: PlanInfo {
            id: format!("plan_{}", subscription_tier),
            name: subscription_tier.to_string().to_uppercase(),
            monthly_price: tier_price,
        },
        billing_history,
        usage_trends: UsageTrends {
            projects_created,
            translations_created,
            ai_credits_used,
        },
    };

    ApiResponse::success(response)
}
