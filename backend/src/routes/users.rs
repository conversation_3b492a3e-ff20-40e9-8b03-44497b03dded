use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use serde::Deserialize;
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::user::{UserProfile, UpdateUser};
use crate::repositories::user_repository::UserRepository;
use crate::utils::response::ApiResponse;

#[derive(Debug, Deserialize)]
pub struct UpdateProfileRequest {
    pub full_name: Option<String>,
    pub preferred_language: Option<String>,
}

#[get("/me")]
pub fn get_profile(auth: AuthUser, mut conn: DbConn) -> Json<ApiResponse<UserProfile>> {
    let result = UserRepository::find_by_id(&mut conn, auth.user_id);

    match result {
        Ok(user) => ApiResponse::success(user.into()),
        Err(_) => ApiResponse::error(
            Status::NotFound,
            crate::utils::response::ErrorCode::NotFound,
            "User profile not found"
        )
    }
}

#[put("/me", format = "json", data = "<request>")]
pub fn update_profile(
    auth: AuthUser,
    request: Json<UpdateProfileRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<UserProfile>> {
    let update = UpdateUser {
        username: None,
        full_name: request.full_name.clone(),
        profile_image_url: None,
        preferred_language: request.preferred_language.clone(),
        is_active: None,
        email_verified: None,
        updated_at: Some(chrono::Utc::now()),
        password_hash: None,
    };

    match UserRepository::update(&mut conn, auth.user_id, &update) {
        Ok(user) => ApiResponse::success_with_message(
            user.into(),
            "Profile updated successfully"
        ),
        Err(_) => ApiResponse::error(
            Status::InternalServerError,
            crate::utils::response::ErrorCode::InternalError,
            "Failed to update profile"
        )
    }
}

#[get("/<id>")]
pub fn get_user(id: String, mut conn: DbConn) -> Json<ApiResponse<UserProfile>> {
    // Parse UUID
    let user_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return ApiResponse::error(
            Status::BadRequest,
            crate::utils::response::ErrorCode::BadRequest,
            "Invalid user ID format"
        )
    };

    // Find user
    match UserRepository::find_by_id(&mut conn, user_id) {
        Ok(user) => ApiResponse::success(user.into()),
        Err(_) => ApiResponse::error(
            Status::NotFound,
            crate::utils::response::ErrorCode::NotFound,
            "User not found"
        )
    }
}
