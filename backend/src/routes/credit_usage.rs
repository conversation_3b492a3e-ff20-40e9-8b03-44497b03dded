use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc, NaiveDate};
use diesel::prelude::*;
use diesel::dsl::sql;
use diesel::sql_types::{Jsonb, Int4, Text, Timestamptz};
use diesel::deserialize::QueryableByName;
use diesel::pg::Pg;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::ai_credit::AICreditUsage;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::schema::ai_credit_usage;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Serialize)]
pub struct CreditUsageResponse {
    pub id: String,
    pub organization_id: String,
    pub credits_used: i32,
    pub operation: String,
    pub source_locale: Option<String>,
    pub target_locale: Option<String>,
    pub text_length: Option<i32>,
    pub user_id: Option<String>,
    pub created_at: DateTime<Utc>,
}

impl From<AICreditUsage> for CreditUsageResponse {
    fn from(usage: AICreditUsage) -> Self {
        CreditUsageResponse {
            id: usage.id.to_string(),
            organization_id: usage.organization_id.to_string(),
            credits_used: usage.credits_used,
            operation: usage.operation,
            source_locale: usage.source_locale,
            target_locale: usage.target_locale,
            text_length: usage.text_length,
            user_id: usage.user_id.map(|id| id.to_string()),
            created_at: usage.created_at.unwrap_or_else(Utc::now),
        }
    }
}

#[derive(Debug, Serialize)]
pub struct CreditUsageAnalyticsResponse {
    pub total_credits_used: i32,
    pub time_series: Vec<TimeSeriesData>,
    pub by_operation: serde_json::Value,
    pub by_locale: serde_json::Value,
}

#[derive(Debug, Serialize)]
pub struct TimeSeriesData {
    pub date: String,
    pub total_credits: i32,
    pub by_operation: serde_json::Value,
    pub by_locale: serde_json::Value,
}

// Get Credit Usage History API
#[get("/<organization_id>/credit-usage?<page>&<per_page>&<start_date>&<end_date>&<operation>")]
pub fn get_credit_usage_history(
    auth: AuthUser,
    organization_id: String,
    page: Option<i64>,
    per_page: Option<i64>,
    start_date: Option<String>,
    end_date: Option<String>,
    operation: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<CreditUsageResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Set up pagination
    let page = page.unwrap_or(1);
    let per_page = per_page.unwrap_or(10);
    let offset = (page - 1) * per_page;

    // Build query
    let mut query = ai_credit_usage::table
        .filter(ai_credit_usage::organization_id.eq(org_id))
        .into_boxed();

    // Apply filters
    if let Some(start) = start_date {
        if let Ok(date) = NaiveDate::parse_from_str(&start, "%Y-%m-%d") {
            let start_datetime = date.and_hms_opt(0, 0, 0).unwrap();
            query = query.filter(ai_credit_usage::created_at.ge(start_datetime));
        }
    }

    if let Some(end) = end_date {
        if let Ok(date) = NaiveDate::parse_from_str(&end, "%Y-%m-%d") {
            let end_datetime = date.and_hms_opt(23, 59, 59).unwrap();
            query = query.filter(ai_credit_usage::created_at.le(end_datetime));
        }
    }

    // Apply operation filter if provided
    if let Some(op) = operation {
        query = query.filter(ai_credit_usage::operation.eq(op));
    }

    // Execute query with pagination
    match query
        .order_by(ai_credit_usage::created_at.desc())
        .limit(per_page)
        .offset(offset)
        .select(AICreditUsage::as_select())
        .load(&mut *conn) {
        Ok(usage_records) => {
            let response = usage_records.into_iter()
                .map(CreditUsageResponse::from)
                .collect();

            ApiResponse::success(response)
        },
        Err(e) => {
            eprintln!("Error retrieving credit usage history: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to retrieve credit usage history: {}", e)
            )
        }
    }
}

// Get Credit Usage Analytics API
#[get("/<organization_id>/credit-analytics?<period>&<start_date>&<end_date>&<group_by>")]
pub fn get_credit_usage_analytics(
    auth: AuthUser,
    organization_id: String,
    period: Option<String>,
    start_date: Option<String>,
    end_date: Option<String>,
    group_by: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<CreditUsageAnalyticsResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Determine time period format
    let period_format = match period.as_deref() {
        Some("daily") => "YYYY-MM-DD",
        Some("weekly") => "YYYY-WW",
        Some("monthly") | None => "YYYY-MM",
        _ => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid period. Valid options are: daily, weekly, monthly"
            );
        }
    };

    // Build date range filter
    let mut date_filter = String::new();
    if let Some(start) = &start_date {
        date_filter.push_str(&format!(" AND created_at >= '{}'", start));
    }
    if let Some(end) = &end_date {
        date_filter.push_str(&format!(" AND created_at <= '{}'", end));
    }

    // Get total credits used
    let total_credits_used = match diesel::dsl::sql::<diesel::sql_types::BigInt>(
        &format!("
            SELECT COALESCE(SUM(credits_used), 0)
            FROM ai_credit_usage
            WHERE organization_id = '{}'
            {}
        ", org_id, date_filter)
    ).get_result::<i64>(&mut *conn) {
        Ok(total) => total as i32,
        Err(e) => {
            eprintln!("Error calculating total credits used: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to calculate total credits used: {}", e)
            );
        }
    };

    // Get time series data
    let time_series_query = format!("
        WITH operation_data AS (
            SELECT
                TO_CHAR(created_at, '{}') as date,
                operation,
                SUM(credits_used) as total_credits
            FROM ai_credit_usage
            WHERE organization_id = '{}'
            {}
            GROUP BY date, operation
        ),
        locale_data AS (
            SELECT
                TO_CHAR(created_at, '{}') as date,
                target_locale,
                SUM(credits_used) as total_credits
            FROM ai_credit_usage
            WHERE organization_id = '{}'
            {}
            GROUP BY date, target_locale
        ),
        date_totals AS (
            SELECT
                TO_CHAR(created_at, '{}') as date,
                SUM(credits_used) as total_credits
            FROM ai_credit_usage
            WHERE organization_id = '{}'
            {}
            GROUP BY date
        ),
        operation_agg AS (
            SELECT
                date,
                jsonb_object_agg(
                    COALESCE(operation, 'unknown'),
                    total_credits
                ) FILTER (WHERE operation IS NOT NULL) as by_operation
            FROM operation_data
            GROUP BY date
        ),
        locale_agg AS (
            SELECT
                date,
                jsonb_object_agg(
                    COALESCE(target_locale, 'unknown'),
                    total_credits
                ) FILTER (WHERE target_locale IS NOT NULL) as by_locale
            FROM locale_data
            GROUP BY date
        )
        SELECT
            d.date,
            d.total_credits,
            COALESCE(o.by_operation, '{{}}'::jsonb) as by_operation,
            COALESCE(l.by_locale, '{{}}'::jsonb) as by_locale
        FROM date_totals d
        LEFT JOIN operation_agg o ON d.date = o.date
        LEFT JOIN locale_agg l ON d.date = l.date
        ORDER BY d.date
    ", period_format, org_id, date_filter,
       period_format, org_id, date_filter,
       period_format, org_id, date_filter);

    #[derive(QueryableByName)]
    #[diesel(check_for_backend(diesel::pg::Pg))]
    struct TimeSeriesResult {
        #[diesel(sql_type = Text)]
        date: String,
        #[diesel(sql_type = diesel::sql_types::BigInt)]
        total_credits: i64,
        #[diesel(sql_type = diesel::sql_types::Nullable<Jsonb>)]
        by_operation: Option<serde_json::Value>,
        #[diesel(sql_type = diesel::sql_types::Nullable<Jsonb>)]
        by_locale: Option<serde_json::Value>,
    }

    let time_series_results: Vec<TimeSeriesResult> = match diesel::sql_query(time_series_query)
        .load::<TimeSeriesResult>(&mut *conn) {
        Ok(results) => results,
        Err(e) => {
            eprintln!("Error getting time series data: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to get time series data: {}", e)
            );
        }
    };

    let time_series = time_series_results.into_iter()
        .map(|result| TimeSeriesData {
            date: result.date,
            total_credits: result.total_credits as i32, // Convert i64 to i32
            by_operation: result.by_operation.unwrap_or_else(|| serde_json::json!({})),
            by_locale: result.by_locale.unwrap_or_else(|| serde_json::json!({})),
        })
        .collect();

    // Get aggregated data by operation
    let by_operation_query = format!("
        WITH operation_sums AS (
            SELECT
                operation,
                SUM(credits_used)::bigint as total
            FROM ai_credit_usage
            WHERE organization_id = '{}'
            {}
            GROUP BY operation
        )
        SELECT
            jsonb_object_agg(
                COALESCE(operation, 'unknown'),
                total
            ) as by_operation
        FROM operation_sums
    ", org_id, date_filter);

    #[derive(QueryableByName)]
    #[diesel(check_for_backend(diesel::pg::Pg))]
    struct ByOperationResult {
        #[diesel(sql_type = diesel::sql_types::Nullable<Jsonb>)]
        by_operation: Option<serde_json::Value>,
    }

    let by_operation = match diesel::sql_query(by_operation_query)
        .get_result::<ByOperationResult>(&mut *conn) {
        Ok(result) => result.by_operation.unwrap_or_else(|| serde_json::json!({})),
        Err(e) => {
            eprintln!("Error getting operation aggregation: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to get operation aggregation: {}", e)
            );
        }
    };

    // Get aggregated data by locale
    let by_locale_query = format!("
        WITH locale_sums AS (
            SELECT
                target_locale,
                SUM(credits_used)::bigint as total
            FROM ai_credit_usage
            WHERE organization_id = '{}'
            {}
            GROUP BY target_locale
        )
        SELECT
            jsonb_object_agg(
                COALESCE(target_locale, 'unknown'),
                total
            ) as by_locale
        FROM locale_sums
    ", org_id, date_filter);

    #[derive(QueryableByName)]
    #[diesel(check_for_backend(diesel::pg::Pg))]
    struct ByLocaleResult {
        #[diesel(sql_type = diesel::sql_types::Nullable<Jsonb>)]
        by_locale: Option<serde_json::Value>,
    }

    let by_locale = match diesel::sql_query(by_locale_query)
        .get_result::<ByLocaleResult>(&mut *conn) {
        Ok(result) => result.by_locale.unwrap_or_else(|| serde_json::json!({})),
        Err(e) => {
            eprintln!("Error getting locale aggregation: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to get locale aggregation: {}", e)
            );
        }
    };

    let response = CreditUsageAnalyticsResponse {
        total_credits_used,
        time_series,
        by_operation,
        by_locale,
    };

    ApiResponse::success(response)
}
