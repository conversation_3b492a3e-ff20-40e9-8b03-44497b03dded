use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use serde_json::json;
use chrono::Utc;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::organization::{Organization, NewOrganization, UpdateOrganization, NewOrganizationMember};
use crate::repositories::organization_repository::OrganizationRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

// Helper function to convert subscription tier ID to name
fn get_tier_name_from_id(tier_id: &Option<Uuid>) -> Option<String> {
    match tier_id {
        Some(id) => {
            match id.to_string().as_str() {
                "00000000-0000-0000-0000-000000000001" => Some("starter".to_string()),
                "00000000-0000-0000-0000-000000000002" => Some("pro".to_string()),
                "00000000-0000-0000-0000-000000000003" => Some("enterprise".to_string()),
                _ => Some("free".to_string())
            }
        },
        None => Some("free".to_string())
    }
}

#[derive(Debug, Deserialize)]
pub struct CreateOrganizationRequest {
    pub name: String,
    pub slug: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateOrganizationRequest {
    pub name: Option<String>,
    pub subscription_tier: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct AddMemberRequest {
    pub user_id: String,
    pub role: String,
}

#[derive(Debug, Serialize)]
pub struct OrganizationResponse {
    pub id: Uuid,
    pub name: String,
    pub slug: String,
    pub subscription_tier: String,
    pub subscription_status: Option<String>,
}

impl From<Organization> for OrganizationResponse {
    fn from(org: Organization) -> Self {
        Self {
            id: org.id,
            name: org.name,
            slug: org.slug,
            subscription_tier: get_tier_name_from_id(&org.subscription_tier_id).unwrap_or_else(|| "free".to_string()),
            subscription_status: org.subscription_status,
        }
    }
}

#[get("/")]
pub fn list_organizations(auth: AuthUser, mut conn: DbConn) -> Json<ApiResponse<Vec<OrganizationResponse>>> {
    match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(organizations) => {
            let response = organizations.into_iter()
                .map(OrganizationResponse::from)
                .collect();

            ApiResponse::success(response)
        },
        Err(e) => {
            eprintln!("Error listing organizations: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organizations"
            )
        }
    }
}

#[post("/", format = "json", data = "<request>")]
pub fn create_organization(
    auth: AuthUser,
    request: Json<CreateOrganizationRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<OrganizationResponse>> {
    // Check if an organization with the same slug already exists
    if let Ok(_) = OrganizationRepository::find_by_slug(&mut conn, &request.slug) {
        return ApiResponse::error(
            Status::Conflict,
            ErrorCode::Conflict,
            &format!("Organization with slug '{}' already exists", request.slug)
        );
    }

    let new_org = NewOrganization {
        name: request.name.clone(),
        slug: request.slug.clone(),
        owner_id: auth.user_id,
        subscription_tier: "free".to_string(),
        description: None,
        logo_url: None,
        website: None,
        stripe_customer_id: None,
        stripe_subscription_id: None,
        subscription_tier_id: None,
        subscription_status: Some("active".to_string()),
        subscription_auto_renew: Some(true),
        billing_period_start: Some(Utc::now()),
        billing_period_end: Some(Utc::now() + chrono::Duration::days(30)),
    };

    // Create the organization
    let organization = match OrganizationRepository::create(&mut conn, &new_org) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error creating organization: {}", e);
            // Check if the error is due to a unique constraint violation
            if e.to_string().contains("duplicate key") || e.to_string().contains("unique constraint") {
                return ApiResponse::error(
                    Status::Conflict,
                    ErrorCode::Conflict,
                    "Organization with this name or slug already exists"
                );
            } else {
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to create organization"
                );
            }
        }
    };

    // Add the creator as an admin
    let new_member = NewOrganizationMember {
        organization_id: organization.id,
        user_id: auth.user_id,
        role: "admin".to_string(),
    };

    match OrganizationRepository::add_member(&mut conn, &new_member) {
        Ok(_) => {},
        Err(e) => {
            eprintln!("Error adding member to organization: {}", e);
            return ApiResponse::error_with_details(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Organization created but failed to add you as admin",
                json!({
                    "organization_id": organization.id.to_string(),
                    "error": e.to_string()
                })
            );
        }
    };

    ApiResponse::success_with_message(
        organization.into(),
        "Organization created successfully"
    )
}

#[get("/<id>")]
pub fn get_organization(
    auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Json<ApiResponse<OrganizationResponse>> {
    // Parse UUID
    let org_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Invalid organization ID format"
        )
    };

    // Check if user is a member of the organization
    let organizations = match OrganizationRepository::list_by_user(&mut conn, auth.user_id) {
        Ok(orgs) => orgs,
        Err(e) => {
            eprintln!("Error listing user organizations: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve user organizations"
            );
        }
    };

    if !organizations.iter().any(|org| org.id == org_id) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this organization"
        );
    }

    // Get the organization
    match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(organization) => ApiResponse::success(organization.into()),
        Err(_) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            "Organization not found"
        )
    }
}

#[get("/<slug>")]
pub fn get_organization_by_slug(
    auth: AuthUser,
    slug: String,
    mut conn: DbConn
) -> Json<ApiResponse<OrganizationResponse>> {
    // Get the organization by slug
    let organization = match OrganizationRepository::find_by_slug(&mut conn, &slug) {
        Ok(org) => org,
        Err(_) => return ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("Organization with slug '{}' not found", slug)
        )
    };

    // Check if user is a member of the organization
    let is_member = match OrganizationRepository::is_member(&mut conn, organization.id, auth.user_id) {
        Ok(is_member) => is_member,
        Err(e) => {
            eprintln!("Error checking organization membership: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to check organization membership"
            );
        }
    };

    if !is_member {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have access to this organization"
        );
    }

    // Return the organization
    ApiResponse::success(organization.into())
}

#[put("/<id>", format = "json", data = "<request>")]
pub fn update_organization(
    auth: AuthUser,
    id: String,
    request: Json<UpdateOrganizationRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<OrganizationResponse>> {
    // Parse UUID
    let org_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Invalid organization ID format"
        )
    };

    // Check if user is an admin of the organization
    let members = match OrganizationRepository::list_members(&mut conn, org_id) {
        Ok(members) => members,
        Err(e) => {
            eprintln!("Error listing organization members: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve organization members"
            );
        }
    };

    let is_admin = members.iter().any(|member|
        member.user_id == auth.user_id && member.role == "admin"
    );

    if !is_admin {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "You don't have permission to update this organization"
        );
    }

    let update = UpdateOrganization {
        name: request.name.clone(),
        slug: None,
        subscription_tier: request.subscription_tier.clone(),
        subscription_status: None,
        description: None,
        logo_url: None,
        website: None,
        updated_at: Some(chrono::Utc::now()),
        stripe_customer_id: None,
        stripe_subscription_id: None,
        subscription_tier_id: request.subscription_tier.clone().map(|tier_name| {
            // Convert tier name to ID
            match tier_name.as_str() {
                "starter" => Uuid::parse_str("00000000-0000-0000-0000-000000000001").unwrap_or_else(|_| Uuid::new_v4()),
                "pro" => Uuid::parse_str("00000000-0000-0000-0000-000000000002").unwrap_or_else(|_| Uuid::new_v4()),
                "enterprise" => Uuid::parse_str("00000000-0000-0000-0000-000000000003").unwrap_or_else(|_| Uuid::new_v4()),
                _ => Uuid::new_v4()
            }
        }),
        subscription_auto_renew: None,
        billing_period_start: None,
        billing_period_end: None,
    };

    // Update the organization
    match OrganizationRepository::update(&mut conn, org_id, &update) {
        Ok(organization) => ApiResponse::success_with_message(
            organization.into(),
            "Organization updated successfully"
        ),
        Err(e) => {
            eprintln!("Error updating organization: {}", e);
            // Check if the error is due to a unique constraint violation
            if e.to_string().contains("duplicate key") || e.to_string().contains("unique constraint") {
                ApiResponse::error(
                    Status::Conflict,
                    ErrorCode::Conflict,
                    "Organization with this name already exists"
                )
            } else {
                ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to update organization"
                )
            }
        }
    }
}
