use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use bcrypt::{hash, verify, DEFAULT_COST};
use chrono::Utc;
use uuid::Uuid;
use std::env;
use std::str::FromStr;
use rand::{thread_rng, Rng};
use rand::distributions::Alphanumeric;

use crate::auth::jwt::{generate_token, generate_refresh_token, decode_token, generate_password_reset_token, generate_email_verification_token};
use crate::auth::roles::Role;
use crate::db::DbConn;
use crate::models::user::{NewUser, UserProfile};
use crate::repositories::user_repository::UserRepository;
use crate::email::cloud_notification::CloudNotificationService;
use crate::email::templates::{
    EmailVerificationContext, PasswordResetContext,
    render_email_verification, render_password_reset
};
use crate::utils::response::{ApiResponse, ErrorCode};

// Helper function to generate a username from an email
fn generate_username_from_email(email: &str) -> String {
    // Extract the part before @ in the email
    let username_base = email.split('@').next().unwrap_or("user");

    // Add a random suffix to make it unique
    let suffix: String = thread_rng()
        .sample_iter(&Alphanumeric)
        .take(6)
        .map(char::from)
        .collect();

    format!("{}_{}", username_base, suffix)
}

#[derive(Debug, Deserialize)]
pub struct SignUpRequest {
    pub email: String,
    pub username: String,
    pub password: String,
    pub full_name: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct SignInRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Deserialize)]
pub struct RefreshTokenRequest {
    pub refresh_token: String,
}

#[derive(Debug, Deserialize)]
pub struct RequestPasswordResetRequest {
    pub email: String,
}

#[derive(Debug, Deserialize)]
pub struct ResetPasswordRequest {
    pub token: String,
    pub new_password: String,
}

#[derive(Debug, Deserialize)]
pub struct RequestEmailVerificationRequest {
    pub email: String,
}

#[derive(Debug, Deserialize)]
pub struct VerifyEmailRequest {
    pub token: String,
}

#[derive(Debug, Deserialize)]
pub struct GoogleAuthRequest {
    pub email: String,
    pub name: String,
    #[serde(rename = "googleId")]
    pub google_id: String,
    pub picture: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct AuthResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user: UserProfile,
}

#[derive(Debug, Serialize)]
pub struct TokenResponse {
    pub access_token: String,
    pub refresh_token: String,
}

#[derive(Debug, Serialize)]
pub struct MessageResponse {
    pub message: String,
}

#[post("/signup", format = "json", data = "<request>")]
pub async fn signup(
    request: Json<SignUpRequest>,
    mut conn: DbConn,
    email_service: &State<CloudNotificationService>
) -> Json<ApiResponse<AuthResponse>> {
    // Check if user already exists
    let email_exists = UserRepository::find_by_email(&mut conn, &request.email).is_ok();
    let username_exists = UserRepository::find_by_username(&mut conn, &request.username).is_ok();

    if email_exists {
        return ApiResponse::error(
            Status::Conflict,
            ErrorCode::Conflict,
            &format!("User with email '{}' already exists", request.email)
        );
    }

    if username_exists {
        return ApiResponse::error(
            Status::Conflict,
            ErrorCode::Conflict,
            &format!("Username '{}' is already taken", request.username)
        );
    }

    // Hash the password
    let password_hash = match hash(&request.password, DEFAULT_COST) {
        Ok(hash) => hash,
        Err(e) => {
            eprintln!("Password hashing error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to process password"
            );
        }
    };

    // Create user in our database
    let new_user = NewUser {
        email: request.email.clone(),
        username: request.username.clone(),
        password_hash: password_hash,
        full_name: request.full_name.clone(),
        profile_image_url: None,
        preferred_language: Some("en".to_string()),
        is_active: Some(true),
        email_verified: Some(false),
    };

    let user = match UserRepository::create(&mut conn, &new_user) {
        Ok(user) => user,
        Err(e) => {
            eprintln!("Database error creating user: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to create user account"
            );
        }
    };

    // Set email_verified to false
    let update = crate::models::user::UpdateUser {
        username: None,
        full_name: None,
        profile_image_url: None,
        preferred_language: None,
        is_active: Some(true),
        email_verified: Some(false),
        updated_at: Some(Utc::now()),
        password_hash: None,
    };

    if let Err(e) = UserRepository::update(&mut conn, user.id, &update) {
        eprintln!("Error updating user after creation: {}", e);
        return ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            "User created but failed to set initial status"
        );
    }

    // Generate verification token
    let verification_token = match generate_email_verification_token(
        user.id,
        &user.email,
        &user.email
    ) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Error generating verification token: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate email verification token"
            );
        }
    };

    // Get the app URL from environment variables
    let app_url = env::var("APP_URL").unwrap_or_else(|_| "http://localhost:3000".to_string());
    let app_name = env::var("APP_NAME").unwrap_or_else(|_| "ADC Multi-Languages".to_string());

    // Create verification link
    let verification_link = format!("{}/verify-email?token={}", app_url, verification_token);

    // Create email context
    let context = EmailVerificationContext {
        username: user.full_name.clone().unwrap_or_else(|| user.email.clone()),
        verification_link,
        app_name: app_name.clone(),
    };

    // Render email templates
    let (html_content, text_content) = match render_email_verification(&context) {
        Ok(content) => content,
        Err(e) => {
            eprintln!("Failed to render email template: {}", e);
            println!("Email verification token for {}: {}", user.email, verification_token);
            // Continue with signup even if email template fails
            // We'll just log the error and continue
            (
                format!("Please verify your email by clicking this link: {}/verify-email?token={}", app_url, verification_token),
                format!("Please verify your email by visiting this link: {}/verify-email?token={}", app_url, verification_token)
            )
        }
    };

    // Send verification email
    let to_name = user.full_name.as_deref().unwrap_or(&user.email);
    let subject = format!("Verify your email address for {}", app_name);

    // Clone necessary values for the async block
    let email_service = email_service.inner().clone();
    let user_email = user.email.clone();
    let user_id = user.id;
    let to_name = to_name.to_string();

    // Send email asynchronously
    tokio::spawn(async move {
        match email_service.send_email(
            &user_email,
            Some(&to_name),
            &subject,
            &html_content,
            &text_content,
            Some(user_id),
        ).await {
            Ok(message_id) => println!("Verification email sent to {}, message ID: {}", user_email, message_id),
            Err(e) => {
                eprintln!("Failed to send verification email: {}", e);

                // Check if this is an IP whitelist error
                if e.to_string().contains("IP Whitelist Error") {
                    eprintln!("Note: This error is due to IP restrictions in Brevo.");
                    eprintln!("To fix this, please whitelist your server's IP address in the Brevo dashboard:");
                    eprintln!("https://app.brevo.com/security/authorised_ips");

                    // In a production environment, you might want to log this error or notify an admin
                    // For now, we'll just print a message to the console
                    println!("IMPORTANT: Email sending is currently disabled due to IP restrictions.");
                    println!("Please whitelist your IP address in the Brevo dashboard to enable email sending.");
                }
            },
        }
    });

    // Generate tokens
    let access_token = match generate_token(
        user.id,
        &user.email,
        &user.email,
        Role::User
    ) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate authentication token"
            );
        }
    };

    let refresh_token = match generate_refresh_token(user.id) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Refresh token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate refresh token"
            );
        }
    };

    // Return the auth response
    ApiResponse::success_with_message(
        AuthResponse {
            access_token,
            refresh_token,
            user: user.into(),
        },
        "Account created successfully. Please check your email to verify your account."
    )
}

#[post("/signin", format = "json", data = "<request>")]
pub async fn signin(
    request: Json<SignInRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<AuthResponse>> {
    // Find user by email
    let user = match UserRepository::find_by_email(&mut conn, &request.email) {
        Ok(user) => user,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid email or password"
            );
        }
    };

    // Verify password
    let password_matches = match verify(&request.password, &user.password_hash) {
        Ok(matches) => matches,
        Err(e) => {
            eprintln!("Password verification error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to verify credentials"
            );
        }
    };

    if !password_matches {
        return ApiResponse::error(
            Status::Unauthorized,
            ErrorCode::Unauthorized,
            "Invalid email or password"
        );
    }

    // Generate tokens
    let access_token = match generate_token(
        user.id,
        &user.email,
        &user.email,
        Role::User
    ) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate authentication token"
            );
        }
    };

    let refresh_token = match generate_refresh_token(user.id) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Refresh token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate refresh token"
            );
        }
    };

    // Update last login time
    let update = crate::models::user::UpdateUser {
        username: None,
        full_name: None,
        profile_image_url: None,
        preferred_language: None,
        is_active: None,
        email_verified: None,
        updated_at: Some(Utc::now()),
        password_hash: None,
    };

    if let Err(e) = UserRepository::update(&mut conn, user.id, &update) {
        eprintln!("Error updating last login time: {}", e);
        // Continue with login even if updating last login time fails
    }

    // Return the auth response
    ApiResponse::success_with_message(
        AuthResponse {
            access_token,
            refresh_token,
            user: user.into(),
        },
        "Login successful"
    )
}

#[post("/refresh", format = "json", data = "<request>")]
pub async fn refresh_token(
    request: Json<RefreshTokenRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<TokenResponse>> {
    // Decode the refresh token
    let claims = match decode_token(&request.refresh_token) {
        Ok(claims) => claims,
        Err(e) => {
            eprintln!("Token decode error: {}", e);
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid refresh token"
            );
        }
    };

    // Check if the token is a refresh token
    if claims.role != "refresh" {
        return ApiResponse::error(
            Status::Unauthorized,
            ErrorCode::Unauthorized,
            "Invalid token type"
        );
    }

    // Parse the user ID from the token
    let user_id = match Uuid::parse_str(&claims.sub) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid token subject"
            );
        }
    };

    // Find the user in the database
    let user = match UserRepository::find_by_id(&mut conn, user_id) {
        Ok(user) => user,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "User not found"
            );
        }
    };

    // Generate new tokens
    let access_token = match generate_token(
        user.id,
        &user.email,
        &user.email,
        Role::User
    ) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate new access token"
            );
        }
    };

    let refresh_token = match generate_refresh_token(user.id) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Refresh token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate new refresh token"
            );
        }
    };

    // Return the new tokens
    ApiResponse::success(TokenResponse {
        access_token,
        refresh_token,
    })
}

#[post("/password-reset/request", format = "json", data = "<request>")]
pub async fn request_password_reset(
    request: Json<RequestPasswordResetRequest>,
    mut conn: DbConn,
    email_service: &State<CloudNotificationService>
) -> Json<ApiResponse<MessageResponse>> {
    // Find user by email
    let user = match UserRepository::find_by_email(&mut conn, &request.email) {
        Ok(user) => user,
        Err(_) => {
            // Don't reveal if the email exists or not for security reasons
            return ApiResponse::success(MessageResponse {
                message: "If your email is registered, you will receive a password reset link.".to_string(),
            });
        }
    };

    // Generate password reset token
    let reset_token = match generate_password_reset_token(user.id, &user.email) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Error generating password reset token: {}", e);
            return ApiResponse::success(MessageResponse {
                message: "If your email is registered, you will receive a password reset link.".to_string(),
            });
        }
    };

    // Get the app URL from environment variables
    let app_url = env::var("APP_URL").unwrap_or_else(|_| "http://localhost:3000".to_string());
    let app_name = env::var("APP_NAME").unwrap_or_else(|_| "ADC Multi-Languages".to_string());

    // Create reset link
    let reset_link = format!("{}/reset-password?token={}", app_url, reset_token);

    // Create email context
    let context = PasswordResetContext {
        username: user.full_name.clone().unwrap_or_else(|| user.email.clone()),
        reset_link,
        app_name: app_name.clone(),
    };

    // Render email templates
    let (html_content, text_content) = match render_password_reset(&context) {
        Ok(content) => content,
        Err(e) => {
            eprintln!("Failed to render email template: {}", e);
            println!("Password reset token for {}: {}", user.email, reset_token);
            return ApiResponse::success(MessageResponse {
                message: "If your email is registered, you will receive a password reset link.".to_string(),
            });
        }
    };

    // Send password reset email
    let to_name = user.full_name.as_deref().unwrap_or(&user.email);
    let subject = format!("Reset your password for {}", app_name);

    // Clone necessary values for the async block
    let email_service = email_service.inner().clone();
    let user_email = user.email.clone();
    let user_id = user.id;
    let to_name = to_name.to_string();

    // Send email asynchronously
    tokio::spawn(async move {
        match email_service.send_email(
            &user_email,
            Some(&to_name),
            &subject,
            &html_content,
            &text_content,
            Some(user_id),
        ).await {
            Ok(message_id) => println!("Password reset email sent to {}, message ID: {}", user_email, message_id),
            Err(e) => {
                eprintln!("Failed to send password reset email: {}", e);

                // Check if this is an IP whitelist error
                if e.to_string().contains("IP Whitelist Error") {
                    eprintln!("Note: This error is due to IP restrictions in Brevo.");
                    eprintln!("To fix this, please whitelist your server's IP address in the Brevo dashboard:");
                    eprintln!("https://app.brevo.com/security/authorised_ips");

                    // In a production environment, you might want to log this error or notify an admin
                    // For now, we'll just print a message to the console
                    println!("IMPORTANT: Email sending is currently disabled due to IP restrictions.");
                    println!("Please whitelist your IP address in the Brevo dashboard to enable email sending.");
                }
            },
        }
    });

    // Return success message
    ApiResponse::success(MessageResponse {
        message: "If your email is registered, you will receive a password reset link.".to_string(),
    })
}

#[post("/password-reset/confirm", format = "json", data = "<request>")]
pub async fn confirm_password_reset(
    request: Json<ResetPasswordRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<MessageResponse>> {
    // Decode the reset token
    let claims = match decode_token(&request.token) {
        Ok(claims) => claims,
        Err(e) => {
            eprintln!("Token decode error: {}", e);
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid or expired reset token"
            );
        }
    };

    // Check if the token is a password reset token
    if claims.role != "password_reset" {
        return ApiResponse::error(
            Status::Unauthorized,
            ErrorCode::Unauthorized,
            "Invalid token type"
        );
    }

    // Parse the user ID from the token
    let user_id = match Uuid::parse_str(&claims.sub) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid token subject"
            );
        }
    };

    // Find the user in the database
    let user = match UserRepository::find_by_id(&mut conn, user_id) {
        Ok(user) => user,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "User not found"
            );
        }
    };

    // Verify that the email in the token matches the user's email
    if claims.email != user.email {
        return ApiResponse::error(
            Status::Unauthorized,
            ErrorCode::Unauthorized,
            "Token does not match user"
        );
    }

    // Hash the new password
    let password_hash = match hash(&request.new_password, DEFAULT_COST) {
        Ok(hash) => hash,
        Err(e) => {
            eprintln!("Password hashing error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to process new password"
            );
        }
    };

    // Update the user's password
    let update = crate::models::user::UpdateUser {
        username: None,
        full_name: None,
        profile_image_url: None,
        preferred_language: None,
        is_active: None,
        email_verified: None,
        updated_at: Some(Utc::now()),
        password_hash: Some(password_hash),
    };

    match UserRepository::update(&mut conn, user.id, &update) {
        Ok(_) => {},
        Err(e) => {
            eprintln!("Error updating user password: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update password"
            );
        }
    }

    // Return success message
    ApiResponse::success_with_message(
        MessageResponse {
            message: "Your password has been reset successfully.".to_string(),
        },
        "Password reset successful"
    )
}

#[post("/email-verification/request", format = "json", data = "<request>")]
pub async fn request_email_verification(
    request: Json<RequestEmailVerificationRequest>,
    mut conn: DbConn,
    email_service: &State<CloudNotificationService>
) -> Json<ApiResponse<MessageResponse>> {
    // Find user by email
    let user = match UserRepository::find_by_email(&mut conn, &request.email) {
        Ok(user) => user,
        Err(_) => {
            // Don't reveal if the email exists or not for security reasons
            return ApiResponse::success(MessageResponse {
                message: "If your email is registered, you will receive a verification link.".to_string(),
            });
        }
    };

    // Check if email is already verified
    if user.email_verified.unwrap_or(false) {
        return ApiResponse::success(MessageResponse {
            message: "Your email is already verified.".to_string(),
        });
    }

    // Generate verification token
    let verification_token = match generate_email_verification_token(
        user.id,
        &user.email,
        &user.email
    ) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Error generating verification token: {}", e);
            return ApiResponse::success(MessageResponse {
                message: "If your email is registered, you will receive a verification link.".to_string(),
            });
        }
    };

    // Get the app URL from environment variables
    let app_url = env::var("APP_URL").unwrap_or_else(|_| "http://localhost:3000".to_string());
    let app_name = env::var("APP_NAME").unwrap_or_else(|_| "ADC Multi-Languages".to_string());

    // Create verification link
    let verification_link = format!("{}/verify-email?token={}", app_url, verification_token);

    // Create email context
    let context = EmailVerificationContext {
        username: user.full_name.clone().unwrap_or_else(|| user.email.clone()),
        verification_link,
        app_name: app_name.clone(),
    };

    // Render email templates
    let (html_content, text_content) = match render_email_verification(&context) {
        Ok(content) => content,
        Err(e) => {
            eprintln!("Failed to render email template: {}", e);
            println!("Email verification token for {}: {}", user.email, verification_token);
            return ApiResponse::success(MessageResponse {
                message: "If your email is registered, you will receive a verification link.".to_string(),
            });
        }
    };

    // Send verification email
    let to_name = user.full_name.as_deref().unwrap_or(&user.email);
    let subject = format!("Verify your email address for {}", app_name);

    // Clone necessary values for the async block
    let email_service = email_service.inner().clone();
    let user_email = user.email.clone();
    let user_id = user.id;
    let to_name = to_name.to_string();

    // Send email asynchronously
    tokio::spawn(async move {
        match email_service.send_email(
            &user_email,
            Some(&to_name),
            &subject,
            &html_content,
            &text_content,
            Some(user_id),
        ).await {
            Ok(message_id) => println!("Verification email sent to {}, message ID: {}", user_email, message_id),
            Err(e) => {
                eprintln!("Failed to send verification email: {}", e);

                // Check if this is an IP whitelist error
                if e.to_string().contains("IP Whitelist Error") {
                    eprintln!("Note: This error is due to IP restrictions in Brevo.");
                    eprintln!("To fix this, please whitelist your server's IP address in the Brevo dashboard:");
                    eprintln!("https://app.brevo.com/security/authorised_ips");

                    // In a production environment, you might want to log this error or notify an admin
                    // For now, we'll just print a message to the console
                    println!("IMPORTANT: Email sending is currently disabled due to IP restrictions.");
                    println!("Please whitelist your IP address in the Brevo dashboard to enable email sending.");
                }
            },
        }
    });

    // Return success message
    ApiResponse::success(MessageResponse {
        message: "If your email is registered, you will receive a verification link.".to_string(),
    })
}

#[post("/email-verification/verify", format = "json", data = "<request>")]
pub async fn verify_email(
    request: Json<VerifyEmailRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<MessageResponse>> {
    // Decode the verification token
    let claims = match decode_token(&request.token) {
        Ok(claims) => claims,
        Err(e) => {
            eprintln!("Token decode error: {}", e);
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid or expired verification token"
            );
        }
    };

    // Check if the token is an email verification token
    if claims.role != "email_verification" {
        return ApiResponse::error(
            Status::Unauthorized,
            ErrorCode::Unauthorized,
            "Invalid token type"
        );
    }

    // Parse the user ID from the token
    let user_id = match Uuid::parse_str(&claims.sub) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "Invalid token subject"
            );
        }
    };

    // Find the user in the database
    let user = match UserRepository::find_by_id(&mut conn, user_id) {
        Ok(user) => user,
        Err(_) => {
            return ApiResponse::error(
                Status::Unauthorized,
                ErrorCode::Unauthorized,
                "User not found"
            );
        }
    };

    // Verify that the email in the token matches the user's email
    if claims.email != user.email {
        return ApiResponse::error(
            Status::Unauthorized,
            ErrorCode::Unauthorized,
            "Token does not match user"
        );
    }

    // Check if email is already verified
    if user.email_verified.unwrap_or(false) {
        return ApiResponse::success(MessageResponse {
            message: "Your email is already verified.".to_string(),
        });
    }

    // Update the user's email_verified status
    let update = crate::models::user::UpdateUser {
        username: None,
        full_name: None,
        profile_image_url: None,
        preferred_language: None,
        is_active: None,
        email_verified: Some(true),
        updated_at: Some(Utc::now()),
        password_hash: None,
    };

    match UserRepository::update(&mut conn, user.id, &update) {
        Ok(_) => {},
        Err(e) => {
            eprintln!("Error updating user email verification status: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to verify email"
            );
        }
    }

    // Return success message
    ApiResponse::success_with_message(
        MessageResponse {
            message: "Your email has been verified successfully.".to_string(),
        },
        "Email verification successful"
    )
}

#[post("/google", format = "json", data = "<request>")]
pub async fn google_auth(
    request: Json<GoogleAuthRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<AuthResponse>> {
    // Check if user already exists by email
    let user = match UserRepository::find_by_email(&mut conn, &request.email) {
        Ok(user) => {
            // User exists, update Google ID if needed
            // In a real implementation, you might want to check if the user was previously
            // created with a password and handle that case appropriately
            user
        },
        Err(_) => {
            // User doesn't exist, create a new one
            // No need to generate username as it's not used in the User model

            // Generate a username from the email
            let username = generate_username_from_email(&request.email);

            let new_user = NewUser {
                email: request.email.clone(),
                username,
                password_hash: "".to_string(), // Empty password for Google users
                full_name: Some(request.name.clone()),
                profile_image_url: request.picture.clone(),
                preferred_language: Some("en".to_string()),
                is_active: Some(true),
                email_verified: Some(true), // Google emails are verified
            };

            match UserRepository::create(&mut conn, &new_user) {
                Ok(user) => user,
                Err(e) => {
                    eprintln!("Error creating user: {}", e);
                    return ApiResponse::error(
                        Status::InternalServerError,
                        ErrorCode::InternalError,
                        "Failed to create user account"
                    );
                }
            }
        }
    };

    // Generate tokens
    // Since we don't have a role field in the User model anymore, default to User role
    let role = Role::User;

    let access_token = match generate_token(
        user.id,
        &user.email,
        &user.full_name.clone().unwrap_or_else(|| "User".to_string()),
        role
    ) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate authentication token"
            );
        }
    };

    let refresh_token = match generate_refresh_token(user.id) {
        Ok(token) => token,
        Err(e) => {
            eprintln!("Refresh token generation error: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to generate refresh token"
            );
        }
    };

    // Return the auth response
    ApiResponse::success(
        AuthResponse {
            access_token,
            refresh_token,
            user: user.into(),
        }
    )
}