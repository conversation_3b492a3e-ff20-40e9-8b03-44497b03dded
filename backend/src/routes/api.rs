use rocket::serde::{Serialize, Deserialize, json::J<PERSON>};
use rocket::http::Status;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Serialize, Deserialize)]
pub struct Message {
    message: String,
}

#[get("/hello")]
pub fn hello() -> J<PERSON><serde_json::Value> {
    Json(serde_json::json!({
        "data": {
            "message": "Hello, world!"
        },
        "message": "Hello from the API",
        "success": true
    }))
}

#[post("/echo", data = "<input>")]
pub fn echo(input: Json<Message>) -> <PERSON><PERSON><ApiResponse<Message>> {
    ApiResponse::success_with_message(
        Message {
            message: input.message.clone(),
        },
        "Echo successful"
    )
}

#[get("/error-example")]
pub fn error_example() -> J<PERSON><ApiResponse<Message>> {
    ApiResponse::error(
        Status::BadRequest,
        ErrorCode::ValidationError,
        "This is an example error response"
    )
}
