use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use serde_json::json;
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::permission_group::{PermissionGroupResponse, CreatePermissionGroupRequest, UpdatePermissionGroupRequest};
use crate::repositories::permission_group_repository::PermissionGroupRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

// List permission groups for an organization
#[get("/<organization_id>/permission-groups")]
pub fn list_permission_groups(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<PermissionGroupResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get permission groups
    match PermissionGroupRepository::list_permission_groups_by_organization(&mut conn, org_id) {
        Ok(groups) => {
            let response: Vec<PermissionGroupResponse> = groups.into_iter()
                .map(|group| group.into())
                .collect();
            
            ApiResponse::success(response)
        },
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to list permission groups: {}", e)
        )
    }
}

// Get a permission group by ID
#[get("/<organization_id>/permission-groups/<group_id>")]
pub fn get_permission_group(
    auth: AuthUser,
    organization_id: String,
    group_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<PermissionGroupResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let permission_group_id = match Uuid::parse_str(&group_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid permission group ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the permission group
    match PermissionGroupRepository::get_permission_group_by_id(&mut conn, permission_group_id) {
        Ok(group) => {
            // Check if the group belongs to the organization
            if group.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "Permission group does not belong to this organization"
                );
            }

            ApiResponse::success(group.into())
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("Permission group not found: {}", e)
        )
    }
}

// Create a new permission group
#[post("/<organization_id>/permission-groups", format = "json", data = "<request>")]
pub fn create_permission_group(
    auth: AuthUser,
    organization_id: String,
    request: Json<CreatePermissionGroupRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<PermissionGroupResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Validate the request
    if request.name.trim().is_empty() {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::ValidationError,
            "Permission group name is required"
        );
    }

    // Create the permission group
    match PermissionGroupRepository::create_permission_group(
        &mut conn,
        org_id,
        &request.name,
        &request.description,
        &request.permissions,
        auth.user_id
    ) {
        Ok(group) => ApiResponse::success_with_message(
            group.into(),
            "Permission group created successfully"
        ),
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to create permission group: {}", e)
        )
    }
}

// Update a permission group
#[put("/<organization_id>/permission-groups/<group_id>", format = "json", data = "<request>")]
pub fn update_permission_group(
    auth: AuthUser,
    organization_id: String,
    group_id: String,
    request: Json<UpdatePermissionGroupRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<PermissionGroupResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let permission_group_id = match Uuid::parse_str(&group_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid permission group ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the permission group to check if it belongs to the organization
    match PermissionGroupRepository::get_permission_group_by_id(&mut conn, permission_group_id) {
        Ok(group) => {
            if group.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "Permission group does not belong to this organization"
                );
            }

            // Update the permission group
            match PermissionGroupRepository::update_permission_group(&mut conn, permission_group_id, &request) {
                Ok(updated_group) => ApiResponse::success_with_message(
                    updated_group.into(),
                    "Permission group updated successfully"
                ),
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to update permission group: {}", e)
                )
            }
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("Permission group not found: {}", e)
        )
    }
}

// Delete a permission group
#[delete("/<organization_id>/permission-groups/<group_id>")]
pub fn delete_permission_group(
    auth: AuthUser,
    organization_id: String,
    group_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let permission_group_id = match Uuid::parse_str(&group_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid permission group ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the permission group to check if it belongs to the organization
    match PermissionGroupRepository::get_permission_group_by_id(&mut conn, permission_group_id) {
        Ok(group) => {
            if group.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "Permission group does not belong to this organization"
                );
            }

            // Delete the permission group
            match PermissionGroupRepository::delete_permission_group(&mut conn, permission_group_id) {
                Ok(_) => ApiResponse::success_with_message(
                    json!({"id": permission_group_id.to_string()}),
                    "Permission group deleted successfully"
                ),
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to delete permission group: {}", e)
                )
            }
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("Permission group not found: {}", e)
        )
    }
}
