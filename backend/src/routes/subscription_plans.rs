use rocket::serde::json::<PERSON><PERSON>;
use serde::Serialize;
use uuid::Uuid;
use chrono::Utc;

use crate::db::DbConn;
use crate::utils::response::ApiResponse;

#[derive(Debug, Serialize)]
pub struct SubscriptionPlanResponse {
    pub id: String,
    pub name: String,
    pub description: String,
    pub price_monthly: f64,
    pub price_yearly: f64,
    pub features: Vec<String>,
    pub is_popular: bool,
    pub stripe_price_monthly: Option<String>,
    pub stripe_price_yearly: Option<String>,
    pub created_at: String,
    pub updated_at: String,
}

/// Get Subscription Plans API
///
/// Returns a list of available subscription plans
#[get("/")]
pub fn list_subscription_plans(_conn: DbConn) -> Json<ApiResponse<Vec<SubscriptionPlanResponse>>> {
    // For simplicity, we'll return mock data with real Stripe price IDs
    let default_plans = vec![
        SubscriptionPlanResponse {
            id: Uuid::new_v4().to_string(),
            name: "Free".to_string(),
            description: "Basic features for small projects".to_string(),
            price_monthly: 0.0,
            price_yearly: 0.0,
            features: vec![
                "Up to 3 projects".to_string(),
                "2 team members".to_string(),
                "100 AI translation credits/month".to_string(),
                "Basic support".to_string(),
            ],
            is_popular: false,
            stripe_price_monthly: None,
            stripe_price_yearly: None,
            created_at: Utc::now().to_rfc3339(),
            updated_at: Utc::now().to_rfc3339(),
        },
        SubscriptionPlanResponse {
            id: Uuid::new_v4().to_string(),
            name: "Standard".to_string(),
            description: "Perfect for growing teams".to_string(),
            price_monthly: 29.0,
            price_yearly: 290.0,
            features: vec![
                "Unlimited projects".to_string(),
                "10 team members".to_string(),
                "1,000 AI translation credits/month".to_string(),
                "Priority support".to_string(),
            ],
            is_popular: true,
            stripe_price_monthly: Some("price_1RQR2ZCtNXkGk5bXcBExxwz6".to_string()),
            stripe_price_yearly: Some("price_1RQR2ZCtNXkGk5bXlucMrhFV".to_string()),
            created_at: Utc::now().to_rfc3339(),
            updated_at: Utc::now().to_rfc3339(),
        },
        SubscriptionPlanResponse {
            id: Uuid::new_v4().to_string(),
            name: "Premium".to_string(),
            description: "Advanced features for professional teams".to_string(),
            price_monthly: 99.0,
            price_yearly: 990.0,
            features: vec![
                "Unlimited projects".to_string(),
                "Unlimited team members".to_string(),
                "5,000 AI translation credits/month".to_string(),
                "Priority support".to_string(),
                "Advanced analytics".to_string(),
                "Custom integrations".to_string(),
            ],
            is_popular: false,
            stripe_price_monthly: Some("price_1RQR2aCtNXkGk5bXmu3lnwHO".to_string()),
            stripe_price_yearly: Some("price_1RQR2bCtNXkGk5bXPGHSeLB4".to_string()),
            created_at: Utc::now().to_rfc3339(),
            updated_at: Utc::now().to_rfc3339(),
        },
        SubscriptionPlanResponse {
            id: Uuid::new_v4().to_string(),
            name: "Enterprise".to_string(),
            description: "For large organizations with complex needs".to_string(),
            price_monthly: 299.0,
            price_yearly: 2990.0,
            features: vec![
                "Unlimited projects".to_string(),
                "Unlimited team members".to_string(),
                "Unlimited AI translation credits".to_string(),
                "24/7 priority support".to_string(),
                "Advanced analytics".to_string(),
                "Custom integrations".to_string(),
                "Dedicated account manager".to_string(),
            ],
            is_popular: false,
            stripe_price_monthly: Some("price_1RQR2bCtNXkGk5bXXRqRCtjm".to_string()),
            stripe_price_yearly: Some("price_1RQR2cCtNXkGk5bXMIt48LkV".to_string()),
            created_at: Utc::now().to_rfc3339(),
            updated_at: Utc::now().to_rfc3339(),
        },
    ];

    ApiResponse::success(default_plans)
}
