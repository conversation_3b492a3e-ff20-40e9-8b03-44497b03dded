use rocket::serde::json::J<PERSON>;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::Utc;
use serde_json::json;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::ai_credit::{
    AICreditBalance, AICreditPricingResponse, AICreditPricingTier,
    AICreditUsageResponse, AICreditPurchase, AITranslationResponse
};
use crate::repositories::ai_credit_repository::AICreditRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::ai::translation::AITranslationRequest;
use crate::ai::gemini::GeminiTranslationService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct AITranslateRequest {
    pub text: String,
    pub source_locale: String,
    pub target_locale: String,
    pub context: Option<String>,
    pub organization_id: String,
    pub provider: Option<String>,
    pub model: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct PurchaseCreditsRequest {
    pub amount: i32,
}

#[derive(Debug, Deserialize)]
pub struct AICreditsHistoryRequest {
    pub page: Option<i64>,
    pub per_page: Option<i64>,
    pub start_date: Option<String>,
    pub end_date: Option<String>,
}

// AI Translation API
#[post("/ai-translate", format = "json", data = "<request>")]
pub async fn ai_translate(
    auth: AuthUser,
    request: Json<AITranslateRequest>,
    mut conn: DbConn,
    ai_service: &State<GeminiTranslationService>
) -> Json<ApiResponse<AITranslationResponse>> {
    // Parse the organization ID
    let org_id = match Uuid::parse_str(&request.organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    };

    // Create the AI translation request
    let ai_request = AITranslationRequest {
        text: request.text.clone(),
        source_locale: request.source_locale.clone(),
        target_locale: request.target_locale.clone(),
        context: request.context.clone(),
        model: request.model.clone(),
        provider: request.provider.clone(),
    };

    // Call the AI translation service
    let translation_result = match ai_service.translate(&ai_request).await {
        Ok(result) => result,
        Err(e) => {
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("AI translation failed: {}", e)
            );
        }
    };

    // Get the credits used from the translation result
    let credits_needed = translation_result.credits_used;

    // Record credit usage and check if there are enough credits
    let usage_result = AICreditRepository::record_credit_usage(
        &mut conn,
        org_id,
        Some(auth.user_id),
        credits_needed,
        "translation",
        Some(&request.source_locale),
        Some(&request.target_locale),
        Some(request.text.len() as i32),
        Some(&translation_result.model_used),
    );

    match usage_result {
        Ok((_, remaining_credits)) => {
            ApiResponse::success(AITranslationResponse {
                translated_text: translation_result.translated_text,
                model_used: translation_result.model_used,
                credits_used: credits_needed,
                credits_remaining: remaining_credits,
            })
        },
        Err(e) => {
            if e.to_string().contains("Insufficient credits") {
                ApiResponse::error(
                    Status::PaymentRequired,
                    ErrorCode::PaymentRequired,
                    "Insufficient credits for AI translation. Please purchase more credits."
                )
            } else {
                ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to record credit usage: {}", e)
                )
            }
        }
    }
}

// Get AI Credits API
#[get("/<organization_id>/ai-credits")]
pub fn get_ai_credits(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<AICreditBalance>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the organization's credit balance
    match AICreditRepository::get_organization_credits(&mut conn, org_id) {
        Ok(balance) => ApiResponse::success(balance),
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to retrieve AI credits: {}", e)
        )
    }
}

// Purchase AI Credits API
#[post("/<organization_id>/ai-credits/purchase", format = "json", data = "<request>")]
pub async fn purchase_ai_credits(
    auth: AuthUser,
    organization_id: String,
    request: Json<PurchaseCreditsRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Validate the amount
    if request.amount < 100 {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::ValidationError,
            "Minimum purchase amount is 100 credits"
        );
    }

    // Get the pricing tier for this amount
    let pricing_tier = match AICreditRepository::find_pricing_tier_for_amount(&mut conn, request.amount) {
        Ok(tier) => tier,
        Err(e) => {
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Failed to determine pricing: {}", e)
            );
        }
    };

    // Calculate the total price
    let price_per_credit = pricing_tier.price_per_credit;
    let total_price = price_per_credit * request.amount as f64;

    // Return information for the frontend to handle the payment
    // The actual credit purchase will be handled by the webhook
    ApiResponse::success(json!({
        "credits_amount": request.amount,
        "price_per_credit": price_per_credit,
        "total_price": total_price,
        "currency": "USD",
        "organization_id": org_id.to_string(),
        "message": "Please complete payment to receive credits. Credits will be added when payment is confirmed."
    }))
}

// AI Credits Usage History API
#[get("/<organization_id>/ai-credits/history?<page>&<per_page>&<start_date>&<end_date>")]
pub fn get_ai_credits_history(
    auth: AuthUser,
    organization_id: String,
    page: Option<i64>,
    per_page: Option<i64>,
    start_date: Option<String>,
    end_date: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<AICreditUsageResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Set pagination defaults
    let page = page.unwrap_or(1);
    let per_page = per_page.unwrap_or(10).min(100);
    let offset = (page - 1) * per_page;

    // Get the usage history
    match AICreditRepository::list_usage_by_organization(&mut conn, org_id, per_page, offset) {
        Ok(usage_list) => {
            // Convert to response format
            let response = usage_list.into_iter()
                .map(|usage| {
                    // Extract operation and other details from the details JSON field
                    // Get operation and other details directly from the fields
                    let operation = usage.operation.clone();
                    let source_locale = usage.source_locale.clone();
                    let target_locale = usage.target_locale.clone();
                    let text_length = usage.text_length;

                    AICreditUsageResponse {
                        id: usage.id.to_string(),
                        credits_used: usage.credits_used,
                        feature: usage.operation.clone(),
                        operation: Some(operation),
                        source_locale,
                        target_locale,
                        text_length,
                        user_id: usage.user_id.map(|id| id.to_string()),
                        created_at: usage.created_at.unwrap_or_else(Utc::now),
                    }
                })
                .collect();

            ApiResponse::success(response)
        },
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to retrieve usage history: {}", e)
        )
    }
}

// AI Credits Pricing API
#[get("/pricing")]
pub fn get_ai_credits_pricing(
    mut conn: DbConn
) -> Json<ApiResponse<AICreditPricingResponse>> {
    match AICreditRepository::list_pricing_tiers(&mut conn) {
        Ok(tiers) => {
            // Convert to response format
            let pricing_tiers = tiers.into_iter()
                .map(|tier| AICreditPricingTier {
                    min_credits: tier.min_credits,
                    max_credits: tier.max_credits,
                    price_per_credit: tier.price_per_credit.to_string().parse::<f64>().unwrap_or(0.01),
                })
                .collect::<Vec<_>>();

            // Get the minimum purchase amount and default price
            let min_purchase = pricing_tiers.first()
                .map(|tier| tier.min_credits)
                .unwrap_or(100);

            let default_price = pricing_tiers.first()
                .map(|tier| tier.price_per_credit)
                .unwrap_or(0.01);

            ApiResponse::success(AICreditPricingResponse {
                price_per_credit: default_price,
                currency: "USD".to_string(),
                minimum_purchase: min_purchase,
                tiers: pricing_tiers,
            })
        },
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to retrieve pricing information: {}", e)
        )
    }
}
