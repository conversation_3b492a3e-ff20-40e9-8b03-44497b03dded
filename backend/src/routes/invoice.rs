use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::payment::stripe::StripeService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Serialize)]
pub struct InvoiceDownloadResponse {
    pub download_url: String,
}

// Download Invoice API
#[get("/<organization_id>/invoices/<invoice_id>/download")]
pub async fn download_invoice(
    auth: AuthUser,
    organization_id: String,
    invoice_id: String,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<InvoiceDownloadResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // In a real implementation, you would:
    // 1. Verify the invoice belongs to the organization
    // 2. Get the invoice PDF URL from Stripe
    // 3. Return the URL or generate a signed URL for the PDF

    // For now, return a mock URL
    let download_url = format!("https://example.com/invoices/{}.pdf", invoice_id);
    
    ApiResponse::success(InvoiceDownloadResponse { download_url })
}
