use rocket::serde::json::Json;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use diesel::prelude::*;

#[derive(QueryableByName, Debug)]
#[diesel(check_for_backend(diesel::pg::Pg))]
struct SubscriptionTierName {
    #[diesel(sql_type = diesel::sql_types::Text)]
    name: String,
}

#[derive(QueryableByName, Debug)]
#[diesel(check_for_backend(diesel::pg::Pg))]
struct SubscriptionTierId {
    #[diesel(sql_type = diesel::sql_types::Uuid)]
    id: Uuid,
}

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::organization::UpdateOrganization;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::payment::StripeService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Serialize)]
pub struct SubscriptionResponse {
    pub id: String,
    pub organization_id: String,
    pub plan_id: String,
    pub status: String,
    pub current_period_start: String,
    pub current_period_end: String,
    pub cancel_at_period_end: bool,
    pub created_at: String,
    pub updated_at: String,
    pub plan: SubscriptionPlanInfo,
}

#[derive(Debug, Serialize)]
pub struct SubscriptionPlanInfo {
    pub id: String,
    pub name: String,
    pub description: String,
    pub price: f64,
    pub currency: String,
    pub interval: String,
}

#[derive(Debug, Deserialize)]
pub struct CreateSubscriptionRequest {
    pub plan_id: String,
    pub payment_method_id: String,
}

#[derive(Debug, Deserialize)]
pub struct CancelSubscriptionRequest {
    pub cancel_at_period_end: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct VerifySubscriptionRequest {
    pub stripe_subscription_id: String,
    pub price_id: String,
    pub status: String,
    pub current_period_end: String,
}

/// Get Organization Subscription API
///
/// Returns the current subscription for an organization
#[get("/<organization_id>/subscription")]
pub fn get_organization_subscription(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<SubscriptionResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Check if organization has a subscription tier
    let subscription_tier_id = match organization.subscription_tier_id {
        Some(id) => id,
        None => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization does not have an active subscription"
            );
        }
    };

    // In a real implementation, we would fetch the subscription details from the database
    // For now, we'll use a mock subscription ID
    let stripe_subscription_id = format!("sub_mock_{}", subscription_tier_id);

    // In a real implementation, you would fetch the subscription details from Stripe
    // For now, we'll return mock data based on the organization's subscription tier

    // Get subscription tier name from the database
    // For now, we'll use a hardcoded value since we can't use sql_query with DbConn
    let subscription_tier_name = match subscription_tier_id.to_string().as_str() {
        "00000000-0000-0000-0000-000000000001" => "starter".to_string(),
        "00000000-0000-0000-0000-000000000002" => "pro".to_string(),
        "00000000-0000-0000-0000-000000000003" => "enterprise".to_string(),
        _ => "free".to_string()
    };

    let plan_id = format!("plan_{}", subscription_tier_name);

    let plan_info = match subscription_tier_name.as_str() {
        "free" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Free".to_string(),
            description: "Free plan for small projects".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "starter" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Starter".to_string(),
            description: "Starter plan for growing teams".to_string(),
            price: 9.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "pro" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Pro".to_string(),
            description: "Pro plan for professional teams".to_string(),
            price: 29.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "enterprise" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Enterprise".to_string(),
            description: "Enterprise plan for large organizations".to_string(),
            price: 99.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        _ => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Unknown".to_string(),
            description: "Unknown plan".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
    };

    let subscription_status = organization.subscription_status.unwrap_or_else(|| "active".to_string());
    let billing_period_start = organization.billing_period_start.unwrap_or_else(|| Utc::now());
    let billing_period_end = organization.billing_period_end.unwrap_or_else(|| Utc::now().checked_add_months(chrono::Months::new(1)).unwrap_or(Utc::now()));

    let response = SubscriptionResponse {
        id: stripe_subscription_id,
        organization_id: org_id.to_string(),
        plan_id: plan_id,
        status: subscription_status,
        current_period_start: billing_period_start.to_rfc3339(),
        current_period_end: billing_period_end.to_rfc3339(),
        cancel_at_period_end: false, // Mock value
        created_at: organization.created_at.unwrap_or(Utc::now()).to_rfc3339(),
        updated_at: organization.updated_at.unwrap_or(Utc::now()).to_rfc3339(),
        plan: plan_info,
    };

    ApiResponse::success(response)
}

/// Create Subscription API
///
/// Creates a new subscription for an organization
#[post("/<organization_id>/subscription", format = "json", data = "<request>")]
pub async fn create_organization_subscription(
    auth: AuthUser,
    organization_id: String,
    request: Json<CreateSubscriptionRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<SubscriptionResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Check if organization already has a subscription tier
    if organization.subscription_tier_id.is_some() {
        return ApiResponse::error(
            Status::Conflict,
            ErrorCode::Conflict,
            "Organization already has an active subscription"
        );
    }

    // Extract plan details from the plan_id
    let plan_tier = request.plan_id.replace("plan_", "");

    // In a real implementation, you would create a subscription in Stripe
    // For now, we'll return mock data
    let now = Utc::now();
    let period_end = now.checked_add_months(chrono::Months::new(1)).unwrap_or(now);
    let subscription_id = format!("sub_{}", Uuid::new_v4().to_string().replace("-", ""));

    // Get subscription tier ID
    // For now, we'll use a hardcoded value since we can't use sql_query with DbConn
    let subscription_tier_id = match plan_tier.as_str() {
        "starter" => Uuid::parse_str("00000000-0000-0000-0000-000000000001").unwrap_or_else(|_| Uuid::new_v4()),
        "pro" => Uuid::parse_str("00000000-0000-0000-0000-000000000002").unwrap_or_else(|_| Uuid::new_v4()),
        "enterprise" => Uuid::parse_str("00000000-0000-0000-0000-000000000003").unwrap_or_else(|_| Uuid::new_v4()),
        _ => Uuid::new_v4()
    };

    // Update organization with subscription details
    let update = UpdateOrganization {
        name: None,
        slug: None,
        subscription_tier: Some(plan_tier.clone()),
        subscription_status: Some("active".to_string()),
        description: None,
        logo_url: None,
        website: None,
        updated_at: Some(now),
        stripe_customer_id: Some(format!("cus_{}", Uuid::new_v4().to_string().replace("-", ""))),
        stripe_subscription_id: Some(subscription_id.clone()),
        subscription_tier_id: Some(subscription_tier_id),
        subscription_auto_renew: Some(true),
        billing_period_start: Some(now),
        billing_period_end: Some(period_end),
    };

    match OrganizationRepository::update(&mut conn, org_id, &update) {
        Ok(_) => (),
        Err(e) => {
            eprintln!("Error updating organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update organization with subscription information"
            );
        }
    };

    // Construct response
    let plan_info = match plan_tier.as_str() {
        "free" => SubscriptionPlanInfo {
            id: request.plan_id.clone(),
            name: "Free".to_string(),
            description: "Free plan for small projects".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "starter" => SubscriptionPlanInfo {
            id: request.plan_id.clone(),
            name: "Starter".to_string(),
            description: "Starter plan for growing teams".to_string(),
            price: 9.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "pro" => SubscriptionPlanInfo {
            id: request.plan_id.clone(),
            name: "Pro".to_string(),
            description: "Pro plan for professional teams".to_string(),
            price: 29.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "enterprise" => SubscriptionPlanInfo {
            id: request.plan_id.clone(),
            name: "Enterprise".to_string(),
            description: "Enterprise plan for large organizations".to_string(),
            price: 99.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        _ => SubscriptionPlanInfo {
            id: request.plan_id.clone(),
            name: "Unknown".to_string(),
            description: "Unknown plan".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
    };

    let response = SubscriptionResponse {
        id: subscription_id,
        organization_id: org_id.to_string(),
        plan_id: request.plan_id.clone(),
        status: "active".to_string(),
        current_period_start: now.to_rfc3339(),
        current_period_end: period_end.to_rfc3339(),
        cancel_at_period_end: false,
        created_at: now.to_rfc3339(),
        updated_at: now.to_rfc3339(),
        plan: plan_info,
    };

    ApiResponse::success_with_status(response, Status::Created)
}

/// Cancel Subscription API
///
/// Cancels an organization's subscription
#[post("/<organization_id>/subscription/cancel", format = "json", data = "<request>")]
pub async fn cancel_organization_subscription(
    auth: AuthUser,
    organization_id: String,
    request: Json<CancelSubscriptionRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<SubscriptionResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Check if organization has a subscription tier
    let subscription_tier_id = match organization.subscription_tier_id {
        Some(id) => id,
        None => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization does not have an active subscription"
            );
        }
    };

    // In a real implementation, we would fetch the subscription details from the database
    // For now, we'll use a mock subscription ID
    let stripe_subscription_id = format!("sub_mock_{}", subscription_tier_id);

    // In a real implementation, you would cancel the subscription in Stripe
    // For now, we'll update the organization's subscription status
    let cancel_at_period_end = request.cancel_at_period_end.unwrap_or(true);

    let update = UpdateOrganization {
        name: None,
        slug: None,
        subscription_tier: None,
        subscription_status: Some(if cancel_at_period_end { "active".to_string() } else { "canceled".to_string() }),
        description: None,
        logo_url: None,
        website: None,
        updated_at: Some(Utc::now()),
        stripe_customer_id: None,
        stripe_subscription_id: None,
        subscription_tier_id: None,
        subscription_auto_renew: Some(false),
        billing_period_start: None,
        billing_period_end: None,
    };

    match OrganizationRepository::update(&mut conn, org_id, &update) {
        Ok(_) => (),
        Err(e) => {
            eprintln!("Error updating organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update organization with subscription information"
            );
        }
    };

    // Get updated organization details
    let updated_organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding updated organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve updated organization details"
            );
        }
    };

    // Construct response
    // Get subscription tier name
    // For now, we'll use a hardcoded value since we can't use sql_query with DbConn
    let subscription_tier_name = match updated_organization.subscription_tier_id {
        Some(tier_id) => match tier_id.to_string().as_str() {
            "00000000-0000-0000-0000-000000000001" => "starter".to_string(),
            "00000000-0000-0000-0000-000000000002" => "pro".to_string(),
            "00000000-0000-0000-0000-000000000003" => "enterprise".to_string(),
            _ => "free".to_string()
        },
        None => "free".to_string()
    };

    let plan_id = format!("plan_{}", subscription_tier_name);

    let plan_info = match subscription_tier_name.as_str() {
        "free" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Free".to_string(),
            description: "Free plan for small projects".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "starter" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Starter".to_string(),
            description: "Starter plan for growing teams".to_string(),
            price: 9.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "pro" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Pro".to_string(),
            description: "Pro plan for professional teams".to_string(),
            price: 29.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "enterprise" => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Enterprise".to_string(),
            description: "Enterprise plan for large organizations".to_string(),
            price: 99.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        _ => SubscriptionPlanInfo {
            id: plan_id.clone(),
            name: "Unknown".to_string(),
            description: "Unknown plan".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
    };

    let billing_period_start = updated_organization.billing_period_start.unwrap_or_else(|| Utc::now());
    let billing_period_end = updated_organization.billing_period_end.unwrap_or_else(|| Utc::now().checked_add_months(chrono::Months::new(1)).unwrap_or(Utc::now()));

    let response = SubscriptionResponse {
        id: stripe_subscription_id,
        organization_id: org_id.to_string(),
        plan_id: plan_id,
        status: updated_organization.subscription_status.unwrap_or("active".to_string()),
        current_period_start: billing_period_start.to_rfc3339(),
        current_period_end: billing_period_end.to_rfc3339(),
        cancel_at_period_end,
        created_at: updated_organization.created_at.unwrap_or(Utc::now()).to_rfc3339(),
        updated_at: updated_organization.updated_at.unwrap_or(Utc::now()).to_rfc3339(),
        plan: plan_info,
    };

    ApiResponse::success(response)
}

/// Verify Subscription API
///
/// Verifies and updates an organization's subscription after Stripe checkout
#[post("/<organization_id>/subscription/verify", format = "json", data = "<request>")]
pub async fn verify_organization_subscription(
    organization_id: String,
    request: Json<VerifySubscriptionRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<SubscriptionResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Extract plan tier from price_id (e.g., price_starter_monthly -> starter)
    let price_id = &request.price_id;
    let plan_tier = if price_id.contains("starter") {
        "starter"
    } else if price_id.contains("pro") {
        "pro"
    } else if price_id.contains("enterprise") {
        "enterprise"
    } else {
        "free"
    };

    // Parse the current_period_end string to DateTime<Utc>
    let current_period_end = match DateTime::parse_from_rfc3339(&request.current_period_end) {
        Ok(dt) => dt.with_timezone(&Utc),
        Err(e) => {
            eprintln!("Error parsing current_period_end: {}", e);
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid current_period_end format"
            );
        }
    };

    // Set AI credits based on the plan tier
    let ai_credits = match plan_tier {
        "starter" => 100,
        "pro" => 500,
        "enterprise" => 2000,
        _ => 0,
    };

    // Get subscription tier ID
    // For now, we'll use a hardcoded value since we can't use sql_query with DbConn
    let subscription_tier_id = match plan_tier {
        "starter" => Uuid::parse_str("00000000-0000-0000-0000-000000000001").unwrap_or_else(|_| Uuid::new_v4()),
        "pro" => Uuid::parse_str("00000000-0000-0000-0000-000000000002").unwrap_or_else(|_| Uuid::new_v4()),
        "enterprise" => Uuid::parse_str("00000000-0000-0000-0000-000000000003").unwrap_or_else(|_| Uuid::new_v4()),
        _ => Uuid::new_v4()
    };

    // Update organization with subscription details
    let update = UpdateOrganization {
        name: None,
        slug: None,
        subscription_tier: Some(plan_tier.to_string()),
        subscription_status: Some(request.status.clone()),
        description: None,
        logo_url: None,
        website: None,
        updated_at: Some(Utc::now()),
        stripe_customer_id: None, // We don't have this from the checkout session
        stripe_subscription_id: Some(request.stripe_subscription_id.clone()),
        subscription_tier_id: Some(subscription_tier_id),
        subscription_auto_renew: Some(true),
        billing_period_start: Some(Utc::now()),
        billing_period_end: Some(current_period_end),
    };

    let updated_org = match OrganizationRepository::update(&mut conn, org_id, &update) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error updating organization: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update organization with subscription information"
            );
        }
    };

    // Construct plan info
    let plan_info = match plan_tier {
        "free" => SubscriptionPlanInfo {
            id: format!("plan_{}", plan_tier),
            name: "Free".to_string(),
            description: "Free plan for small projects".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "starter" => SubscriptionPlanInfo {
            id: format!("plan_{}", plan_tier),
            name: "Starter".to_string(),
            description: "Starter plan for growing teams".to_string(),
            price: 9.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "pro" => SubscriptionPlanInfo {
            id: format!("plan_{}", plan_tier),
            name: "Pro".to_string(),
            description: "Pro plan for professional teams".to_string(),
            price: 29.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        "enterprise" => SubscriptionPlanInfo {
            id: format!("plan_{}", plan_tier),
            name: "Enterprise".to_string(),
            description: "Enterprise plan for large organizations".to_string(),
            price: 99.99,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
        _ => SubscriptionPlanInfo {
            id: format!("plan_{}", plan_tier),
            name: "Unknown".to_string(),
            description: "Unknown plan".to_string(),
            price: 0.0,
            currency: "USD".to_string(),
            interval: "month".to_string(),
        },
    };

    // Construct response
    let response = SubscriptionResponse {
        id: request.stripe_subscription_id.clone(),
        organization_id: org_id.to_string(),
        plan_id: format!("plan_{}", plan_tier),
        status: request.status.clone(),
        current_period_start: Utc::now().to_rfc3339(),
        current_period_end: current_period_end.to_rfc3339(),
        cancel_at_period_end: false,
        created_at: updated_org.created_at.unwrap_or(Utc::now()).to_rfc3339(),
        updated_at: updated_org.updated_at.unwrap_or(Utc::now()).to_rfc3339(),
        plan: plan_info,
    };

    ApiResponse::success(response)
}