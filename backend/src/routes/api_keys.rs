use rocket::serde::json::Json;
use rocket::http::Status;
use serde_json::json;
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::auth::permissions;
use crate::db::DbConn;
use crate::models::api_key::{CreateApiKeyRequest, ApiKeyResponse, ApiKeyCreatedResponse};
use crate::repositories::api_key_repository::ApiKeyRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::permission_group_repository::PermissionGroupRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

// List API keys for an organization
#[get("/<organization_id>/api-keys")]
pub fn list_api_keys(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<ApiKeyResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get API keys with permission group details
    match ApiKeyRepository::list_api_keys_with_groups(&mut conn, org_id) {
        Ok(api_keys) => ApiResponse::success(api_keys),
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to list API keys: {}", e)
        )
    }
}

// Create a new API key
#[post("/<organization_id>/api-keys", format = "json", data = "<request>")]
pub fn create_api_key(
    auth: AuthUser,
    organization_id: String,
    request: Json<CreateApiKeyRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<ApiKeyCreatedResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Validate the request
    if request.name.trim().is_empty() {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::ValidationError,
            "API key name is required"
        );
    }

    // Get permissions from the request or use defaults
    let permission_keys = match &request.permissions {
        Some(perms) => perms.clone(),
        None => vec!["read".to_string()] // Default to read-only
    };

    // Validate permissions
    if let Err(err) = ApiKeyRepository::validate_permissions(&permission_keys) {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::ValidationError,
            &err.to_string()
        );
    }

    // Convert permissions to JSON
    let permissions = match ApiKeyRepository::permissions_to_json(&permission_keys) {
        Ok(json) => json,
        Err(err) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                &err.to_string()
            );
        }
    };

    // Check if a permission group ID was provided
    let permission_group_id = request.permission_group_id.clone();

    // If a permission group ID was provided, validate it
    if let Some(group_id_str) = &permission_group_id {
        match Uuid::parse_str(group_id_str) {
            Ok(group_id) => {
                // Check if the permission group exists and belongs to the organization
                match PermissionGroupRepository::get_permission_group_by_id(&mut conn, group_id) {
                    Ok(group) => {
                        if group.organization_id != org_id {
                            return ApiResponse::error(
                                Status::BadRequest,
                                ErrorCode::ValidationError,
                                "Permission group does not belong to this organization"
                            );
                        }
                    },
                    Err(_) => {
                        return ApiResponse::error(
                            Status::BadRequest,
                            ErrorCode::ValidationError,
                            "Permission group not found"
                        );
                    }
                }
            },
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::ValidationError,
                    "Invalid permission group ID format"
                );
            }
        }
    }

    // Create the API key
    match ApiKeyRepository::create_api_key(
        &mut conn,
        org_id,
        &request.name,
        permissions,
        request.rate_limit,
        request.rate_limit_period.as_deref(),
        request.expires_at,
        auth.user_id,
        permission_group_id
    ) {
        Ok((api_key, key)) => {
            let permissions = match api_key.permissions.as_object() {
                Some(obj) => obj.keys()
                    .filter(|k| *k != "rate_limit_period") // Filter out internal fields
                    .map(|k| k.to_string())
                    .collect(),
                None => Vec::new(),
            };

            // Extract rate limit period
            let rate_limit_period = match api_key.permissions.get("rate_limit_period") {
                Some(period) => period.as_str().map(|s| s.to_string()),
                None => Some("minute".to_string()), // Default to minute
            };

            // Get permission group name if available
            let (permission_group_id, permission_group_name) = if false {
                // Permission groups are not implemented yet
                (None, None)
            } else {
                (None, None)
            };

            let response = ApiKeyCreatedResponse {
                id: api_key.id.to_string(),
                name: api_key.name,
                key,
                prefix: api_key.prefix,
                permissions,
                rate_limit: api_key.rate_limit,
                rate_limit_period,
                expires_at: api_key.expires_at,
                created_at: api_key.created_at,
                permission_group_id,
                permission_group_name,
            };

            ApiResponse::success_with_message(response, "API key created successfully")
        },
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to create API key: {}", e)
        )
    }
}

// Revoke an API key
#[delete("/<organization_id>/api-keys/<key_id>")]
pub fn revoke_api_key(
    auth: AuthUser,
    organization_id: String,
    key_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let api_key_id = match Uuid::parse_str(&key_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid API key ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the API key to check if it belongs to the organization
    match ApiKeyRepository::get_api_key_by_id(&mut conn, api_key_id) {
        Ok(api_key) => {
            if api_key.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "API key does not belong to this organization"
                );
            }

            // Revoke the API key
            match ApiKeyRepository::revoke_api_key(&mut conn, api_key_id) {
                Ok(_) => ApiResponse::success_with_message(
                    json!({"id": api_key_id.to_string()}),
                    "API key revoked successfully"
                ),
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to revoke API key: {}", e)
                )
            }
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("API key not found: {}", e)
        )
    }
}

// Get API key usage statistics
#[get("/<organization_id>/api-keys/<key_id>/usage?<days>")]
pub fn get_api_key_usage(
    auth: AuthUser,
    organization_id: String,
    key_id: String,
    days: Option<i32>,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let api_key_id = match Uuid::parse_str(&key_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid API key ID format"
            );
        }
    };

    // Default to 30 days if not specified
    let days_value = days.unwrap_or(30);

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the API key to check if it belongs to the organization
    match ApiKeyRepository::get_api_key_by_id(&mut conn, api_key_id) {
        Ok(api_key) => {
            if api_key.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "API key does not belong to this organization"
                );
            }

            // Get API key usage
            match ApiKeyRepository::get_api_key_usage(&mut conn, api_key_id, days_value) {
                Ok(usage_count) => ApiResponse::success(
                    json!({
                        "id": api_key_id.to_string(),
                        "usage_count": usage_count,
                        "days": days_value
                    })
                ),
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to get API key usage: {}", e)
                )
            }
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("API key not found: {}", e)
        )
    }
}

// Get API key usage statistics by endpoint
#[get("/<organization_id>/api-keys/<key_id>/usage/endpoints?<days>")]
pub fn get_api_key_usage_by_endpoint(
    auth: AuthUser,
    organization_id: String,
    key_id: String,
    days: Option<i32>,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let api_key_id = match Uuid::parse_str(&key_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid API key ID format"
            );
        }
    };

    // Default to 30 days if not specified
    let days_value = days.unwrap_or(30);

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the API key to check if it belongs to the organization
    match ApiKeyRepository::get_api_key_by_id(&mut conn, api_key_id) {
        Ok(api_key) => {
            if api_key.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "API key does not belong to this organization"
                );
            }

            // Get API key usage by endpoint
            match ApiKeyRepository::get_api_key_usage_by_endpoint(&mut conn, api_key_id, days_value) {
                Ok(endpoint_usage) => {
                    let usage_data = endpoint_usage.into_iter()
                        .map(|(endpoint, count)| {
                            json!({
                                "endpoint": endpoint,
                                "count": count
                            })
                        })
                        .collect::<Vec<_>>();

                    ApiResponse::success(
                        json!({
                            "id": api_key_id.to_string(),
                            "days": days_value,
                            "endpoints": usage_data
                        })
                    )
                },
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to get API key usage by endpoint: {}", e)
                )
            }
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("API key not found: {}", e)
        )
    }
}

// Get all available permissions
#[get("/permissions")]
pub fn get_permissions(
    auth: AuthUser,
) -> Json<ApiResponse<serde_json::Value>> {
    // Get all permissions
    let all_permissions = permissions::get_all_permissions();

    // Convert permissions to a JSON response
    let mut permissions_json = Vec::new();
    for (key, permission) in all_permissions {
        let permission_type = match &permission.permission_type {
            permissions::PermissionType::Global => "global",
            permissions::PermissionType::Resource(resource) => "resource",
        };

        let action = match &permission.action {
            permissions::PermissionAction::Read => "read",
            permissions::PermissionAction::Write => "write",
            permissions::PermissionAction::Admin => "admin",
            permissions::PermissionAction::Custom(custom) => "custom",
        };

        let resource = match &permission.permission_type {
            permissions::PermissionType::Resource(resource) => Some(resource.as_str()),
            _ => None,
        };

        permissions_json.push(json!({
            "key": key,
            "type": permission_type,
            "action": action,
            "resource": resource,
            "description": permission.description,
        }));
    }

    ApiResponse::success(json!({
        "permissions": permissions_json
    }))
}

// Get API key usage statistics by day
#[get("/<organization_id>/api-keys/<key_id>/usage/daily?<days>")]
pub fn get_api_key_usage_by_day(
    auth: AuthUser,
    organization_id: String,
    key_id: String,
    days: Option<i32>,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let api_key_id = match Uuid::parse_str(&key_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid API key ID format"
            );
        }
    };

    // Default to 30 days if not specified
    let days_value = days.unwrap_or(30);

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get the API key to check if it belongs to the organization
    match ApiKeyRepository::get_api_key_by_id(&mut conn, api_key_id) {
        Ok(api_key) => {
            if api_key.organization_id != org_id {
                return ApiResponse::error(
                    Status::Forbidden,
                    ErrorCode::Forbidden,
                    "API key does not belong to this organization"
                );
            }

            // Get API key usage by day
            match ApiKeyRepository::get_api_key_usage_by_day(&mut conn, api_key_id, days_value) {
                Ok(daily_usage) => {
                    let usage_data = daily_usage.into_iter()
                        .map(|(date, count)| {
                            json!({
                                "date": date,
                                "count": count
                            })
                        })
                        .collect::<Vec<_>>();

                    ApiResponse::success(
                        json!({
                            "id": api_key_id.to_string(),
                            "days": days_value,
                            "daily": usage_data
                        })
                    )
                },
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to get API key usage by day: {}", e)
                )
            }
        },
        Err(e) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("API key not found: {}", e)
        )
    }
}
