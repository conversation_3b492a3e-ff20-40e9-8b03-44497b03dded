use rocket::serde::json::Json;
use rocket::http::Status;
use serde_json::json;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::Deserialize;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::permission_audit_log::PermissionAuditLogResponse;
use crate::repositories::permission_audit_log_repository::PermissionAuditLogRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct AuditLogQueryParams {
    pub limit: Option<i64>,
    pub offset: Option<i64>,
    pub start_date: Option<DateTime<Utc>>,
    pub end_date: Option<DateTime<Utc>>,
    pub permission_key: Option<String>,
    pub granted: Option<bool>,
}

// Get audit logs for an organization
#[get("/<organization_id>/audit-logs?<limit>&<offset>&<permission_key>&<granted>")]
pub fn get_audit_logs(
    auth: AuthUser,
    organization_id: String,
    limit: Option<i64>,
    offset: Option<i64>,
    permission_key: Option<String>,
    granted: Option<bool>,
    mut conn: DbConn
) -> Json<ApiResponse<serde_json::Value>> {
    // For now, we'll ignore date filtering since FromForm is not implemented for DateTime<Utc>
    let start_date: Option<DateTime<Utc>> = None;
    let end_date: Option<DateTime<Utc>> = None;
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Set default limit and offset
    let limit_value = limit.unwrap_or(100);
    let offset_value = offset.unwrap_or(0);

    // Get audit logs based on filters
    let logs = if let Some(key) = permission_key {
        // Filter by permission key
        PermissionAuditLogRepository::get_logs_by_permission(&mut conn, org_id, &key, limit_value, offset_value)
    } else if let Some(is_granted) = granted {
        if !is_granted {
            // Get denied logs
            PermissionAuditLogRepository::get_denied_logs(&mut conn, org_id, limit_value, offset_value)
        } else {
            // Get all logs (we'll filter by granted later)
            PermissionAuditLogRepository::get_logs_by_organization(&mut conn, org_id, limit_value, offset_value)
        }
    } else if let (Some(start), Some(end)) = (start_date, end_date) {
        // Filter by date range
        PermissionAuditLogRepository::get_logs_by_date_range(&mut conn, org_id, start, end, limit_value, offset_value)
    } else {
        // Get all logs
        PermissionAuditLogRepository::get_logs_by_organization(&mut conn, org_id, limit_value, offset_value)
    };

    match logs {
        Ok(logs) => {
            // Filter by granted if needed
            let filtered_logs = if let Some(is_granted) = granted {
                logs.into_iter()
                    .filter(|log| log.granted == is_granted)
                    .collect::<Vec<_>>()
            } else {
                logs
            };

            // Convert to response format
            let response_logs: Vec<PermissionAuditLogResponse> = filtered_logs.into_iter()
                .map(|log| log.into())
                .collect();

            // Get total count
            let total_count = match PermissionAuditLogRepository::count_logs_by_organization(&mut conn, org_id) {
                Ok(count) => count,
                Err(_) => 0, // Default to 0 if count fails
            };

            ApiResponse::success(json!({
                "logs": response_logs,
                "total": total_count,
                "limit": limit_value,
                "offset": offset_value
            }))
        },
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to get audit logs: {}", e)
        )
    }
}

// Get audit logs for a specific API key
#[get("/<organization_id>/api-keys/<key_id>/audit-logs?<limit>&<offset>")]
pub fn get_api_key_audit_logs(
    auth: AuthUser,
    organization_id: String,
    key_id: String,
    limit: Option<i64>,
    offset: Option<i64>,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<PermissionAuditLogResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    let api_key_id = match Uuid::parse_str(&key_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid API key ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Set default limit and offset
    let limit_value = limit.unwrap_or(100);
    let offset_value = offset.unwrap_or(0);

    // Get audit logs for the API key
    match PermissionAuditLogRepository::get_logs_by_api_key(&mut conn, api_key_id, limit_value, offset_value) {
        Ok(logs) => {
            let response_logs: Vec<PermissionAuditLogResponse> = logs.into_iter()
                .map(|log| log.into())
                .collect();

            ApiResponse::success(response_logs)
        },
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to get API key audit logs: {}", e)
        )
    }
}
