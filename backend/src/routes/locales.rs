use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::locale::{Locale, NewLocale};
use crate::repositories::locale_repository::LocaleRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct CreateLocaleRequest {
    pub code: String,
    pub name: String,
    pub native_name: String,
    pub text_direction: Option<String>,
}

#[derive(Debug, Serialize)]
pub struct LocaleResponse {
    pub id: Uuid,
    pub code: String,
    pub name: String,
    pub native_name: String,
    pub text_direction: Option<String>,
}

impl From<Locale> for LocaleResponse {
    fn from(locale: Locale) -> Self {
        Self {
            id: locale.id,
            code: locale.code,
            name: locale.name,
            native_name: locale.native_name,
            text_direction: locale.text_direction,
        }
    }
}

#[get("/")]
pub fn list_locales(mut conn: DbConn) -> Json<ApiResponse<Vec<LocaleResponse>>> {
    match LocaleRepository::list_all(&mut conn) {
        Ok(locales) => {
            let response = locales.into_iter()
                .map(LocaleResponse::from)
                .collect();

            ApiResponse::success(response)
        },
        Err(e) => {
            eprintln!("Error listing locales: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve locales"
            )
        }
    }
}

#[get("/<code>")]
pub fn get_locale_by_code(code: String, mut conn: DbConn) -> Json<ApiResponse<LocaleResponse>> {
    match LocaleRepository::find_by_code(&mut conn, &code) {
        Ok(locale) => ApiResponse::success(locale.into()),
        Err(_) => ApiResponse::error(
            Status::NotFound,
            ErrorCode::NotFound,
            &format!("Locale with code '{}' not found", code)
        )
    }
}

#[post("/", format = "json", data = "<request>")]
pub fn create_locale(
    _auth: AuthUser,
    request: Json<CreateLocaleRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<LocaleResponse>> {
    // Only allow admins to create new locales
    // In a real application, you would check if the user is an admin

    // Check if locale with the same code already exists
    if let Ok(_) = LocaleRepository::find_by_code(&mut conn, &request.code) {
        return ApiResponse::error(
            Status::Conflict,
            ErrorCode::Conflict,
            &format!("Locale with code '{}' already exists", request.code)
        );
    }

    let new_locale = NewLocale {
        code: request.code.clone(),
        name: request.name.clone(),
        native_name: request.native_name.clone(),
        text_direction: request.text_direction.clone(),
        is_active: Some(true),
    };

    match LocaleRepository::create(&mut conn, &new_locale) {
        Ok(locale) => ApiResponse::success_with_message(
            locale.into(),
            "Locale created successfully"
        ),
        Err(e) => {
            eprintln!("Error creating locale: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to create locale"
            )
        }
    }
}
