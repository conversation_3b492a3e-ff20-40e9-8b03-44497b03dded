use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
// use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::{DateTime, Utc};

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
// use crate::models::subscription_event::{SubscriptionEventResponse, SubscriptionHistoryResponse, AICreditUsageResponse};
use crate::repositories::organization_repository::OrganizationRepository;
// use crate::repositories::subscription_event_repository::SubscriptionEventRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

/*
/// Get Subscription History API
///
/// Returns the subscription event history for an organization
#[get("/<organization_id>/subscription/history?<limit>")]
pub fn get_subscription_history(
    auth: AuthUser,
    organization_id: String,
    limit: Option<i64>,
    mut conn: DbConn
) -> <PERSON><PERSON><ApiResponse<SubscriptionHistoryResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Get subscription events
    let events = match SubscriptionEventRepository::find_by_organization(&mut conn, org_id, limit.unwrap_or(10)) {
        Ok(events) => events,
        Err(e) => {
            eprintln!("Error finding subscription events: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve subscription history"
            );
        }
    };

    // Transform events to response format
    let event_responses: Vec<SubscriptionEventResponse> = events.into_iter()
        .map(|event| SubscriptionEventResponse {
            id: event.id.to_string(),
            organization_id: event.organization_id.to_string(),
            event_type: event.event_type,
            details: event.details,
            created_at: event.created_at.to_rfc3339(),
        })
        .collect();

    let response = SubscriptionHistoryResponse {
        events: event_responses,
    };

    ApiResponse::success(response)
}
*/

/*
/// Get AI Credit Usage API
///
/// Returns the AI credit usage for an organization
#[get("/<organization_id>/ai-credits")]
pub fn get_ai_credit_usage(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<AICreditUsageResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get organization details
    let organization = match OrganizationRepository::find_by_id(&mut conn, org_id) {
        Ok(org) => org,
        Err(e) => {
            eprintln!("Error finding organization: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Organization not found"
            );
        }
    };

    // Get AI credits from organization
    let total = organization.ai_credits_monthly_allowance.unwrap_or(0);
    let remaining = organization.ai_credits_remaining.unwrap_or(0);
    let used = total - remaining;
    let reset_date = organization.ai_credits_reset_date.unwrap_or(Utc::now()).to_rfc3339();

    let response = AICreditUsageResponse {
        total,
        used,
        remaining,
        reset_date,
    };

    ApiResponse::success(response)
}
*/
