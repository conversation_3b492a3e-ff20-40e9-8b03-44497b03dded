use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::Utc;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::translation::{TranslationKey, NewTranslationKey, Translation, NewTranslation, UpdateTranslation, TranslationHistory, NewTranslationHistory};
use crate::repositories::translation_repository::TranslationRepository;
use crate::repositories::project_repository::ProjectRepository;
use crate::repositories::locale_repository::LocaleRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct CreateTranslationKeyRequest {
    pub resource_id: Option<String>,
    pub project_id: Option<String>,
    pub key_name: String,
    pub description: Option<String>,
    pub context: Option<String>,
    pub is_plural: Option<bool>,
    pub max_length: Option<i32>,
    pub screenshot_url: Option<String>,
}

#[derive(Debug, Deserialize)]
pub struct CreateTranslationRequest {
    pub key_id: String,
    pub locale_id: String,
    pub content: String,
}

#[derive(Debug, Deserialize)]
pub struct UpdateTranslationRequest {
    pub content: String,
    pub is_reviewed: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct TranslationKeyResponse {
    pub id: Uuid,
    pub resource_id: Option<Uuid>,
    pub key_name: String,
    pub description: Option<String>,
    pub image_url: Option<String>,
    pub tags: Option<Vec<String>>,
    pub project_id: Uuid,
}

#[derive(Debug, Serialize)]
pub struct TranslationResponse {
    pub id: Uuid,
    pub key_id: Uuid,
    pub locale_id: Uuid,
    pub content: String,
    pub is_fuzzy: Option<bool>,
    pub is_reviewed: Option<bool>,
}

#[derive(Debug, Serialize)]
pub struct TranslationHistoryResponse {
    pub id: Uuid,
    pub translation_id: Uuid,
    pub content: String,
    pub action: String,
    pub created_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl From<TranslationKey> for TranslationKeyResponse {
    fn from(key: TranslationKey) -> Self {
        Self {
            id: key.id,
            resource_id: key.resource_id,
            key_name: key.key_name,
            description: key.description,
            image_url: key.screenshot_url,
            tags: None, // No longer in the model
            project_id: key.project_id.unwrap_or(Uuid::nil()),
        }
    }
}

impl From<Translation> for TranslationResponse {
    fn from(translation: Translation) -> Self {
        Self {
            id: translation.id,
            key_id: translation.key_id,
            locale_id: translation.locale_id,
            content: translation.content,
            is_fuzzy: translation.is_fuzzy,
            is_reviewed: translation.is_reviewed,
        }
    }
}

impl From<TranslationHistory> for TranslationHistoryResponse {
    fn from(history: TranslationHistory) -> Self {
        Self {
            id: history.id,
            translation_id: history.translation_id,
            content: history.content,
            action: history.action,
            created_at: history.created_at,
        }
    }
}

#[get("/keys?<resource_id>&<project_id>")]
pub fn list_translation_keys(
    _auth: AuthUser,
    resource_id: Option<String>,
    project_id: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<TranslationKeyResponse>>> {
    // Either resource_id or project_id must be provided
    if resource_id.is_none() && project_id.is_none() {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Either resource_id or project_id must be provided"
        );
    }

    let keys = if let Some(resource_id_str) = resource_id {
        // Fetch keys by resource ID
        let resource_uuid = match Uuid::parse_str(&resource_id_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::BadRequest,
                    "Invalid resource ID format"
                );
            }
        };

        // Check if user has access to the resource
        let _resource = match ProjectRepository::find_resource_by_id(&mut conn, resource_uuid) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Resource with ID '{}' not found", resource_id_str)
                );
            }
        };

        // TODO: Check if user has access to the project

        match TranslationRepository::list_keys_by_resource(&mut conn, resource_uuid) {
            Ok(keys) => keys,
            Err(e) => {
                eprintln!("Error listing translation keys by resource: {:?}", e);
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to retrieve translation keys"
                );
            }
        }
    } else if let Some(project_id_str) = project_id {
        // Fetch keys by project ID
        let project_uuid = match Uuid::parse_str(&project_id_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::BadRequest,
                    "Invalid project ID format"
                );
            }
        };

        // Check if project exists
        let _project = match ProjectRepository::find_by_id(&mut conn, project_uuid) {
            Ok(project) => project,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Project with ID '{}' not found", project_id_str)
                );
            }
        };

        // TODO: Check if user has access to the project

        // Get all keys for the project
        eprintln!("Querying translation keys for project_id: {}", project_uuid);

        // Try to get keys directly by project_id first
        let direct_keys = match TranslationRepository::list_keys_by_project(&mut conn, project_uuid) {
            Ok(keys) => keys,
            Err(e) => {
                eprintln!("Error listing translation keys by project_id: {:?}", e);
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to retrieve translation keys by project ID"
                );
            }
        };

        if !direct_keys.is_empty() {
            eprintln!("Successfully retrieved {} translation keys directly by project_id", direct_keys.len());
            direct_keys
        } else {
            // Fallback to getting keys through resources
            eprintln!("No keys found directly by project_id, falling back to resources");
            match TranslationRepository::list_keys_by_resource_project(&mut conn, project_uuid) {
                Ok(keys) => keys,
                Err(e) => {
                    eprintln!("Error listing translation keys by resource project: {:?}", e);
                    return ApiResponse::error(
                        Status::InternalServerError,
                        ErrorCode::InternalError,
                        "Failed to retrieve translation keys through resources"
                    );
                }
            }
        }
    } else {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Either resource_id or project_id must be provided"
        );
    };

    let response = keys.into_iter()
        .map(TranslationKeyResponse::from)
        .collect();

    ApiResponse::success(response)
}

#[post("/keys", format = "json", data = "<request>")]
pub fn create_translation_key(
    auth: AuthUser,
    request: Json<CreateTranslationKeyRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<TranslationKeyResponse>> {
    // Either resource_id or project_id must be provided
    if request.resource_id.is_none() && request.project_id.is_none() {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "Either resource_id or project_id must be provided"
        );
    }

    let resource_id = if let Some(resource_id_str) = &request.resource_id {
        let resource_uuid = match Uuid::parse_str(resource_id_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::BadRequest,
                    "Invalid resource ID format"
                );
            }
        };

        // Check if user has access to the resource
        let _resource = match ProjectRepository::find_resource_by_id(&mut conn, resource_uuid) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Resource with ID '{}' not found", resource_id_str)
                );
            }
        };

        Some(resource_uuid)
    } else {
        None
    };

    let project_id = if let Some(project_id_str) = &request.project_id {
        let project_uuid = match Uuid::parse_str(project_id_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::BadRequest,
                    "Invalid project ID format"
                );
            }
        };

        // Check if project exists
        let _project = match ProjectRepository::find_by_id(&mut conn, project_uuid) {
            Ok(project) => project,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Project with ID '{}' not found", project_id_str)
                );
            }
        };

        // TODO: Check if user has access to the project

        Some(project_uuid)
    } else if let Some(resource_id) = resource_id {
        // If only resource_id is provided, get the project_id from the resource
        let resource = match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    "Resource not found"
                );
            }
        };

        Some(resource.project_id)
    } else {
        None
    };

    // Create the new key
    let new_key = NewTranslationKey {
        resource_id,
        key_name: request.key_name.clone(),
        description: request.description.clone(),
        context: request.context.clone(),
        is_plural: request.is_plural,
        max_length: request.max_length,
        screenshot_url: request.screenshot_url.clone(),
        created_by: Some(auth.user_id),
        project_id,
    };

    let key = match TranslationRepository::create_key(&mut conn, &new_key) {
        Ok(key) => key,
        Err(e) => {
            eprintln!("Error creating translation key: {:?}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to create translation key"
            );
        }
    };

    ApiResponse::success_with_message(key.into(), "Translation key created successfully")
}

#[get("/keys/<id>", rank = 1)]
pub fn get_translation_key(
    _auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Json<ApiResponse<TranslationKeyResponse>> {
    let key_id = match Uuid::parse_str(&id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid translation key ID format"
            );
        }
    };

    // Get the translation key
    let key = match TranslationRepository::find_key_by_id(&mut conn, key_id) {
        Ok(key) => key,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Translation key with ID '{}' not found", id)
            );
        }
    };

    // Check access based on whether the key is associated with a resource or directly with a project
    let project_id = if let Some(resource_id) = key.resource_id {
        // Get the resource to check access
        let resource = match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    "Resource not found"
                );
            }
        };

        resource.project_id
    } else {
        // Key is directly associated with a project
        key.project_id.unwrap_or_else(|| {
            // This should not happen, but we need to handle it
            eprintln!("Translation key {} has no project_id or resource_id", key.id);
            Uuid::nil()
        })
    };

    // Get the project to check access
    let _project = match ProjectRepository::find_by_id(&mut conn, project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Project not found"
            );
        }
    };

    // Check if user has access to the organization
    // TODO: Implement proper access control

    ApiResponse::success(key.into())
}

#[post("/", format = "json", data = "<request>")]
pub fn create_translation(
    auth: AuthUser,
    request: Json<CreateTranslationRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<TranslationResponse>> {
    let key_id = match Uuid::parse_str(&request.key_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid key ID format"
            );
        }
    };

    let locale_id = match Uuid::parse_str(&request.locale_id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid locale ID format"
            );
        }
    };

    // Check if key exists
    let key = match TranslationRepository::find_key_by_id(&mut conn, key_id) {
        Ok(key) => key,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Translation key with ID '{}' not found", request.key_id)
            );
        }
    };

    // Check if locale exists
    let _locale = match LocaleRepository::find_by_id(&mut conn, locale_id) {
        Ok(locale) => locale,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Locale with ID '{}' not found", request.locale_id)
            );
        }
    };

    // Check access based on whether the key is associated with a resource or directly with a project
    let project_id = if let Some(resource_id) = key.resource_id {
        // Get the resource to check access
        let resource = match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    "Resource not found"
                );
            }
        };

        resource.project_id
    } else {
        // Key is directly associated with a project
        key.project_id.unwrap_or_else(|| {
            // This should not happen, but we need to handle it
            eprintln!("Translation key {} has no project_id or resource_id", key.id);
            Uuid::nil()
        })
    };

    // Get the project to check access
    let _project = match ProjectRepository::find_by_id(&mut conn, project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Project not found"
            );
        }
    };

    // Check if user has access to the project
    // TODO: Implement proper access control

    // Create the translation
    let new_translation = NewTranslation {
        key_id,
        locale_id,
        content: request.content.clone(),
        is_fuzzy: Some(false),
        is_reviewed: Some(false),
        reviewed_by: None,
        created_by: Some(auth.user_id),
    };

    let translation = match TranslationRepository::create_translation(&mut conn, &new_translation) {
        Ok(translation) => translation,
        Err(e) => {
            eprintln!("Error creating translation: {:?}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to create translation"
            );
        }
    };

    // Create history entry
    let new_history = NewTranslationHistory {
        translation_id: translation.id,
        content: translation.content.clone(),
        action: "create".to_string(),
        performed_by: Some(auth.user_id),
    };

    match TranslationRepository::add_history(&mut conn, &new_history) {
        Ok(_) => {},
        Err(e) => {
            eprintln!("Error creating translation history: {:?}", e);
            // We don't fail the request if history creation fails
            // Just log the error and continue
        }
    };

    ApiResponse::success_with_message(translation.into(), "Translation created successfully")
}

#[get("/?<key_id>&<locale_id>")]
pub fn list_translations(
    _auth: AuthUser,
    key_id: Option<String>,
    locale_id: Option<String>,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<TranslationResponse>>> {
    // At least one filter must be provided
    if key_id.is_none() && locale_id.is_none() {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "At least one filter (key_id or locale_id) must be provided"
        );
    }

    let translations = if let Some(key_id_str) = key_id {
        // List translations by key
        let key_uuid = match Uuid::parse_str(&key_id_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::BadRequest,
                    "Invalid key ID format"
                );
            }
        };

        // Check if key exists
        let key = match TranslationRepository::find_key_by_id(&mut conn, key_uuid) {
            Ok(key) => key,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Translation key with ID '{}' not found", key_id_str)
                );
            }
        };

        // Get the resource to check access if resource_id exists
        if let Some(resource_id) = key.resource_id {
            let resource = match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
                Ok(resource) => resource,
                Err(_) => {
                    return ApiResponse::error(
                        Status::NotFound,
                        ErrorCode::NotFound,
                        "Resource not found"
                    );
                }
            };

            // Get the project to check access
            let _project = match ProjectRepository::find_by_id(&mut conn, resource.project_id) {
                Ok(project) => project,
                Err(_) => {
                    return ApiResponse::error(
                        Status::NotFound,
                        ErrorCode::NotFound,
                        "Project not found"
                    );
                }
            };

            // Check if user has access to the project
            // TODO: Implement proper access control
        }

        // Get translations for the key
        match TranslationRepository::list_translations_by_key(&mut conn, key_uuid) {
            Ok(translations) => translations,
            Err(e) => {
                eprintln!("Error listing translations by key: {:?}", e);
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to retrieve translations"
                );
            }
        }
    } else if let Some(locale_id_str) = locale_id {
        // List translations by locale
        let locale_uuid = match Uuid::parse_str(&locale_id_str) {
            Ok(uuid) => uuid,
            Err(_) => {
                return ApiResponse::error(
                    Status::BadRequest,
                    ErrorCode::BadRequest,
                    "Invalid locale ID format"
                );
            }
        };

        // Check if locale exists
        match LocaleRepository::find_by_id(&mut conn, locale_uuid) {
            Ok(_) => {},
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    &format!("Locale with ID '{}' not found", locale_id_str)
                );
            }
        };

        // Get all key IDs for this locale (empty array means no filtering by key)
        let key_ids: Vec<Uuid> = vec![];
        match TranslationRepository::list_translations_by_locale(&mut conn, locale_uuid, &key_ids) {
            Ok(translations) => translations,
            Err(e) => {
                eprintln!("Error listing translations by locale: {:?}", e);
                return ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    "Failed to retrieve translations"
                );
            }
        }
    } else {
        return ApiResponse::error(
            Status::BadRequest,
            ErrorCode::BadRequest,
            "At least one filter (key_id or locale_id) must be provided"
        );
    };

    let response = translations.into_iter()
        .map(TranslationResponse::from)
        .collect();

    ApiResponse::success(response)
}

#[get("/<id>")]
pub fn get_translation(
    _auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Json<ApiResponse<TranslationResponse>> {
    let translation_id = match Uuid::parse_str(&id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid translation ID format"
            );
        }
    };

    // Get the translation
    let translation = match TranslationRepository::find_translation_by_id(&mut conn, translation_id) {
        Ok(translation) => translation,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Translation with ID '{}' not found", id)
            );
        }
    };

    // Get the key to check access
    let key = match TranslationRepository::find_key_by_id(&mut conn, translation.key_id) {
        Ok(key) => key,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Translation key not found"
            );
        }
    };

    // Get the resource to check access if resource_id exists
    let resource = if let Some(resource_id) = key.resource_id {
        match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    "Resource not found"
                );
            }
        }
    } else {
        // If there's no resource_id, we'll check project access directly from the key's project_id
        // Create a dummy resource with the project_id
        crate::models::project::Resource {
            id: Uuid::nil(), // Use a nil UUID as this is just a placeholder
            project_id: key.project_id.unwrap_or(Uuid::nil()),
            name: String::new(),
            type_: String::new(),
            path: None,
            description: None,
            created_by: None,
            created_at: None,
            updated_at: None,
            deleted_at: None,
        }
    };

    // Get the project to check access
    let _project = match ProjectRepository::find_by_id(&mut conn, resource.project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Project not found"
            );
        }
    };

    // Check if user has access to the project
    // TODO: Implement proper access control

    ApiResponse::success(translation.into())
}

#[put("/<id>", format = "json", data = "<request>")]
pub fn update_translation(
    auth: AuthUser,
    id: String,
    request: Json<UpdateTranslationRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<TranslationResponse>> {
    let translation_id = match Uuid::parse_str(&id) {
        Ok(uuid) => uuid,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid translation ID format"
            );
        }
    };

    // Get the translation
    let translation = match TranslationRepository::find_translation_by_id(&mut conn, translation_id) {
        Ok(translation) => translation,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Translation with ID '{}' not found", id)
            );
        }
    };

    // Get the key to check access
    let key = match TranslationRepository::find_key_by_id(&mut conn, translation.key_id) {
        Ok(key) => key,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Translation key not found"
            );
        }
    };

    // Get the resource to check access if resource_id exists
    let resource = if let Some(resource_id) = key.resource_id {
        match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    "Resource not found"
                );
            }
        }
    } else {
        // If there's no resource_id, we'll check project access directly from the key's project_id
        // Create a dummy resource with the project_id
        crate::models::project::Resource {
            id: Uuid::nil(), // Use a nil UUID as this is just a placeholder
            project_id: key.project_id.unwrap_or(Uuid::nil()),
            name: String::new(),
            type_: String::new(),
            path: None,
            description: None,
            created_by: None,
            created_at: None,
            updated_at: None,
            deleted_at: None,
        }
    };

    // Get the project to check access
    let _project = match ProjectRepository::find_by_id(&mut conn, resource.project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Project not found"
            );
        }
    };

    // Check if user has access to the project
    // TODO: Implement proper access control

    // Create history entry before updating
    let new_history = NewTranslationHistory {
        translation_id,
        content: translation.content.clone(),
        action: "update".to_string(),
        performed_by: Some(auth.user_id),
    };

    match TranslationRepository::add_history(&mut conn, &new_history) {
        Ok(_) => {},
        Err(e) => {
            eprintln!("Error creating translation history: {:?}", e);
            // We don't fail the request if history creation fails
            // Just log the error and continue
        }
    };

    // Update the translation
    let update = UpdateTranslation {
        content: Some(request.content.clone()),
        is_fuzzy: None,
        is_reviewed: request.is_reviewed,
        reviewed_by: request.is_reviewed.map(|_| auth.user_id),
        reviewed_at: request.is_reviewed.map(|_| Utc::now()),
        updated_at: Some(Utc::now()),
    };

    let updated_translation = match TranslationRepository::update_translation(&mut conn, translation_id, &update) {
        Ok(translation) => translation,
        Err(e) => {
            eprintln!("Error updating translation: {:?}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update translation"
            );
        }
    };

    ApiResponse::success_with_message(updated_translation.into(), "Translation updated successfully")
}

#[get("/<id>/history", rank = 2)]
pub fn get_translation_history(
    _auth: AuthUser,
    id: String,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<TranslationHistoryResponse>>> {
    let translation_id = match Uuid::parse_str(&id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid translation ID format"
            );
        }
    };

    // Get the translation
    let translation = match TranslationRepository::find_translation_by_id(&mut conn, translation_id) {
        Ok(translation) => translation,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                &format!("Translation with ID '{}' not found", id)
            );
        }
    };

    // Get the key to check access
    let key = match TranslationRepository::find_key_by_id(&mut conn, translation.key_id) {
        Ok(key) => key,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Translation key not found"
            );
        }
    };

    // Get the resource to check access if resource_id exists
    let resource = if let Some(resource_id) = key.resource_id {
        match ProjectRepository::find_resource_by_id(&mut conn, resource_id) {
            Ok(resource) => resource,
            Err(_) => {
                return ApiResponse::error(
                    Status::NotFound,
                    ErrorCode::NotFound,
                    "Resource not found"
                );
            }
        }
    } else {
        // If there's no resource_id, we'll check project access directly from the key's project_id
        // Create a dummy resource with the project_id
        crate::models::project::Resource {
            id: Uuid::nil(), // Use a nil UUID as this is just a placeholder
            project_id: key.project_id.unwrap_or(Uuid::nil()),
            name: String::new(),
            type_: String::new(),
            path: None,
            description: None,
            created_by: None,
            created_at: None,
            updated_at: None,
            deleted_at: None,
        }
    };

    // Get the project to check access
    let _project = match ProjectRepository::find_by_id(&mut conn, resource.project_id) {
        Ok(project) => project,
        Err(_) => {
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Project not found"
            );
        }
    };

    // Check if user has access to the project
    // TODO: Implement proper access control

    // Get history
    let history = match TranslationRepository::list_history_by_translation(&mut conn, translation_id) {
        Ok(history) => history,
        Err(e) => {
            eprintln!("Error retrieving translation history: {}", e);
            return ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve translation history"
            );
        }
    };

    let response = history.into_iter()
        .map(TranslationHistoryResponse::from)
        .collect();

    ApiResponse::success(response)
}
