use rocket::serde::json::<PERSON><PERSON>;
use serde::{Serialize};

use crate::auth::guards::{AuthUser, RequireRole};
use crate::utils::response::ApiResponse;

#[derive(Debug, Serialize)]
pub struct AdminResponse {
    pub message: String,
}

#[get("/dashboard")]
pub fn admin_dashboard(
    _auth: AuthUser,
    _admin: RequireRole<AuthUser>
) -> <PERSON><PERSON><ApiResponse<AdminResponse>> {
    ApiResponse::success(AdminResponse {
        message: "Welcome to the admin dashboard!".to_string(),
    })
}

#[get("/users")]
pub fn admin_users(
    _auth: AuthUser,
    _admin: RequireRole<AuthUser>
) -> Json<ApiResponse<AdminResponse>> {
    ApiResponse::success(AdminResponse {
        message: "Here is the list of users.".to_string(),
    })
}
