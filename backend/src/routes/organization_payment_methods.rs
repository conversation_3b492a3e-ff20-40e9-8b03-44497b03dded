use rocket::serde::json::Json;
use rocket::http::Status;
use rocket::State;
use serde::{Deserialize, Serialize};
use uuid::Uuid;
use chrono::Utc;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::payment::{NewPaymentMethod, PaymentMethodResponse, UpdatePaymentMethod};
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::payment_repository::PaymentRepository;
use crate::payment::stripe::StripeService;
use crate::utils::response::{ApiResponse, ErrorCode};

#[derive(Debug, Deserialize)]
pub struct AddPaymentMethodRequest {
    pub payment_method_id: String,
    pub set_as_default: Option<bool>,
}

#[derive(Debug, Deserialize)]
pub struct SetDefaultPaymentMethodRequest {
    pub is_default: bool,
}

/// List Payment Methods API
///
/// Returns a list of payment methods for an organization
#[get("/<organization_id>/payment-methods")]
pub fn list_payment_methods(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<Vec<PaymentMethodResponse>>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // List payment methods
    match PaymentRepository::list_by_organization(&mut conn, org_id) {
        Ok(payment_methods) => {
            let response = payment_methods.into_iter()
                .map(PaymentMethodResponse::from)
                .collect();

            ApiResponse::success(response)
        },
        Err(e) => {
            eprintln!("Error listing payment methods: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to retrieve payment methods"
            )
        }
    }
}

/// Add Payment Method API
///
/// Adds a new payment method to an organization
#[post("/<organization_id>/payment-methods", format = "json", data = "<request>")]
pub fn add_payment_method(
    auth: AuthUser,
    organization_id: String,
    request: Json<AddPaymentMethodRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<PaymentMethodResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // In a real implementation, you would validate the payment method with Stripe
    // For now, we'll create a mock payment method
    let set_as_default = request.set_as_default.unwrap_or(false);

    // Create new payment method
    let new_payment_method = NewPaymentMethod {
        organization_id: org_id,
        stripe_payment_method_id: request.payment_method_id.clone(),
        payment_type: "card".to_string(), // Mock value
        card_brand: Some("visa".to_string()), // Mock value
        last_four: Some("4242".to_string()), // Mock value
        expiry_month: Some(12), // Mock value
        expiry_year: Some(2025), // Mock value
        is_default: set_as_default,
    };

    match PaymentRepository::create(&mut conn, &new_payment_method) {
        Ok(payment_method) => ApiResponse::success_with_status(
            payment_method.into(),
            Status::Created
        ),
        Err(e) => {
            eprintln!("Error creating payment method: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to add payment method"
            )
        }
    }
}

/// Delete Payment Method API
///
/// Deletes a payment method from an organization
#[delete("/<organization_id>/payment-methods/<payment_method_id>")]
pub fn delete_payment_method(
    auth: AuthUser,
    organization_id: String,
    payment_method_id: String,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<()>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Check if payment method exists and belongs to the organization
    let payment_method = match PaymentRepository::find_by_stripe_id(&mut conn, &payment_method_id) {
        Ok(pm) => pm,
        Err(e) => {
            eprintln!("Error finding payment method: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Payment method not found"
            );
        }
    };

    if payment_method.organization_id != org_id {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "Payment method does not belong to this organization"
        );
    }

    // In a real implementation, you would delete the payment method from Stripe
    // For now, we'll just delete it from our database
    match PaymentRepository::delete(&mut conn, payment_method.id) {
        Ok(_) => ApiResponse::success_with_message(
            (),
            "Payment method deleted successfully"
        ),
        Err(e) => {
            eprintln!("Error deleting payment method: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to delete payment method"
            )
        }
    }
}

/// Set Default Payment Method API
///
/// Sets a payment method as the default for an organization
#[post("/<organization_id>/payment-methods/<payment_method_id>/set-default", format = "json", data = "<request>")]
pub fn set_default_payment_method(
    auth: AuthUser,
    organization_id: String,
    payment_method_id: String,
    request: Json<SetDefaultPaymentMethodRequest>,
    mut conn: DbConn,
    stripe_service: &State<StripeService>
) -> Json<ApiResponse<PaymentMethodResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Check if payment method exists and belongs to the organization
    let payment_method = match PaymentRepository::find_by_stripe_id(&mut conn, &payment_method_id) {
        Ok(pm) => pm,
        Err(e) => {
            eprintln!("Error finding payment method: {}", e);
            return ApiResponse::error(
                Status::NotFound,
                ErrorCode::NotFound,
                "Payment method not found"
            );
        }
    };

    if payment_method.organization_id != org_id {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "Payment method does not belong to this organization"
        );
    }

    // Update payment method
    let update = UpdatePaymentMethod {
        payment_type: None,
        last_four: None,
        card_brand: None,
        expiry_month: None,
        expiry_year: None,
        is_default: Some(request.is_default),
        updated_at: Some(Utc::now()),
    };

    match PaymentRepository::update(&mut conn, payment_method.id, &update) {
        Ok(updated_pm) => ApiResponse::success(updated_pm.into()),
        Err(e) => {
            eprintln!("Error updating payment method: {}", e);
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                "Failed to update payment method"
            )
        }
    }
}
