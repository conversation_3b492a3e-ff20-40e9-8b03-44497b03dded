use rocket::serde::json::<PERSON><PERSON>;
use rocket::http::Status;
use uuid::Uuid;

use crate::auth::guards::AuthUser;
use crate::db::DbConn;
use crate::models::credit_limit::{CreditLimitRequest, CreditLimitResponse, UpdateCreditLimit};
use crate::repositories::credit_limit_repository::CreditLimitRepository;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::utils::response::{ApiResponse, ErrorCode};

// Get Credit Limit API
#[get("/<organization_id>/credit-limit")]
pub fn get_credit_limit(
    auth: AuthUser,
    organization_id: String,
    mut conn: DbConn
) -> Json<ApiResponse<CreditLimitResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization
    if !OrganizationRepository::is_member(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have access to this organization"
        );
    }

    // Get or create credit limit
    match CreditLimitRepository::find_by_organization_id(&mut conn, org_id) {
        Ok(limit) => {
            ApiResponse::success(CreditLimitResponse::from(limit))
        },
        Err(_) => {
            // Create default credit limit
            let update_limit = UpdateCreditLimit {
                monthly_limit: None,
                warning_threshold: None,
                auto_purchase_enabled: None,
                auto_purchase_amount: None,
            };

            match CreditLimitRepository::create_or_update(&mut conn, org_id, &update_limit) {
                Ok(limit) => ApiResponse::success(CreditLimitResponse::from(limit)),
                Err(e) => ApiResponse::error(
                    Status::InternalServerError,
                    ErrorCode::InternalError,
                    &format!("Failed to create credit limit: {}", e)
                )
            }
        }
    }
}

// Update Credit Limit API
#[put("/<organization_id>/credit-limit", format = "json", data = "<request>")]
pub fn update_credit_limit(
    auth: AuthUser,
    organization_id: String,
    request: Json<CreditLimitRequest>,
    mut conn: DbConn
) -> Json<ApiResponse<CreditLimitResponse>> {
    let org_id = match Uuid::parse_str(&organization_id) {
        Ok(id) => id,
        Err(_) => {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::BadRequest,
                "Invalid organization ID format"
            );
        }
    };

    // Check if user has access to the organization and is an admin
    if !OrganizationRepository::is_admin(&mut conn, org_id, auth.user_id).unwrap_or(false) {
        return ApiResponse::error(
            Status::Forbidden,
            ErrorCode::Forbidden,
            "User does not have admin access to this organization"
        );
    }

    // Validate request
    if let Some(monthly_limit) = request.monthly_limit {
        if monthly_limit < 0 {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Monthly limit cannot be negative"
            );
        }
    }

    if let Some(warning_threshold) = request.warning_threshold {
        if warning_threshold < 0 || warning_threshold > 100 {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Warning threshold must be between 0 and 100"
            );
        }
    }

    if let Some(auto_purchase_amount) = request.auto_purchase_amount {
        if auto_purchase_amount < 100 {
            return ApiResponse::error(
                Status::BadRequest,
                ErrorCode::ValidationError,
                "Auto purchase amount must be at least 100"
            );
        }
    }

    // Convert request to update model
    let update_limit = UpdateCreditLimit {
        monthly_limit: request.monthly_limit,
        warning_threshold: request.warning_threshold,
        auto_purchase_enabled: request.auto_purchase_enabled,
        auto_purchase_amount: request.auto_purchase_amount,
    };

    // Update credit limit
    match CreditLimitRepository::create_or_update(&mut conn, org_id, &update_limit) {
        Ok(limit) => ApiResponse::success(CreditLimitResponse::from(limit)),
        Err(e) => ApiResponse::error(
            Status::InternalServerError,
            ErrorCode::InternalError,
            &format!("Failed to update credit limit: {}", e)
        )
    }
}
