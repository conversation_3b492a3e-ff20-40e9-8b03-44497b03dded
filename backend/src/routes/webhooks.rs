use rocket::http::Status;
use rocket::data::{Data, ToByteUnit};
use rocket::{post, State};
use serde_json::Value;
use std::env;
use chrono::{Datelike, Timelike, Utc};

use crate::db::DbConn;
use crate::models::organization::UpdateOrganization;
use crate::models::ai_credit::NewAICreditTransaction;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::ai_credit_repository::AICreditRepository;
use crate::payment::{StripeService, Webhook};
use crate::payment::stripe_webhook::handlers;

#[post("/stripe", data = "<body>")]
pub async fn stripe_webhook(
    body: Data<'_>,
    mut conn: DbConn,
    _stripe_service: &State<StripeService>,
    request: &rocket::Request<'_>,
) -> Status {
    // Get the Stripe signature from the header
    let signature = match request.headers().get_one("Stripe-Signature") {
        Some(sig) => sig,
        None => {
            eprintln!("Missing Stripe-Signature header");
            return Status::BadRequest;
        }
    };

    // Read the request body
    let body_str = match body.open(5.mebibytes()).into_string().await {
        Ok(string) => string,
        Err(_) => return Status::BadRequest,
    };

    // Verify webhook signature
    let webhook_secret = match env::var("STRIPE_WEBHOOK_SECRET") {
        Ok(secret) => secret,
        Err(_) => {
            eprintln!("STRIPE_WEBHOOK_SECRET not set");
            return Status::InternalServerError;
        }
    };

    // Parse the event
    let event = match Webhook::construct_event(
        &body_str,
        signature,
        &webhook_secret,
    ) {
        Ok(event) => event,
        Err(e) => {
            eprintln!("Error constructing webhook event: {}", e);
            return Status::BadRequest;
        }
    };

    // Process the event
    match event.type_str.as_str() {
        "customer.subscription.updated" => {
            // Parse the subscription data
            let subscription_data = match handlers::extract_subscription_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting subscription data: {}", e);
                    return Status::BadRequest;
                }
            };

            let subscription_id = subscription_data.subscription_id;
            let status = subscription_data.status;
            let current_period_end = subscription_data.current_period_end;

            // Find organization by subscription ID
            let organizations = match OrganizationRepository::find_by_stripe_subscription_id(&mut conn, &subscription_id) {
                Ok(orgs) => orgs,
                Err(e) => {
                    eprintln!("Error finding organization by subscription ID: {}", e);
                    return Status::InternalServerError;
                }
            };

            if let Some(org) = organizations.first() {
                // Map Stripe status to our status
                let org_status = match status.as_str() {
                    "active" => "active",
                    "past_due" => "past_due",
                    "unpaid" => "past_due",
                    "canceled" => "cancelled",
                    "incomplete" => "incomplete",
                    "incomplete_expired" => "cancelled",
                    "trialing" => "active",
                    _ => "active",
                };

                // Update organization subscription status
                let update = UpdateOrganization {
                    name: None,
                    subscription_tier: None,
                    subscription_status: Some(org_status.to_string()),
                    billing_period_start: None,
                    billing_period_end: current_period_end,
                    updated_at: Some(Utc::now()),
                    deleted_at: None,
                    stripe_customer_id: None,
                    stripe_subscription_id: None,
                    ai_credits_monthly_allowance: None,
                    ai_credits_remaining: None,
                    ai_credits_reset_date: None,
                };

                if let Err(e) = OrganizationRepository::update(&mut conn, org.id, &update) {
                    eprintln!("Error updating organization: {}", e);
                    return Status::InternalServerError;
                }
            } else {
                eprintln!("No organization found with subscription ID: {}", subscription_id);
            }
        },
        "invoice.payment_succeeded" => {
            // Process successful payment
            let invoice_data = match handlers::extract_invoice_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting invoice data: {}", e);
                    return Status::BadRequest;
                }
            };

            // Check if this is a subscription renewal
            if let Some(subscription_id) = &invoice_data.subscription_id {
                // Check if this is a recurring payment (status is paid)
                if invoice_data.status == "paid" {
                    // Find the organization with this subscription
                    let orgs = match OrganizationRepository::find_by_stripe_subscription_id(&mut conn, subscription_id) {
                        Ok(orgs) => orgs,
                        Err(_) => {
                            eprintln!("No organization found with subscription ID: {}", subscription_id);
                            return Status::BadRequest;
                        }
                    };

                        // Get the first organization
                    let org = match orgs.first() {
                        Some(org) => org,
                        None => {
                            eprintln!("No organization found with subscription ID: {}", subscription_id);
                            return Status::BadRequest;
                        }
                    };

                    // Calculate reset date (first day of next month)
                    let now = Utc::now();
                    let next_month = if now.month() == 12 {
                        Utc::now().with_year(now.year() + 1).unwrap().with_month(1).unwrap()
                    } else {
                        Utc::now().with_month(now.month() + 1).unwrap()
                    };
                    let reset_date = next_month.with_day(1).unwrap().with_hour(0).unwrap()
                        .with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();

                    // Get the monthly allowance
                    let monthly_allowance = org.ai_credits_monthly_allowance.unwrap_or(0);

                    // Update the organization with new credits
                    let update = UpdateOrganization {
                        name: None,
                        subscription_tier: None,
                        subscription_status: None,
                        billing_period_start: Some(Utc::now()),
                        billing_period_end: None,
                        updated_at: Some(Utc::now()),
                        deleted_at: None,
                        stripe_customer_id: None,
                        stripe_subscription_id: None,
                        ai_credits_monthly_allowance: None,
                        ai_credits_remaining: Some(monthly_allowance),
                        ai_credits_reset_date: Some(reset_date),
                    };

                    if let Err(e) = OrganizationRepository::update(&mut conn, org.id, &update) {
                        eprintln!("Error updating organization credits: {}", e);
                        return Status::InternalServerError;
                    }

                    // Record the credit reset
                    if let Err(e) = AICreditRepository::update_organization_credits(
                        &mut conn,
                        org.id,
                        monthly_allowance,
                        "subscription_renewal",
                        Some("Monthly credit reset"),
                        None,
                        None,
                        Some(&invoice_data.invoice_id),
                    ) {
                        eprintln!("Error recording credit reset: {}", e);
                        return Status::InternalServerError;
                    }
                }
            }

            // Check if this is related to AI credits purchase
            let metadata = &invoice_data.metadata;
            if let Some(payment_type) = metadata.get("type") {
                if payment_type == "ai_credits_purchase" {
                    // This is an AI credits purchase
                    let customer_id = invoice_data.customer_id;

                    // Get the amount of credits purchased
                    let credits_amount = match metadata.get("credits_amount") {
                        Some(amount) => amount.parse::<i32>().unwrap_or(0),
                        None => {
                            eprintln!("Invalid credits amount in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Get the organization ID
                    let organization_id = match metadata.get("organization_id") {
                        Some(id) => match uuid::Uuid::parse_str(id) {
                            Ok(uuid) => uuid,
                            Err(_) => {
                                eprintln!("Invalid organization ID in webhook");
                                return Status::BadRequest;
                            }
                        },
                        None => {
                            eprintln!("Missing organization ID in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Get the payment intent ID
                    let payment_intent_id = match invoice_data.payment_intent_id {
                        Some(id) => id,
                        None => {
                            eprintln!("Invalid payment intent ID in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Get the invoice ID
                    let invoice_id = invoice_data.invoice_id;

                    // Update the organization's credits
                    match AICreditRepository::update_organization_credits(
                        &mut conn,
                        organization_id,
                        credits_amount,
                        "purchase",
                        Some("AI Credits purchase via Stripe"),
                        None,
                        Some(&payment_intent_id),
                        Some(&invoice_id),
                    ) {
                        Ok(_) => {
                            println!("Successfully added {} credits to organization {}", credits_amount, organization_id);
                        },
                        Err(e) => {
                            eprintln!("Error updating organization credits: {}", e);
                            return Status::InternalServerError;
                        }
                    }
                }
            }
        },
        "invoice.payment_failed" => {
            // Process failed payment
            let invoice_data = match handlers::extract_invoice_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting invoice data: {}", e);
                    return Status::BadRequest;
                }
            };

            // Check if this is related to AI credits
            let metadata = &invoice_data.metadata;
            if let Some(payment_type) = metadata.get("type") {
                if payment_type == "ai_credits_purchase" {
                    // This is an AI credits purchase that failed
                    let organization_id = match metadata.get("organization_id") {
                        Some(id) => match uuid::Uuid::parse_str(id) {
                            Ok(uuid) => uuid,
                            Err(_) => {
                                eprintln!("Invalid organization ID in webhook");
                                return Status::BadRequest;
                            }
                        },
                        None => {
                            eprintln!("Missing organization ID in webhook");
                            return Status::BadRequest;
                        }
                    };

                    // Log the failed payment
                    let invoice_id = invoice_data.invoice_id;

                    // Create a transaction record for the failed payment
                    let new_transaction = NewAICreditTransaction {
                        organization_id,
                        transaction_type: "payment_failed".to_string(),
                        amount: 0, // No credits added
                        balance_after: None,
                        description: Some(format!("Payment failed for AI Credits purchase (Invoice: {})", invoice_id)),
                        metadata: None,
                        stripe_payment_intent_id: None,
                        stripe_invoice_id: Some(invoice_id),
                        created_by: None,
                    };

                    if let Err(e) = AICreditRepository::create_transaction(&mut conn, &new_transaction) {
                        eprintln!("Error creating transaction record for failed payment: {}", e);
                        return Status::InternalServerError;
                    }
                }
            }
        },
        "payment_intent.succeeded" => {
            // Process successful payment intent
            let payment_intent_data = match handlers::extract_payment_intent_data(&event) {
                Ok(data) => data,
                Err(e) => {
                    eprintln!("Error extracting payment intent data: {}", e);
                    return Status::BadRequest;
                }
            };

            // Check if this is related to AI credits
            let metadata = &payment_intent_data.metadata;
            if let Some(payment_type) = metadata.get("type") {
                if payment_type == "ai_credits_purchase" {
                    println!("Payment intent succeeded for AI credits purchase: {}", payment_intent_data.payment_intent_id);
                    // The actual credit update will happen in the invoice.payment_succeeded event
                }
            }
        },
        _ => {
            // Ignore other events
            println!("Ignoring webhook event: {}", event.type_str);
        }
    }

    Status::Ok
}
