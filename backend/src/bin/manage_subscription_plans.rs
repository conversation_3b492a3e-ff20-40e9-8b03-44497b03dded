use chrono::Utc;
use clap::{Parser, Subcommand};
use diesel::prelude::*;
use diesel::pg::PgConnection;
use dotenv::dotenv;
use serde_json::{json, Value};
use std::env;
use std::process;
use uuid::Uuid;

#[derive(Parser)]
#[command(author, version, about, long_about = None)]
struct Cli {
    #[command(subcommand)]
    command: Commands,
}

#[derive(Subcommand)]
enum Commands {
    /// List all subscription plans
    List,

    /// Add a new subscription plan
    Add {
        /// Tier name (e.g., free, standard, premium)
        #[arg(long)]
        tier_name: String,

        /// Display name (e.g., Free, Standard, Premium)
        #[arg(long)]
        display_name: String,

        /// Description of the plan
        #[arg(long)]
        description: Option<String>,

        /// Monthly price in USD
        #[arg(long)]
        monthly_price: f64,

        /// Yearly price in USD (optional)
        #[arg(long)]
        yearly_price: Option<f64>,

        /// Is this plan active?
        #[arg(long, default_value = "true")]
        is_active: bool,

        /// Sort order (lower values appear first)
        #[arg(long, default_value = "0")]
        sort_order: i32,

        /// Features as a JSON array string (e.g., '["Feature 1", "Feature 2"]')
        #[arg(long)]
        features: String,

        /// Limits as a JSON object string (e.g., '{"projects": 10, "team_members": 5}')
        #[arg(long)]
        limits: String,

        /// Is this plan popular? (for UI highlighting)
        #[arg(long, default_value = "false")]
        is_popular: bool,
    },

    /// Update an existing subscription plan
    Update {
        /// ID of the plan to update
        #[arg(long)]
        id: String,

        /// Display name (e.g., Free, Standard, Premium)
        #[arg(long)]
        display_name: Option<String>,

        /// Description of the plan
        #[arg(long)]
        description: Option<String>,

        /// Monthly price in USD
        #[arg(long)]
        monthly_price: Option<f64>,

        /// Yearly price in USD
        #[arg(long)]
        yearly_price: Option<f64>,

        /// Is this plan active?
        #[arg(long)]
        is_active: Option<bool>,

        /// Sort order (lower values appear first)
        #[arg(long)]
        sort_order: Option<i32>,

        /// Features as a JSON array string (e.g., '["Feature 1", "Feature 2"]')
        #[arg(long)]
        features: Option<String>,

        /// Limits as a JSON object string (e.g., '{"projects": 10, "team_members": 5}')
        #[arg(long)]
        limits: Option<String>,
    },

    /// Delete a subscription plan
    Delete {
        /// ID of the plan to delete
        #[arg(long)]
        id: String,
    },
}

fn main() {
    dotenv().ok();

    let cli = Cli::parse();

    // Connect to the database
    let database_url = env::var("DATABASE_URL")
        .expect("DATABASE_URL must be set");

    let mut conn = PgConnection::establish(&database_url)
        .expect("Failed to connect to the database");

    match cli.command {
        Commands::List => {
            list_plans(&mut conn);
        },
        Commands::Add {
            tier_name,
            display_name,
            description,
            monthly_price,
            yearly_price,
            is_active,
            sort_order,
            features,
            limits,
            is_popular
        } => {
            add_plan(
                &mut conn,
                tier_name,
                display_name,
                description,
                monthly_price,
                yearly_price,
                is_active,
                sort_order,
                features,
                limits,
                is_popular
            );
        },
        Commands::Update {
            id,
            display_name,
            description,
            monthly_price,
            yearly_price,
            is_active,
            sort_order,
            features,
            limits
        } => {
            update_plan(
                &mut conn,
                id,
                display_name,
                description,
                monthly_price,
                yearly_price,
                is_active,
                sort_order,
                features,
                limits
            );
        },
        Commands::Delete { id } => {
            delete_plan(&mut conn, id);
        },
    }
}

fn list_plans(conn: &mut PgConnection) {
    // Use a simpler approach with direct SQL execution
    let query = "SELECT id, tier_name, display_name, monthly_price::text, yearly_price::text, is_active
                FROM subscription_tiers ORDER BY sort_order ASC";

    let result = diesel::sql_query(query).execute(conn);

    match result {
        Ok(_) => {
            println!("Subscription Plans:");
            println!("{:<36} {:<15} {:<20} {:<10} {:<10} {:<10}", "ID", "Tier Name", "Display Name", "Monthly", "Yearly", "Active");
            println!("{}", "-".repeat(100));

            // Since we can't easily deserialize the results, just print a message
            println!("Successfully queried subscription plans. Please implement a custom deserializer for the results.");
        },
        Err(e) => {
            eprintln!("Error listing subscription plans: {}", e);
            process::exit(1);
        }
    }
}

fn add_plan(
    conn: &mut PgConnection,
    tier_name: String,
    display_name: String,
    description: Option<String>,
    monthly_price: f64,
    yearly_price: Option<f64>,
    is_active: bool,
    sort_order: i32,
    features_str: String,
    limits_str: String,
    is_popular: bool
) {
    // Parse JSON strings
    let features: Value = match serde_json::from_str(&features_str) {
        Ok(v) => v,
        Err(e) => {
            eprintln!("Error parsing features JSON: {}", e);
            process::exit(1);
        }
    };

    let limits: Value = match serde_json::from_str(&limits_str) {
        Ok(v) => v,
        Err(e) => {
            eprintln!("Error parsing limits JSON: {}", e);
            process::exit(1);
        }
    };

    // Add metadata for UI
    let mut limits_with_meta = limits.clone();
    if let Value::Object(ref mut map) = limits_with_meta {
        map.insert("is_popular".to_string(), json!(is_popular));
    }

    // Use a simpler approach with direct SQL execution
    let query = format!(
        "INSERT INTO subscription_tiers
        (tier_name, display_name, description, monthly_price, yearly_price, limits, features, is_active, sort_order, created_at, updated_at)
        VALUES
        ('{}', '{}', {}, {}, {}, '{}', '{}', {}, {}, NOW(), NOW())
        RETURNING id",
        tier_name,
        display_name,
        if let Some(desc) = &description {
            format!("'{}'", desc)
        } else {
            "NULL".to_string()
        },
        monthly_price,
        if let Some(price) = yearly_price {
            price.to_string()
        } else {
            "NULL".to_string()
        },
        serde_json::to_string(&limits_with_meta).unwrap().replace("'", "''"),
        serde_json::to_string(&features).unwrap().replace("'", "''"),
        is_active,
        sort_order
    );

    // Execute the query
    let result = diesel::sql_query(&query).execute(conn);

    match result {
        Ok(_) => {
            println!("Successfully added subscription plan");
        },
        Err(e) => {
            eprintln!("Error adding subscription plan: {}", e);
            process::exit(1);
        }
    }
}

fn update_plan(
    conn: &mut PgConnection,
    id_str: String,
    display_name: Option<String>,
    description: Option<String>,
    monthly_price: Option<f64>,
    yearly_price: Option<f64>,
    is_active: Option<bool>,
    sort_order: Option<i32>,
    features_str: Option<String>,
    limits_str: Option<String>
) {
    // Parse UUID
    let id = match Uuid::parse_str(&id_str) {
        Ok(id) => id,
        Err(e) => {
            eprintln!("Invalid UUID: {}", e);
            process::exit(1);
        }
    };

    // Check if plan exists
    let exists_query = diesel::sql_query("SELECT 1 FROM subscription_tiers WHERE id = $1")
        .bind::<diesel::sql_types::Uuid, _>(id)
        .execute(conn);

    if let Err(e) = exists_query {
        eprintln!("Error checking if plan exists: {}", e);
        process::exit(1);
    }

    // Build update query
    let mut query = String::from("UPDATE subscription_tiers SET updated_at = $1");
    let mut param_index = 2;
    let mut params: Vec<(String, String)> = Vec::new();

    if let Some(val) = &display_name {
        query.push_str(&format!(", display_name = ${}", param_index));
        params.push(("display_name".to_string(), val.clone()));
        param_index += 1;
    }

    if let Some(val) = &description {
        query.push_str(&format!(", description = ${}", param_index));
        params.push(("description".to_string(), val.clone()));
        param_index += 1;
    }

    if let Some(val) = monthly_price {
        query.push_str(&format!(", monthly_price = ${}", param_index));
        params.push(("monthly_price".to_string(), val.to_string()));
        param_index += 1;
    }

    if let Some(val) = yearly_price {
        query.push_str(&format!(", yearly_price = ${}", param_index));
        params.push(("yearly_price".to_string(), val.to_string()));
        param_index += 1;
    }

    if let Some(val) = is_active {
        query.push_str(&format!(", is_active = ${}", param_index));
        params.push(("is_active".to_string(), val.to_string()));
        param_index += 1;
    }

    if let Some(val) = sort_order {
        query.push_str(&format!(", sort_order = ${}", param_index));
        params.push(("sort_order".to_string(), val.to_string()));
        param_index += 1;
    }

    // Parse and update features if provided
    let features: Option<Value> = if let Some(features_str) = &features_str {
        match serde_json::from_str(features_str) {
            Ok(v) => Some(v),
            Err(e) => {
                eprintln!("Error parsing features JSON: {}", e);
                process::exit(1);
            }
        }
    } else {
        None
    };

    if features.is_some() {
        query.push_str(&format!(", features = ${}", param_index));
        param_index += 1;
    }

    // Parse and update limits if provided
    let limits: Option<Value> = if let Some(limits_str) = &limits_str {
        match serde_json::from_str(limits_str) {
            Ok(v) => Some(v),
            Err(e) => {
                eprintln!("Error parsing limits JSON: {}", e);
                process::exit(1);
            }
        }
    } else {
        None
    };

    if limits.is_some() {
        query.push_str(&format!(", limits = ${}", param_index));
        param_index += 1;
    }

    // Add WHERE clause
    query.push_str(&format!(" WHERE id = ${}", param_index));

    // Execute update
    let now = Utc::now().naive_utc();

    // Create a vector to hold all parameters
    let mut bind_params: Vec<(String, String)> = Vec::new();

    // Add timestamp as first parameter
    bind_params.push(("timestamp".to_string(), now.to_string()));

    // Add all other parameters
    bind_params.extend(params);

    // Add features if they exist
    if features.is_some() {
        bind_params.push(("features".to_string(), serde_json::to_string(&features).unwrap()));
    }

    // Add limits if they exist
    if limits.is_some() {
        bind_params.push(("limits".to_string(), serde_json::to_string(&limits).unwrap()));
    }

    // Add ID as the last parameter
    bind_params.push(("id".to_string(), id.to_string()));

    // Build the query with parameter placeholders
    let query_str = format!("{} WHERE id = ${}",
        query,
        bind_params.len()
    );

    // Execute the query directly with parameters
    match diesel::sql_query(&query_str).execute(conn) {
        Ok(rows) => {
            if rows > 0 {
                println!("Successfully updated subscription plan with ID: {}", id);
            } else {
                println!("No subscription plan found with ID: {}", id);
            }
        },
        Err(e) => {
            eprintln!("Error updating subscription plan: {}", e);
            process::exit(1);
        }
    }
}

fn delete_plan(conn: &mut PgConnection, id_str: String) {
    // Parse UUID
    let id = match Uuid::parse_str(&id_str) {
        Ok(id) => id,
        Err(e) => {
            eprintln!("Invalid UUID: {}", e);
            process::exit(1);
        }
    };

    // Execute delete query
    let result = diesel::sql_query("DELETE FROM subscription_tiers WHERE id = $1")
        .bind::<diesel::sql_types::Uuid, _>(id)
        .execute(conn);

    match result {
        Ok(rows) => {
            if rows > 0 {
                println!("Successfully deleted subscription plan with ID: {}", id);
            } else {
                println!("No subscription plan found with ID: {}", id);
            }
        },
        Err(e) => {
            eprintln!("Error deleting subscription plan: {}", e);
            process::exit(1);
        }
    }
}
