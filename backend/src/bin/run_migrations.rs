use diesel::prelude::*;
use diesel_migrations::{embed_migrations, EmbeddedMigrations, MigrationHarness};
use std::env;

pub const MIGRATIONS: EmbeddedMigrations = embed_migrations!("migrations");

fn main() {
    let database_url = env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let mut conn = PgConnection::establish(&database_url)
        .expect("Could not establish connection to the database");

    println!("Running migrations...");
    conn.run_pending_migrations(MIGRATIONS).expect("Failed to run migrations");
    println!("Migrations completed successfully!");
}
