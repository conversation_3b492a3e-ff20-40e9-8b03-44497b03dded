use anyhow::{anyhow, Result};
use chrono::{Datelike, Timelike, Utc};
use std::sync::Arc;
use tokio::time::{sleep, Duration};

use crate::db::Pool;
use crate::models::organization::UpdateOrganization;
use crate::repositories::organization_repository::OrganizationRepository;
use crate::repositories::ai_credit_repository::AICreditRepository;

pub struct CreditResetTask {
    db_pool: Arc<Pool>,
}

impl CreditResetTask {
    pub fn new(db_pool: Arc<Pool>) -> Self {
        Self { db_pool }
    }

    pub async fn start(self) {
        tokio::spawn(async move {
            loop {
                // Check if it's the first day of the month
                let now = Utc::now();
                if now.day() == 1 {
                    // It's the first day of the month, reset credits
                    if let Err(e) = self.reset_all_credits().await {
                        eprintln!("Error resetting credits: {}", e);
                    }
                }

                // Sleep for 1 hour before checking again
                sleep(Duration::from_secs(3600)).await;
            }
        });
    }

    async fn reset_all_credits(&self) -> Result<()> {
        let mut conn = self.db_pool.get()
            .map_err(|e| anyhow!("Failed to get database connection: {}", e))?;

        // Get all active organizations
        let organizations = OrganizationRepository::list_active_pooled(&mut conn)
            .map_err(|e| anyhow!("Failed to list active organizations: {}", e))?;

        println!("Resetting AI credits for {} organizations", organizations.len());

        // Calculate reset date (first day of next month)
        let now = Utc::now();
        let next_month = if now.month() == 12 {
            Utc::now().with_year(now.year() + 1).unwrap().with_month(1).unwrap()
        } else {
            Utc::now().with_month(now.month() + 1).unwrap()
        };
        let reset_date = next_month.with_day(1).unwrap().with_hour(0).unwrap()
            .with_minute(0).unwrap().with_second(0).unwrap().with_nanosecond(0).unwrap();

        // Reset credits for each organization
        for org in organizations {
            // For now, we'll use a hardcoded value since we've removed the field from the Organization model
            let monthly_allowance = 1000; // Default monthly allowance

            // Update the organization with new credits
            let update = UpdateOrganization {
                name: None,
                slug: None,
                subscription_tier: None,
                subscription_status: None,
                description: None,
                logo_url: None,
                website: None,
                updated_at: Some(Utc::now()),
                stripe_customer_id: None,
                stripe_subscription_id: None,
                subscription_tier_id: None,
                subscription_auto_renew: None,
                billing_period_start: None,
                billing_period_end: Some(reset_date),
            };

            match OrganizationRepository::update_pooled(&mut conn, org.id, &update) {
                Ok(_) => {
                    // Record the credit reset
                    if let Err(e) = AICreditRepository::update_organization_credits_pooled(
                        &mut conn,
                        org.id,
                        monthly_allowance,
                        "monthly_reset",
                        Some("Monthly credit reset"),
                        None,
                        None,
                        None,
                    ) {
                        eprintln!("Error recording credit reset for organization {}: {}", org.id, e);
                    } else {
                        println!("Reset {} credits for organization {}", monthly_allowance, org.id);
                    }
                },
                Err(e) => {
                    eprintln!("Error updating credits for organization {}: {}", org.id, e);
                }
            }
        }

        Ok(())
    }
}
