use anyhow::Result;
use handlebars::Handlebars;
use serde::Serialize;
use std::sync::OnceLock;

static HANDLEBARS: OnceLock<Handlebars<'static>> = OnceLock::new();

fn get_handlebars() -> &'static Handlebars<'static> {
    HANDLEBARS.get_or_init(|| {
        let mut handlebars = Handlebars::new();
        
        // Register email verification template
        handlebars
            .register_template_string(
                "email_verification_html",
                include_str!("../../templates/email/verification.html"),
            )
            .expect("Failed to register email verification HTML template");
        
        handlebars
            .register_template_string(
                "email_verification_text",
                include_str!("../../templates/email/verification.txt"),
            )
            .expect("Failed to register email verification text template");
        
        // Register password reset template
        handlebars
            .register_template_string(
                "password_reset_html",
                include_str!("../../templates/email/password_reset.html"),
            )
            .expect("Failed to register password reset HTML template");
        
        handlebars
            .register_template_string(
                "password_reset_text",
                include_str!("../../templates/email/password_reset.txt"),
            )
            .expect("Failed to register password reset text template");
        
        // Register welcome template
        handlebars
            .register_template_string(
                "welcome_html",
                include_str!("../../templates/email/welcome.html"),
            )
            .expect("Failed to register welcome HTML template");
        
        handlebars
            .register_template_string(
                "welcome_text",
                include_str!("../../templates/email/welcome.txt"),
            )
            .expect("Failed to register welcome text template");
        
        handlebars
    })
}

#[derive(Serialize)]
pub struct EmailVerificationContext {
    pub username: String,
    pub verification_link: String,
    pub app_name: String,
}

pub fn render_email_verification(context: &EmailVerificationContext) -> Result<(String, String)> {
    let handlebars = get_handlebars();
    
    let html = handlebars.render("email_verification_html", context)?;
    let text = handlebars.render("email_verification_text", context)?;
    
    Ok((html, text))
}

#[derive(Serialize)]
pub struct PasswordResetContext {
    pub username: String,
    pub reset_link: String,
    pub app_name: String,
}

pub fn render_password_reset(context: &PasswordResetContext) -> Result<(String, String)> {
    let handlebars = get_handlebars();
    
    let html = handlebars.render("password_reset_html", context)?;
    let text = handlebars.render("password_reset_text", context)?;
    
    Ok((html, text))
}

#[derive(Serialize)]
pub struct WelcomeContext {
    pub username: String,
    pub login_link: String,
    pub app_name: String,
}

pub fn render_welcome(context: &WelcomeContext) -> Result<(String, String)> {
    let handlebars = get_handlebars();
    
    let html = handlebars.render("welcome_html", context)?;
    let text = handlebars.render("welcome_text", context)?;
    
    Ok((html, text))
}
