// This module is deprecated and replaced by cloud_notification.rs
// It's kept for backward compatibility

use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Re-export the CloudNotificationService for backward compatibility

#[derive(Debug, Serialize)]
pub struct SendEmailRequest {
    pub to: Vec<EmailRecipient>,
    pub sender: EmailSender,
    pub subject: String,
    pub html_content: String,
    pub text_content: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailRecipient {
    pub email: String,
    pub name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct EmailSender {
    pub email: String,
    pub name: String,
}

// Deprecated - use CloudNotificationService instead
pub struct BrevoEmailService;

impl BrevoEmailService {
    pub fn new() -> Result<Self> {
        println!("WARNING: BrevoEmailService is deprecated. Use CloudNotificationService instead.");
        Ok(Self)
    }

    pub async fn send_email(
        &self,
        _to_email: &str,
        _to_name: Option<&str>,
        _subject: &str,
        _html_content: &str,
        _text_content: &str,
        _user_id: Option<Uuid>,
    ) -> Result<String> {
        Err(anyhow!("BrevoEmailService is deprecated. Use CloudNotificationService instead."))
    }

    pub async fn send_verification_email(
        &self,
        _to_email: &str,
        _to_name: Option<&str>,
        _verification_url: &str,
        _app_name: &str,
        _user_id: Option<Uuid>,
    ) -> Result<String> {
        Err(anyhow!("BrevoEmailService is deprecated. Use CloudNotificationService instead."))
    }

    pub async fn send_password_reset_email(
        &self,
        _to_email: &str,
        _to_name: Option<&str>,
        _reset_url: &str,
        _app_name: &str,
        _user_id: Option<Uuid>,
    ) -> Result<String> {
        Err(anyhow!("BrevoEmailService is deprecated. Use CloudNotificationService instead."))
    }
}
