use anyhow::{anyhow, Result};
use reqwest::{Client, header};
use serde::{Deserialize, Serialize};
use std::env;
use uuid::Uuid;

#[derive(Clone)]
pub struct CloudNotificationService {
    client: Client,
    brevo_api_key: String,
    sender_email: String,
    sender_name: String,
    cloud_function_url: String,
    service_api_key: String,
}

#[derive(Debug, Serialize)]
struct EmailNotificationRequest {
    notification_type: String,
    to_email: String,
    to_name: Option<String>,
    subject: String,
    html_content: String,
    text_content: String,
    api_key: String,
    sender_email: String,
    sender_name: String,
    service_api_key: String,
}

#[derive(Debug, Serialize)]
struct VerificationEmailRequest {
    notification_type: String,
    to_email: String,
    to_name: Option<String>,
    verification_url: String,
    app_name: String,
    api_key: String,
    sender_email: String,
    sender_name: String,
    service_api_key: String,
}

#[derive(Debug, Serial<PERSON>)]
struct PasswordResetEmailRequest {
    notification_type: String,
    to_email: String,
    to_name: Option<String>,
    reset_url: String,
    app_name: String,
    api_key: String,
    sender_email: String,
    sender_name: String,
    service_api_key: String,
}

#[derive(Debug, Deserialize)]
struct NotificationResponse {
    success: bool,
    message: String,
    message_id: Option<String>,
}

impl CloudNotificationService {
    pub fn new() -> Result<Self> {
        let brevo_api_key = env::var("BREVO_API_KEY")
            .map_err(|_| anyhow!("BREVO_API_KEY environment variable not set"))?;

        let sender_email = env::var("EMAIL_SENDER_ADDRESS")
            .map_err(|_| anyhow!("EMAIL_SENDER_ADDRESS environment variable not set"))?;

        let sender_name = env::var("EMAIL_SENDER_NAME")
            .map_err(|_| anyhow!("EMAIL_SENDER_NAME environment variable not set"))?;

        let cloud_function_url = env::var("NOTIFICATION_CLOUD_FUNCTION_URL")
            .map_err(|_| anyhow!("NOTIFICATION_CLOUD_FUNCTION_URL environment variable not set"))?;

        let service_api_key = env::var("NOTIFICATION_SERVICE_API_KEY")
            .map_err(|_| anyhow!("NOTIFICATION_SERVICE_API_KEY environment variable not set"))?;

        // Create a client with default headers
        let mut headers = header::HeaderMap::new();
        headers.insert(
            "X-API-Key",
            header::HeaderValue::from_str(&service_api_key)
                .map_err(|e| anyhow!("Invalid API key header value: {}", e))?,
        );

        let client = Client::builder()
            .default_headers(headers)
            .build()
            .map_err(|e| anyhow!("Failed to build HTTP client: {}", e))?;

        Ok(Self {
            client,
            brevo_api_key,
            sender_email,
            sender_name,
            cloud_function_url,
            service_api_key,
        })
    }

    pub fn mock() -> Self {
        // Create a default client
        let client = Client::new();

        // Return a mock service with dummy values
        Self {
            client,
            brevo_api_key: "mock-api-key".to_string(),
            sender_email: "<EMAIL>".to_string(),
            sender_name: "Mock Sender".to_string(),
            cloud_function_url: "https://mock-function-url.com".to_string(),
            service_api_key: "mock-service-api-key".to_string(),
        }
    }

    pub async fn send_email(
        &self,
        to_email: &str,
        to_name: Option<&str>,
        subject: &str,
        html_content: &str,
        text_content: &str,
        _user_id: Option<Uuid>,
    ) -> Result<String> {
        // Prepare request
        let request = EmailNotificationRequest {
            notification_type: "email".to_string(),
            to_email: to_email.to_string(),
            to_name: to_name.map(|s| s.to_string()),
            subject: subject.to_string(),
            html_content: html_content.to_string(),
            text_content: text_content.to_string(),
            api_key: self.brevo_api_key.clone(),
            sender_email: self.sender_email.clone(),
            sender_name: self.sender_name.clone(),
            service_api_key: self.service_api_key.clone(),
        };

        // Send request to cloud function
        let response = self.client
            .post(&self.cloud_function_url)
            .json(&request)
            .send()
            .await?;

        let status = response.status();

        if status.is_success() {
            let notification_response = response.json::<NotificationResponse>().await?;

            if notification_response.success {
                let message_id = notification_response.message_id.unwrap_or_else(|| "unknown".to_string());
                println!("Email sent successfully to {}, message ID: {}", to_email, message_id);
                Ok(message_id)
            } else {
                Err(anyhow!("Failed to send email: {}", notification_response.message))
            }
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            println!("Failed to send email to {}: {}", to_email, error_text);
            Err(anyhow!("Failed to send email: {}", error_text))
        }
    }

    pub async fn send_verification_email(
        &self,
        to_email: &str,
        to_name: Option<&str>,
        verification_url: &str,
        app_name: &str,
        _user_id: Option<Uuid>,
    ) -> Result<String> {
        // Prepare request
        let request = VerificationEmailRequest {
            notification_type: "verification_email".to_string(),
            to_email: to_email.to_string(),
            to_name: to_name.map(|s| s.to_string()),
            verification_url: verification_url.to_string(),
            app_name: app_name.to_string(),
            api_key: self.brevo_api_key.clone(),
            sender_email: self.sender_email.clone(),
            sender_name: self.sender_name.clone(),
            service_api_key: self.service_api_key.clone(),
        };

        // Send request to cloud function
        let response = self.client
            .post(&self.cloud_function_url)
            .json(&request)
            .send()
            .await?;

        let status = response.status();

        if status.is_success() {
            let notification_response = response.json::<NotificationResponse>().await?;

            if notification_response.success {
                let message_id = notification_response.message_id.unwrap_or_else(|| "unknown".to_string());
                println!("Verification email sent successfully to {}, message ID: {}", to_email, message_id);
                Ok(message_id)
            } else {
                Err(anyhow!("Failed to send verification email: {}", notification_response.message))
            }
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            println!("Failed to send verification email to {}: {}", to_email, error_text);
            Err(anyhow!("Failed to send verification email: {}", error_text))
        }
    }

    pub async fn send_password_reset_email(
        &self,
        to_email: &str,
        to_name: Option<&str>,
        reset_url: &str,
        app_name: &str,
        _user_id: Option<Uuid>,
    ) -> Result<String> {
        // Prepare request
        let request = PasswordResetEmailRequest {
            notification_type: "password_reset".to_string(),
            to_email: to_email.to_string(),
            to_name: to_name.map(|s| s.to_string()),
            reset_url: reset_url.to_string(),
            app_name: app_name.to_string(),
            api_key: self.brevo_api_key.clone(),
            sender_email: self.sender_email.clone(),
            sender_name: self.sender_name.clone(),
            service_api_key: self.service_api_key.clone(),
        };

        // Send request to cloud function
        let response = self.client
            .post(&self.cloud_function_url)
            .json(&request)
            .send()
            .await?;

        let status = response.status();

        if status.is_success() {
            let notification_response = response.json::<NotificationResponse>().await?;

            if notification_response.success {
                let message_id = notification_response.message_id.unwrap_or_else(|| "unknown".to_string());
                println!("Password reset email sent successfully to {}, message ID: {}", to_email, message_id);
                Ok(message_id)
            } else {
                Err(anyhow!("Failed to send password reset email: {}", notification_response.message))
            }
        } else {
            let error_text = response.text().await.unwrap_or_else(|_| "Unknown error".to_string());
            println!("Failed to send password reset email to {}: {}", to_email, error_text);
            Err(anyhow!("Failed to send password reset email: {}", error_text))
        }
    }
}
