use diesel::prelude::*;
use diesel::sql_query;
use uuid::Uuid;
use anyhow::{anyhow, Result};

use crate::db::DbConn;
use crate::models::translation::{TranslationKey, NewTranslationKey, Translation, NewTranslation, UpdateTranslation, TranslationHistory, NewTranslationHistory};
use crate::schema::{translation_keys, translations, translation_history};

pub struct TranslationRepository;

impl TranslationRepository {
    pub fn find_key_by_id(conn: &mut DbConn, id: Uuid) -> Result<TranslationKey> {
        translation_keys::table
            .find(id)
            .first::<TranslationKey>(&mut **conn)
            .map_err(|e| anyhow!("Error finding translation key by ID: {}", e))
    }

    pub fn find_key_by_name(conn: &mut DbConn, resource_id: Uuid, key_name: &str) -> Result<TranslationKey> {
        translation_keys::table
            .filter(translation_keys::resource_id.eq(resource_id))
            .filter(translation_keys::key_name.eq(key_name))
            .first::<TranslationKey>(&mut **conn)
            .map_err(|e| anyhow!("Error finding translation key by name: {}", e))
    }

    pub fn find_key_by_name_in_project(conn: &mut DbConn, project_id: Uuid, key_name: &str) -> Result<TranslationKey> {
        translation_keys::table
            .filter(translation_keys::project_id.eq(project_id))
            .filter(translation_keys::key_name.eq(key_name))
            .first::<TranslationKey>(&mut **conn)
            .map_err(|e| anyhow!("Error finding translation key by name in project: {}", e))
    }

    pub fn create_key(conn: &mut DbConn, new_key: &NewTranslationKey) -> Result<TranslationKey> {
        diesel::insert_into(translation_keys::table)
            .values(new_key)
            .get_result::<TranslationKey>(&mut **conn)
            .map_err(|e| anyhow!("Error creating translation key: {}", e))
    }

    pub fn list_keys_by_resource(conn: &mut DbConn, resource_id: Uuid) -> Result<Vec<TranslationKey>> {
        translation_keys::table
            .filter(translation_keys::resource_id.eq(resource_id))
            .order_by(translation_keys::key_name.asc())
            .load::<TranslationKey>(&mut **conn)
            .map_err(|e| anyhow!("Error listing translation keys by resource: {}", e))
    }

    pub fn list_keys_by_resource_project(conn: &mut DbConn, project_id: Uuid) -> Result<Vec<TranslationKey>> {
        // Get all translation keys for the project through resources
        eprintln!("Getting translation keys through resources for project_id: {}", project_id);

        // First, get all resources for the project
        use crate::schema::resources;

        let resource_ids = resources::table
            .filter(resources::project_id.eq(project_id))
            .select(resources::id)
            .load::<Uuid>(&mut **conn)
            .map_err(|e| {
                eprintln!("Error getting resources for project: {:?}", e);
                anyhow!("Error getting resources for project: {}", e)
            })?;

        eprintln!("Found {} resources for project", resource_ids.len());

        if resource_ids.is_empty() {
            return Ok(Vec::new());
        }

        // Get all translation keys for these resources
        let keys = translation_keys::table
            .filter(translation_keys::resource_id.eq_any(resource_ids))
            .order_by(translation_keys::key_name.asc())
            .load::<TranslationKey>(&mut **conn)
            .map_err(|e| {
                eprintln!("Error getting translation keys for resources: {:?}", e);
                anyhow!("Error getting translation keys for resources: {}", e)
            })?;

        eprintln!("Found {} translation keys through resources", keys.len());

        Ok(keys)
    }

    pub fn list_keys_by_project(conn: &mut DbConn, project_id: Uuid) -> Result<Vec<TranslationKey>> {
        // Use Diesel ORM to get translation keys by project_id
        eprintln!("Querying translation keys for project_id: {}", project_id);

        // Check if the project_id column exists in the translation_keys table
        let has_project_id_column = true; // We've updated the schema, so we know it exists

        if !has_project_id_column {
            eprintln!("project_id column does not exist in translation_keys table");
            return Ok(Vec::new());
        }

        // Get all translation keys for the project
        translation_keys::table
            .filter(translation_keys::project_id.eq(project_id))
            .order_by(translation_keys::key_name.asc())
            .load::<TranslationKey>(&mut **conn)
            .map_err(|e| {
                eprintln!("Database error when listing translation keys: {:?}", e);
                anyhow!("Error listing translation keys by project: {}", e)
            })
    }

    pub fn find_translation(conn: &mut DbConn, key_id: Uuid, locale_id: Uuid) -> Result<Translation> {
        translations::table
            .filter(translations::key_id.eq(key_id))
            .filter(translations::locale_id.eq(locale_id))
            .select(Translation::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding translation: {}", e))
    }

    pub fn create_translation(conn: &mut DbConn, new_translation: &NewTranslation) -> Result<Translation> {
        diesel::insert_into(translations::table)
            .values(new_translation)
            .returning(Translation::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error creating translation: {}", e))
    }

    pub fn update_translation(conn: &mut DbConn, id: Uuid, update: &UpdateTranslation) -> Result<Translation> {
        diesel::update(translations::table.find(id))
            .set(update)
            .returning(Translation::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error updating translation: {}", e))
    }

    pub fn list_translations_by_key(conn: &mut DbConn, key_id: Uuid) -> Result<Vec<Translation>> {
        translations::table
            .filter(translations::key_id.eq(key_id))
            .select(Translation::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing translations by key: {}", e))
    }

    pub fn list_translations_by_locale(conn: &mut DbConn, locale_id: Uuid, key_ids: &[Uuid]) -> Result<Vec<Translation>> {
        translations::table
            .filter(translations::locale_id.eq(locale_id))
            .filter(translations::key_id.eq_any(key_ids))
            .select(Translation::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing translations by locale: {}", e))
    }

    pub fn add_history(conn: &mut DbConn, new_history: &NewTranslationHistory) -> Result<TranslationHistory> {
        diesel::insert_into(translation_history::table)
            .values(new_history)
            .returning(TranslationHistory::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error adding translation history: {}", e))
    }

    pub fn list_history(conn: &mut DbConn, translation_id: Uuid) -> Result<Vec<TranslationHistory>> {
        translation_history::table
            .filter(translation_history::translation_id.eq(translation_id))
            .order_by(translation_history::created_at.desc())
            .select(TranslationHistory::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing translation history: {}", e))
    }

    // Alias for list_history to match function calls in routes
    pub fn list_history_by_translation(conn: &mut DbConn, translation_id: Uuid) -> Result<Vec<TranslationHistory>> {
        Self::list_history(conn, translation_id)
    }

    // Alias for find_translation to match function calls in routes
    pub fn find_translation_by_id(conn: &mut DbConn, id: Uuid) -> Result<Translation> {
        translations::table
            .find(id)
            .select(Translation::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding translation by ID: {}", e))
    }
}
