use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};

use crate::db::DbConn;
use crate::models::project::{Project, NewProject, UpdateProject, ProjectLocale, NewProjectLocale, Resource, NewResource};
use crate::schema::{projects, project_locales, resources};

pub struct ProjectRepository;

impl ProjectRepository {
    pub fn find_by_id(conn: &mut DbConn, id: Uuid) -> Result<Project> {
        projects::table
            .find(id)
            .first::<Project>(&mut **conn)
            .map_err(|e| anyhow!("Error finding project by ID: {}", e))
    }

    pub fn find_by_slug(conn: &mut DbConn, org_id: Uuid, slug: &str) -> Result<Project> {
        projects::table
            .filter(projects::organization_id.eq(org_id))
            .filter(projects::slug.eq(slug))
            .first::<Project>(&mut **conn)
            .map_err(|e| anyhow!("Error finding project by slug: {}", e))
    }

    pub fn create(conn: &mut DbConn, new_project: &NewProject) -> Result<Project> {
        diesel::insert_into(projects::table)
            .values(new_project)
            .get_result::<Project>(&mut **conn)
            .map_err(|e| anyhow!("Error creating project: {}", e))
    }

    pub fn update(conn: &mut DbConn, id: Uuid, update_project: &UpdateProject) -> Result<Project> {
        diesel::update(projects::table.find(id))
            .set(update_project)
            .get_result::<Project>(&mut **conn)
            .map_err(|e| anyhow!("Error updating project: {}", e))
    }

    pub fn delete(conn: &mut DbConn, id: Uuid) -> Result<Project> {
        // Since we don't have a deleted_at column, we'll just return the project
        // In a real implementation, you might want to add a deleted_at column to the schema
        projects::table
            .find(id)
            .first::<Project>(&mut **conn)
            .map_err(|e| anyhow!("Error finding project for deletion: {}", e))
    }

    pub fn list_by_organization(conn: &mut DbConn, org_id: Uuid) -> Result<Vec<Project>> {
        projects::table
            .filter(projects::organization_id.eq(org_id))
            .load::<Project>(&mut **conn)
            .map_err(|e| anyhow!("Error listing projects by organization: {}", e))
    }

    pub fn add_locale(conn: &mut DbConn, new_locale: &NewProjectLocale) -> Result<ProjectLocale> {
        diesel::insert_into(project_locales::table)
            .values(new_locale)
            .returning(ProjectLocale::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error adding project locale: {}", e))
    }

    pub fn list_locales(conn: &mut DbConn, project_id: Uuid) -> Result<Vec<ProjectLocale>> {
        project_locales::table
            .filter(project_locales::project_id.eq(project_id))
            .select(ProjectLocale::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing project locales: {}", e))
    }

    pub fn create_resource(conn: &mut DbConn, new_resource: &NewResource) -> Result<Resource> {
        diesel::insert_into(resources::table)
            .values(new_resource)
            .returning(Resource::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error creating resource: {}", e))
    }

    pub fn find_resource_by_id(conn: &mut DbConn, id: Uuid) -> Result<Resource> {
        resources::table
            .find(id)
            .select(Resource::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding resource by ID: {}", e))
    }

    pub fn list_resources(conn: &mut DbConn, project_id: Uuid) -> Result<Vec<Resource>> {
        resources::table
            .filter(resources::project_id.eq(project_id))
            .select(Resource::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing resources: {}", e))
    }
}
