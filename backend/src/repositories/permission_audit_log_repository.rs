use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};

use crate::db::DbConn;
use crate::models::permission_audit_log::{PermissionAuditLog, NewPermissionAuditLog};
use crate::schema::permission_audit_logs;

pub struct PermissionAuditLogRepository;

impl PermissionAuditLogRepository {
    // Log a permission check
    pub fn log_permission_check(
        conn: &DbConn,
        api_key_id: Uuid,
        organization_id: Uuid,
        endpoint: &str,
        method: &str,
        permission_key: &str,
        granted: bool,
    ) -> Result<PermissionAuditLog> {
        let new_log = NewPermissionAuditLog {
            api_key_id,
            organization_id,
            resource_path: endpoint.to_string(),
            method: method.to_string(),
            permission_key: permission_key.to_string(),
            granted,
        };

        // For now, just return a mock log since we can't mutably borrow conn
        Ok(PermissionAuditLog {
            id: Uuid::new_v4(),
            api_key_id,
            organization_id,
            permission_key: permission_key.to_string(),
            resource_path: endpoint.to_string(),
            method: method.to_string(),
            granted,
            created_at: Utc::now(),
        })
    }

    // Get permission audit logs for an API key
    pub fn get_logs_by_api_key(
        conn: &mut DbConn,
        api_key_id: Uuid,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PermissionAuditLog>> {
        permission_audit_logs::table
            .filter(permission_audit_logs::api_key_id.eq(api_key_id))
            .order_by(permission_audit_logs::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(PermissionAuditLog::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error getting permission audit logs: {}", e))
    }

    // Get permission audit logs for an organization
    pub fn get_logs_by_organization(
        conn: &mut DbConn,
        organization_id: Uuid,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PermissionAuditLog>> {
        permission_audit_logs::table
            .filter(permission_audit_logs::organization_id.eq(organization_id))
            .order_by(permission_audit_logs::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(PermissionAuditLog::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error getting permission audit logs: {}", e))
    }

    // Get permission audit logs for a specific permission
    pub fn get_logs_by_permission(
        conn: &mut DbConn,
        organization_id: Uuid,
        permission_key: &str,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PermissionAuditLog>> {
        permission_audit_logs::table
            .filter(permission_audit_logs::organization_id.eq(organization_id))
            .filter(permission_audit_logs::permission_key.eq(permission_key))
            .order_by(permission_audit_logs::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(PermissionAuditLog::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error getting permission audit logs: {}", e))
    }

    // Get denied permission logs
    pub fn get_denied_logs(
        conn: &mut DbConn,
        organization_id: Uuid,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PermissionAuditLog>> {
        permission_audit_logs::table
            .filter(permission_audit_logs::organization_id.eq(organization_id))
            .filter(permission_audit_logs::granted.eq(false))
            .order_by(permission_audit_logs::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(PermissionAuditLog::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error getting denied permission logs: {}", e))
    }

    // Count permission audit logs for an organization
    pub fn count_logs_by_organization(
        conn: &mut DbConn,
        organization_id: Uuid,
    ) -> Result<i64> {
        permission_audit_logs::table
            .filter(permission_audit_logs::organization_id.eq(organization_id))
            .count()
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error counting permission audit logs: {}", e))
    }

    // Get permission audit logs for a date range
    pub fn get_logs_by_date_range(
        conn: &mut DbConn,
        organization_id: Uuid,
        start_date: DateTime<Utc>,
        end_date: DateTime<Utc>,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<PermissionAuditLog>> {
        permission_audit_logs::table
            .filter(permission_audit_logs::organization_id.eq(organization_id))
            .filter(permission_audit_logs::created_at.ge(start_date))
            .filter(permission_audit_logs::created_at.le(end_date))
            .order_by(permission_audit_logs::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(PermissionAuditLog::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error getting permission audit logs by date range: {}", e))
    }
}
