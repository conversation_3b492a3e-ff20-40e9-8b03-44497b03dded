use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};

use crate::db::{DbConn, PooledConn};
use crate::models::organization::{Organization, NewOrganization, UpdateOrganization, OrganizationMember, NewOrganizationMember};
use crate::schema::{organizations, organization_members};

pub struct OrganizationRepository;

impl OrganizationRepository {
    pub fn find_by_id(conn: &mut DbConn, id: Uuid) -> Result<Organization> {
        Self::find_by_id_pooled(conn, id)
    }

    pub fn find_by_id_pooled(conn: &mut PooledConn, id: Uuid) -> Result<Organization> {
        organizations::table
            .find(id)
            .select(Organization::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding organization by ID: {}", e))
    }

    pub fn find_by_slug(conn: &mut DbConn, slug: &str) -> Result<Organization> {
        Self::find_by_slug_pooled(conn, slug)
    }

    pub fn find_by_slug_pooled(conn: &mut PooledConn, slug: &str) -> Result<Organization> {
        organizations::table
            .filter(organizations::slug.eq(slug))
            .select(Organization::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding organization by slug: {}", e))
    }

    pub fn create(conn: &mut DbConn, new_org: &NewOrganization) -> Result<Organization> {
        Self::create_pooled(conn, new_org)
    }

    pub fn create_pooled(conn: &mut PooledConn, new_org: &NewOrganization) -> Result<Organization> {
        diesel::insert_into(organizations::table)
            .values(new_org)
            .returning(Organization::as_select())
            .get_result(conn)
            .map_err(|e| anyhow!("Error creating organization: {}", e))
    }

    pub fn update(conn: &mut DbConn, id: Uuid, update_org: &UpdateOrganization) -> Result<Organization> {
        Self::update_pooled(conn, id, update_org)
    }

    pub fn update_pooled(conn: &mut PooledConn, id: Uuid, update_org: &UpdateOrganization) -> Result<Organization> {
        diesel::update(organizations::table.find(id))
            .set(update_org)
            .returning(Organization::as_select())
            .get_result(conn)
            .map_err(|e| anyhow!("Error updating organization: {}", e))
    }

    pub fn delete(conn: &mut DbConn, id: Uuid) -> Result<Organization> {
        Self::delete_pooled(conn, id)
    }

    pub fn delete_pooled(conn: &mut PooledConn, id: Uuid) -> Result<Organization> {
        // Since we don't have a deleted_at column, we'll just return the organization
        // In a real implementation, you might want to add a deleted_at column to the schema
        organizations::table
            .find(id)
            .select(Organization::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding organization for deletion: {}", e))
    }

    pub fn list_by_user(conn: &mut DbConn, user_id: Uuid) -> Result<Vec<Organization>> {
        Self::list_by_user_pooled(conn, user_id)
    }

    pub fn list_by_user_pooled(conn: &mut PooledConn, user_id: Uuid) -> Result<Vec<Organization>> {
        organizations::table
            .inner_join(organization_members::table)
            .filter(organization_members::user_id.eq(user_id))
            .select(Organization::as_select())
            .load(conn)
            .map_err(|e| anyhow!("Error listing organizations by user: {}", e))
    }

    pub fn add_member(conn: &mut DbConn, new_member: &NewOrganizationMember) -> Result<OrganizationMember> {
        Self::add_member_pooled(conn, new_member)
    }

    pub fn add_member_pooled(conn: &mut PooledConn, new_member: &NewOrganizationMember) -> Result<OrganizationMember> {
        diesel::insert_into(organization_members::table)
            .values(new_member)
            .returning(OrganizationMember::as_select())
            .get_result(conn)
            .map_err(|e| anyhow!("Error adding organization member: {}", e))
    }

    pub fn remove_member(conn: &mut DbConn, org_id: Uuid, user_id: Uuid) -> Result<usize> {
        Self::remove_member_pooled(conn, org_id, user_id)
    }

    pub fn remove_member_pooled(conn: &mut PooledConn, org_id: Uuid, user_id: Uuid) -> Result<usize> {
        diesel::delete(
            organization_members::table
                .filter(organization_members::organization_id.eq(org_id))
                .filter(organization_members::user_id.eq(user_id))
        )
        .execute(conn)
        .map_err(|e| anyhow!("Error removing organization member: {}", e))
    }

    pub fn list_members(conn: &mut DbConn, org_id: Uuid) -> Result<Vec<OrganizationMember>> {
        Self::list_members_pooled(conn, org_id)
    }

    pub fn list_members_pooled(conn: &mut PooledConn, org_id: Uuid) -> Result<Vec<OrganizationMember>> {
        organization_members::table
            .filter(organization_members::organization_id.eq(org_id))
            .select(OrganizationMember::as_select())
            .load(conn)
            .map_err(|e| anyhow!("Error listing organization members: {}", e))
    }

    pub fn is_member(conn: &mut DbConn, org_id: Uuid, user_id: Uuid) -> Result<bool> {
        Self::is_member_pooled(conn, org_id, user_id)
    }

    pub fn is_member_pooled(conn: &mut PooledConn, org_id: Uuid, user_id: Uuid) -> Result<bool> {
        let count = organization_members::table
            .filter(organization_members::organization_id.eq(org_id))
            .filter(organization_members::user_id.eq(user_id))
            .count()
            .get_result::<i64>(conn)
            .map_err(|e| anyhow!("Error checking organization membership: {}", e))?;

        Ok(count > 0)
    }

    pub fn is_admin(conn: &mut DbConn, org_id: Uuid, user_id: Uuid) -> Result<bool> {
        Self::is_admin_pooled(conn, org_id, user_id)
    }

    pub fn is_admin_pooled(conn: &mut PooledConn, org_id: Uuid, user_id: Uuid) -> Result<bool> {
        let count = organization_members::table
            .filter(organization_members::organization_id.eq(org_id))
            .filter(organization_members::user_id.eq(user_id))
            .filter(organization_members::role.eq("admin"))
            .count()
            .get_result::<i64>(conn)
            .map_err(|e| anyhow!("Error checking organization admin status: {}", e))?;

        Ok(count > 0)
    }

    pub fn list_active(conn: &mut DbConn) -> Result<Vec<Organization>> {
        Self::list_active_pooled(conn)
    }

    pub fn list_active_pooled(conn: &mut PooledConn) -> Result<Vec<Organization>> {
        organizations::table
            .filter(organizations::subscription_status.eq("active"))
            .select(Organization::as_select())
            .load(conn)
            .map_err(|e| anyhow!("Error listing active organizations: {}", e))
    }

    // Commented out due to Numeric type compatibility issues
    // pub fn get_subscription_tier(conn: &mut DbConn, tier_name_param: &str) -> Result<SubscriptionTier> {
    //     use crate::schema::subscription_tiers::dsl::*;
    //
    //     subscription_tiers
    //         .filter(tier_name.eq(tier_name_param))
    //         .filter(is_active.eq(true))
    //         .first::<SubscriptionTier>(&mut **conn)
    //         .map_err(|e| anyhow!("Error getting subscription tier: {}", e))
    // }
    //
    // pub fn list_subscription_tiers(conn: &mut DbConn) -> Result<Vec<SubscriptionTier>> {
    //     use crate::schema::subscription_tiers::dsl::*;
    //
    //     subscription_tiers
    //         .filter(is_active.eq(true))
    //         .order_by(sort_order.asc())
    //         .load::<SubscriptionTier>(&mut **conn)
    //         .map_err(|e| anyhow!("Error listing subscription tiers: {}", e))
    // }

    // Stripe-related methods
    pub fn find_by_stripe_customer_id(conn: &mut DbConn, customer_id: &str) -> Result<Organization> {
        Self::find_by_stripe_customer_id_pooled(conn, customer_id)
    }

    pub fn find_by_stripe_customer_id_pooled(conn: &mut PooledConn, customer_id: &str) -> Result<Organization> {
        organizations::table
            .filter(organizations::stripe_customer_id.eq(customer_id))
            .select(Organization::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding organization by Stripe customer ID: {}", e))
    }

    pub fn find_by_stripe_subscription_id(conn: &mut DbConn, subscription_id: &str) -> Result<Vec<Organization>> {
        Self::find_by_stripe_subscription_id_pooled(conn, subscription_id)
    }

    pub fn find_by_stripe_subscription_id_pooled(conn: &mut PooledConn, subscription_id: &str) -> Result<Vec<Organization>> {
        // Note: stripe_subscription_id doesn't exist in the schema
        // This is a placeholder implementation
        organizations::table
            .filter(organizations::stripe_customer_id.eq(subscription_id)) // Using customer_id as a workaround
            .select(Organization::as_select())
            .load(conn)
            .map_err(|e| anyhow!("Error finding organization by Stripe subscription ID: {}", e))
    }

    pub fn update_subscription_status(
        conn: &mut DbConn,
        id: Uuid,
        status: &str,
        billing_period_end: Option<DateTime<Utc>>
    ) -> Result<Organization> {
        Self::update_subscription_status_pooled(conn, id, status, billing_period_end)
    }

    pub fn update_subscription_status_pooled(
        conn: &mut PooledConn,
        id: Uuid,
        status: &str,
        billing_period_end: Option<DateTime<Utc>>
    ) -> Result<Organization> {
        let update = UpdateOrganization {
            name: None,
            slug: None,
            subscription_tier: None,
            subscription_status: Some(status.to_string()),
            description: None,
            logo_url: None,
            website: None,
            updated_at: Some(Utc::now()),
            stripe_customer_id: None,
            stripe_subscription_id: None,
            subscription_tier_id: None,
            subscription_auto_renew: None,
            billing_period_start: None,
            billing_period_end: billing_period_end,
        };

        diesel::update(organizations::table.find(id))
            .set(update)
            .returning(Organization::as_select())
            .get_result(conn)
            .map_err(|e| anyhow!("Error updating organization subscription status: {}", e))
    }
}
