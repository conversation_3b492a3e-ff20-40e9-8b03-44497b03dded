use anyhow::{anyhow, Result};
use chrono::Utc;
use diesel::prelude::*;
use uuid::Uuid;

use crate::db::DbConn;
use crate::models::subscription_event::{NewSubscriptionEvent, SubscriptionEvent};
use crate::schema::subscription_events;

pub struct SubscriptionEventRepository;

impl SubscriptionEventRepository {
    pub fn create(conn: &mut DbConn, new_event: &NewSubscriptionEvent) -> Result<SubscriptionEvent> {
        diesel::insert_into(subscription_events::table)
            .values(new_event)
            .get_result::<SubscriptionEvent>(conn)
            .map_err(|e| anyhow!("Error creating subscription event: {}", e))
    }

    pub fn find_by_organization(conn: &mut DbConn, org_id: Uuid, limit: i64) -> Result<Vec<SubscriptionEvent>> {
        subscription_events::table
            .filter(subscription_events::organization_id.eq(org_id))
            .order(subscription_events::created_at.desc())
            .limit(limit)
            .load::<SubscriptionEvent>(conn)
            .map_err(|e| anyhow!("Error finding subscription events: {}", e))
    }

    pub fn find_by_id(conn: &mut DbConn, event_id: Uuid) -> Result<SubscriptionEvent> {
        subscription_events::table
            .find(event_id)
            .first::<SubscriptionEvent>(conn)
            .map_err(|e| anyhow!("Error finding subscription event: {}", e))
    }

    pub fn record_subscription_created(
        conn: &mut DbConn,
        org_id: Uuid,
        subscription_id: &str,
        plan_name: &str,
        price: f64,
        currency: &str,
    ) -> Result<SubscriptionEvent> {
        let details = serde_json::json!({
            "subscription_id": subscription_id,
            "plan_name": plan_name,
            "amount": price,
            "currency": currency,
            "status": "active"
        });

        let new_event = NewSubscriptionEvent {
            organization_id: org_id,
            event_type: "subscription_created".to_string(),
            details,
        };

        Self::create(conn, &new_event)
    }

    pub fn record_subscription_updated(
        conn: &mut DbConn,
        org_id: Uuid,
        subscription_id: &str,
        plan_name: &str,
        status: &str,
    ) -> Result<SubscriptionEvent> {
        let details = serde_json::json!({
            "subscription_id": subscription_id,
            "plan_name": plan_name,
            "status": status
        });

        let new_event = NewSubscriptionEvent {
            organization_id: org_id,
            event_type: "subscription_updated".to_string(),
            details,
        };

        Self::create(conn, &new_event)
    }

    pub fn record_payment_succeeded(
        conn: &mut DbConn,
        org_id: Uuid,
        subscription_id: &str,
        plan_name: &str,
        amount: f64,
        currency: &str,
    ) -> Result<SubscriptionEvent> {
        let details = serde_json::json!({
            "subscription_id": subscription_id,
            "plan_name": plan_name,
            "amount": amount,
            "currency": currency
        });

        let new_event = NewSubscriptionEvent {
            organization_id: org_id,
            event_type: "payment_succeeded".to_string(),
            details,
        };

        Self::create(conn, &new_event)
    }

    pub fn record_payment_failed(
        conn: &mut DbConn,
        org_id: Uuid,
        subscription_id: &str,
        plan_name: &str,
        amount: f64,
        currency: &str,
        error: &str,
    ) -> Result<SubscriptionEvent> {
        let details = serde_json::json!({
            "subscription_id": subscription_id,
            "plan_name": plan_name,
            "amount": amount,
            "currency": currency,
            "error": error
        });

        let new_event = NewSubscriptionEvent {
            organization_id: org_id,
            event_type: "payment_failed".to_string(),
            details,
        };

        Self::create(conn, &new_event)
    }

    pub fn record_subscription_canceled(
        conn: &mut DbConn,
        org_id: Uuid,
        subscription_id: &str,
        plan_name: &str,
    ) -> Result<SubscriptionEvent> {
        let details = serde_json::json!({
            "subscription_id": subscription_id,
            "plan_name": plan_name,
            "status": "canceled"
        });

        let new_event = NewSubscriptionEvent {
            organization_id: org_id,
            event_type: "subscription_canceled".to_string(),
            details,
        };

        Self::create(conn, &new_event)
    }
}
