use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};

use crate::db::DbConn;
use crate::models::user::{User, NewUser, UpdateUser};
use crate::schema::users;

pub struct UserRepository;

impl UserRepository {
    pub fn find_by_id(conn: &mut DbConn, id: Uuid) -> Result<User> {
        users::table
            .find(id)
            .select(User::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding user by ID: {}", e))
    }

    pub fn find_by_email(conn: &mut DbConn, email: &str) -> Result<User> {
        users::table
            .filter(users::email.eq(email))
            .select(User::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding user by email: {}", e))
    }

    // Note: username field doesn't exist in the schema
    // This is a placeholder implementation that will always return an error
    pub fn find_by_username(conn: &mut DbConn, username: &str) -> Result<User> {
        Err(anyhow!("Username field does not exist in the schema"))
    }

    pub fn create(conn: &mut DbConn, new_user: &NewUser) -> Result<User> {
        diesel::insert_into(users::table)
            .values(new_user)
            .returning(User::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error creating user: {}", e))
    }

    pub fn update(conn: &mut DbConn, id: Uuid, update_user: &UpdateUser) -> Result<User> {
        diesel::update(users::table.find(id))
            .set(update_user)
            .returning(User::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error updating user: {}", e))
    }

    pub fn delete(conn: &mut DbConn, id: Uuid) -> Result<usize> {
        diesel::delete(users::table.find(id))
            .execute(&mut **conn)
            .map_err(|e| anyhow!("Error deleting user: {}", e))
    }

    pub fn list(conn: &mut DbConn, limit: i64, offset: i64) -> Result<Vec<User>> {
        users::table
            .limit(limit)
            .offset(offset)
            .select(User::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing users: {}", e))
    }
}
