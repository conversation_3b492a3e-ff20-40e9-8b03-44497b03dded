use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};

use crate::db::DbConn;
use crate::models::locale::{Locale, NewLocale};
use crate::schema::locales;

pub struct LocaleRepository;

impl LocaleRepository {
    pub fn find_by_id(conn: &mut DbConn, id: Uuid) -> Result<Locale> {
        locales::table
            .find(id)
            .first::<Locale>(&mut **conn)
            .map_err(|e| anyhow!("Error finding locale by ID: {}", e))
    }

    pub fn find_by_code(conn: &mut DbConn, code: &str) -> Result<Locale> {
        locales::table
            .filter(locales::code.eq(code))
            .first::<Locale>(&mut **conn)
            .map_err(|e| anyhow!("Error finding locale by code: {}", e))
    }

    pub fn create(conn: &mut DbConn, new_locale: &NewLocale) -> Result<Locale> {
        diesel::insert_into(locales::table)
            .values(new_locale)
            .get_result::<Locale>(&mut **conn)
            .map_err(|e| anyhow!("Error creating locale: {}", e))
    }

    pub fn list_all(conn: &mut DbConn) -> Result<Vec<Locale>> {
        locales::table
            .order_by(locales::name.asc())
            .load::<Locale>(&mut **conn)
            .map_err(|e| anyhow!("Error listing locales: {}", e))
    }

    pub fn list_by_codes(conn: &mut DbConn, codes: &[&str]) -> Result<Vec<Locale>> {
        locales::table
            .filter(locales::code.eq_any(codes))
            .order_by(locales::name.asc())
            .load::<Locale>(&mut **conn)
            .map_err(|e| anyhow!("Error listing locales by codes: {}", e))
    }
}
