use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};
use chrono::Utc;
use rand::{thread_rng, Rng};
use rand::distributions::Alphanumeric;
use sha2::{Sha256, Digest};
use base64::{Engine as _, engine::general_purpose};
use serde_json::Map;

use crate::auth::permissions;
use crate::db::DbConn;
use crate::models::api_key::{ApiKey, NewApiKey, ApiKeyResponse};
use crate::models::permission_group::PermissionGroup;
use crate::repositories::permission_group_repository::PermissionGroupRepository;
use crate::schema::{api_keys, permission_groups};

pub struct ApiKeyRepository;

impl ApiKeyRepository {
    // Generate a new API key
    pub fn generate_api_key() -> (String, String) {
        // Generate a random API key
        let key: String = thread_rng()
            .sample_iter(&Alphanumeric)
            .take(32)
            .map(char::from)
            .collect();

        // Create a prefix (first 8 characters)
        let prefix = key[0..8].to_string();

        (key, prefix)
    }

    // Hash an API key for storage
    pub fn hash_api_key(key: &str) -> String {
        let mut hasher = Sha256::new();
        hasher.update(key.as_bytes());
        let result = hasher.finalize();
        general_purpose::STANDARD.encode(result)
    }

    // Validate permissions
    pub fn validate_permissions(permission_keys: &[String]) -> Result<()> {
        // Validate that all permissions exist
        for key in permission_keys {
            if permissions::get_permission(key).is_none() {
                return Err(anyhow!("Invalid permission: {}", key));
            }
        }

        Ok(())
    }

    // Convert permission keys to a JSON object
    pub fn permissions_to_json(permission_keys: &[String]) -> Result<serde_json::Value> {
        // Validate permissions first
        Self::validate_permissions(permission_keys)?;

        // Convert to JSON object
        let mut perm_map = Map::new();
        for key in permission_keys {
            perm_map.insert(key.clone(), serde_json::Value::Bool(true));
        }

        Ok(serde_json::Value::Object(perm_map))
    }

    // Create a new API key
    pub fn create_api_key(
        conn: &mut DbConn,
        organization_id: Uuid,
        name: &str,
        permissions_json: serde_json::Value,
        rate_limit: Option<i32>,
        rate_limit_period: Option<&str>,
        expires_at: Option<chrono::DateTime<Utc>>,
        created_by: Uuid,
        permission_group_id: Option<String>,
    ) -> Result<(ApiKey, String)> {
        // Generate a new API key
        let (key, prefix) = Self::generate_api_key();
        let key_hash = Self::hash_api_key(&key);

        // Extract permission keys from the JSON object
        let permission_keys: Vec<String> = match permissions_json.as_object() {
            Some(obj) => obj.keys().cloned().collect(),
            None => return Err(anyhow!("Invalid permissions format")),
        };

        // Validate permissions
        Self::validate_permissions(&permission_keys)?;

        // Create a mutable copy of the permissions JSON
        let mut permissions = permissions_json.clone();

        // Add rate limit period to permissions if provided
        if let Some(period) = rate_limit_period {
            if let Some(obj) = permissions.as_object_mut() {
                obj.insert("rate_limit_period".to_string(), serde_json::Value::String(period.to_string()));
            }
        }

        // Parse permission group ID if provided
        let group_id = if let Some(id_str) = permission_group_id {
            match Uuid::parse_str(&id_str) {
                Ok(id) => Some(id),
                Err(_) => return Err(anyhow!("Invalid permission group ID format")),
            }
        } else {
            None
        };

        // Create a new API key record
        let new_api_key = NewApiKey {
            organization_id,
            name: name.to_string(),
            key_hash,
            prefix: prefix.clone(),
            permissions,
            rate_limit,
            expires_at,
            created_by,

        };

        // Insert the new API key
        let api_key = diesel::insert_into(api_keys::table)
            .values(&new_api_key)
            .returning(ApiKey::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error creating API key: {}", e))?;

        Ok((api_key, key))
    }

    // Get effective permissions for an API key (including inherited permissions from group)
    pub fn get_effective_permissions(
        conn: &DbConn,
        api_key: &ApiKey,
    ) -> Result<serde_json::Value> {
        // Start with the API key's own permissions
        let mut effective_permissions = api_key.permissions.clone();

        // If the API key has a permission group, merge in the group's permissions
        // Permission groups are not implemented yet
        if false {
            // For now, we'll just use the API key's permissions
            // In a real implementation, we would fetch the permission group and merge its permissions
            // This is a temporary workaround for the type mismatch issue
        }

        Ok(effective_permissions)
    }

    // Get API key with permission group details
    pub fn get_api_key_with_group(
        conn: &mut DbConn,
        id: Uuid,
    ) -> Result<ApiKeyResponse> {
        // Get the API key
        let api_key = Self::get_api_key_by_id(conn, id)?;

        // Convert to response
        let mut response: ApiKeyResponse = api_key.clone().into();

        // If the API key has a permission group, get the group details
        // Permission groups are not implemented yet
        if false {
            // Permission groups are not implemented yet
            response.permission_group_id = None;
            response.permission_group_name = None;
        }

        // Get effective permissions
        let effective_permissions = Self::get_effective_permissions(conn, &api_key)?;

        // Update permissions in the response
        if let Some(obj) = effective_permissions.as_object() {
            response.permissions = obj.keys()
                .filter(|k| *k != "rate_limit_period") // Filter out internal fields
                .map(|k| k.to_string())
                .collect();
        }

        Ok(response)
    }

    // List API keys for an organization
    pub fn list_api_keys_by_organization(
        conn: &mut DbConn,
        organization_id: Uuid,
    ) -> Result<Vec<ApiKey>> {
        api_keys::table
            .filter(api_keys::organization_id.eq(organization_id))
            .filter(api_keys::revoked_at.is_null())
            .order_by(api_keys::created_at.desc())
            .select(ApiKey::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing API keys: {}", e))
    }

    // List API keys with permission group details for an organization
    pub fn list_api_keys_with_groups(
        conn: &mut DbConn,
        organization_id: Uuid,
    ) -> Result<Vec<ApiKeyResponse>> {
        // Get all API keys for the organization
        let api_keys = Self::list_api_keys_by_organization(conn, organization_id)?;

        // Get all permission groups for the organization
        let groups = PermissionGroupRepository::list_permission_groups_by_organization(conn, organization_id)?;

        // Create a map of group IDs to group names for quick lookup
        let group_map: std::collections::HashMap<Uuid, String> = groups.into_iter()
            .map(|group| (group.id, group.name))
            .collect();

        // Convert API keys to responses with group details
        let mut responses = Vec::new();
        for api_key in api_keys {
            // Convert to response
            let mut response: ApiKeyResponse = api_key.clone().into();

            // Add group details if available
            // Permission groups are not implemented yet
            if false {
                // Permission groups are not implemented yet
                response.permission_group_id = None;
                response.permission_group_name = None;
            }

            // Get effective permissions
            let effective_permissions = Self::get_effective_permissions(conn, &api_key)?;

            // Update permissions in the response
            if let Some(obj) = effective_permissions.as_object() {
                response.permissions = obj.keys()
                    .filter(|k| *k != "rate_limit_period") // Filter out internal fields
                    .map(|k| k.to_string())
                    .collect();
            }

            responses.push(response);
        }

        Ok(responses)
    }

    // Get an API key by ID
    pub fn get_api_key_by_id(
        conn: &mut DbConn,
        id: Uuid,
    ) -> Result<ApiKey> {
        api_keys::table
            .find(id)
            .select(ApiKey::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding API key: {}", e))
    }

    // Revoke an API key
    pub fn revoke_api_key(
        conn: &mut DbConn,
        id: Uuid,
    ) -> Result<ApiKey> {
        diesel::update(api_keys::table.find(id))
            .set(api_keys::revoked_at.eq(Some(Utc::now())))
            .returning(ApiKey::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error revoking API key: {}", e))
    }

    // Validate an API key
    pub fn validate_api_key(
        conn: &mut DbConn,
        key: &str,
    ) -> Result<ApiKey> {
        let prefix = key[0..8].to_string();
        let key_hash = Self::hash_api_key(key);

        let api_key = api_keys::table
            .filter(api_keys::prefix.eq(prefix))
            .filter(api_keys::key_hash.eq(key_hash))
            .filter(api_keys::revoked_at.is_null())
            .select(ApiKey::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Invalid API key: {}", e))?;

        // Check if the key has expired
        if let Some(expires_at) = api_key.expires_at {
            if expires_at < Utc::now() {
                return Err(anyhow!("API key has expired"));
            }
        }

        // Update last_used_at
        diesel::update(api_keys::table.find(api_key.id))
            .set(api_keys::last_used_at.eq(Some(Utc::now())))
            .execute(&mut **conn)
            .map_err(|e| anyhow!("Error updating API key last_used_at: {}", e))?;

        Ok(api_key)
    }

    // Get API key usage statistics
    pub fn get_api_key_usage(
        conn: &mut DbConn,
        api_key_id: Uuid,
        days: i32,
    ) -> Result<i64> {
        use crate::repositories::api_call_log_repository::ApiCallLogRepository;
        use chrono::{Duration, Utc};

        let end_time = Utc::now();
        let start_time = end_time - Duration::days(days as i64);

        // Get the actual count from the api_call_logs table
        ApiCallLogRepository::count_api_key_calls(conn, api_key_id, start_time, end_time)
    }

    // Get detailed API key usage statistics by endpoint
    pub fn get_api_key_usage_by_endpoint(
        conn: &mut DbConn,
        api_key_id: Uuid,
        days: i32,
    ) -> Result<Vec<(String, i64)>> {
        use crate::repositories::api_call_log_repository::ApiCallLogRepository;

        // Get the actual endpoint usage from the api_call_logs table
        let endpoint_usage = ApiCallLogRepository::get_api_key_usage_by_endpoint(conn, api_key_id, days)?;

        // Convert to the expected format
        let result = endpoint_usage
            .into_iter()
            .map(|usage| (usage.endpoint, usage.count))
            .collect();

        Ok(result)
    }

    // Get API key usage statistics by day
    pub fn get_api_key_usage_by_day(
        conn: &mut DbConn,
        api_key_id: Uuid,
        days: i32,
    ) -> Result<Vec<(String, i64)>> {
        use crate::repositories::api_call_log_repository::ApiCallLogRepository;

        // Get the actual daily usage from the api_call_logs table
        let daily_usage = ApiCallLogRepository::get_api_key_usage_by_day(conn, api_key_id, days)?;

        // Convert to the expected format
        let result = daily_usage
            .into_iter()
            .map(|usage| (usage.date, usage.count))
            .collect();

        Ok(result)
    }
}
