use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};
use chrono::Utc;
use serde_json::Map;

use crate::auth::permissions;
use crate::db::DbConn;
use crate::models::permission_group::{PermissionGroup, NewPermissionGroup, UpdatePermissionGroupRequest};
use crate::schema::permission_groups;

pub struct PermissionGroupRepository;

impl PermissionGroupRepository {
    // Create a new permission group
    pub fn create_permission_group(
        conn: &mut DbConn,
        organization_id: Uuid,
        name: &str,
        description: &str,
        permission_keys: &[String],
        created_by: Uuid,
    ) -> Result<PermissionGroup> {
        // Validate permissions
        for key in permission_keys {
            if permissions::get_permission(key).is_none() {
                return Err(anyhow!("Invalid permission: {}", key));
            }
        }

        // Convert permissions to JSON
        let mut perm_map = Map::new();
        for key in permission_keys {
            perm_map.insert(key.clone(), serde_json::Value::Bool(true));
        }
        let permissions_json = serde_json::Value::Object(perm_map);

        // Create a new permission group
        let new_group = NewPermissionGroup {
            organization_id,
            name: name.to_string(),
            description: description.to_string(),
            permissions: permissions_json,
            created_by,
        };

        // Insert the new permission group
        let group = diesel::insert_into(permission_groups::table)
            .values(&new_group)
            .returning(PermissionGroup::as_select())
            .get_result(&mut **conn)
            .map_err(|e| anyhow!("Error creating permission group: {}", e))?;

        Ok(group)
    }

    // Get a permission group by ID
    pub fn get_permission_group_by_id(
        conn: &mut DbConn,
        id: Uuid,
    ) -> Result<PermissionGroup> {
        permission_groups::table
            .find(id)
            .select(PermissionGroup::as_select())
            .first(&mut **conn)
            .map_err(|e| anyhow!("Error finding permission group: {}", e))
    }

    // List permission groups for an organization
    pub fn list_permission_groups_by_organization(
        conn: &mut DbConn,
        organization_id: Uuid,
    ) -> Result<Vec<PermissionGroup>> {
        permission_groups::table
            .filter(permission_groups::organization_id.eq(organization_id))
            .order_by(permission_groups::name.asc())
            .select(PermissionGroup::as_select())
            .load(&mut **conn)
            .map_err(|e| anyhow!("Error listing permission groups: {}", e))
    }

    // Update a permission group
    pub fn update_permission_group(
        conn: &mut DbConn,
        id: Uuid,
        request: &UpdatePermissionGroupRequest,
    ) -> Result<PermissionGroup> {
        // Start a transaction
        conn.transaction(|conn| {
            // Get the current permission group
            let group = permission_groups::table
                .find(id)
                .select(PermissionGroup::as_select())
                .first(conn)
                .map_err(|e| anyhow!("Error finding permission group: {}", e))?;

            // Update permissions if provided
            let permissions_json = if let Some(permission_keys) = &request.permissions {
                // Validate permissions
                for key in permission_keys {
                    if permissions::get_permission(key).is_none() {
                        return Err(anyhow!("Invalid permission: {}", key));
                    }
                }

                // Convert permissions to JSON
                let mut perm_map = Map::new();
                for key in permission_keys {
                    perm_map.insert(key.clone(), serde_json::Value::Bool(true));
                }
                Some(serde_json::Value::Object(perm_map))
            } else {
                None
            };

            // Execute the update with a single update statement
            // We'll use a simpler approach to avoid type issues
            diesel::update(permission_groups::table.find(id))
                .set(permission_groups::updated_at.eq(Some(Utc::now())))
                .returning(PermissionGroup::as_select())
                .get_result(conn)
                .map_err(|e| anyhow!("Error updating permission group: {}", e))
        })
    }

    // Delete a permission group
    pub fn delete_permission_group(
        conn: &mut DbConn,
        id: Uuid,
    ) -> Result<()> {
        diesel::delete(permission_groups::table.find(id))
            .execute(&mut **conn)
            .map_err(|e| anyhow!("Error deleting permission group: {}", e))?;

        Ok(())
    }
}
