use diesel::prelude::*;
use uuid::Uuid;
use anyhow::{anyhow, Result};
use chrono::{Utc, Datelike, DateTime};

use crate::db::{DbConn, PooledConn};
use crate::models::ai_credit::{
    AICreditTransaction, NewAICreditTransaction,
    AICreditUsage, NewAICreditUsage,
    AICreditPricing, AICreditBalance
};
use crate::models::organization::Organization;
use crate::schema::{ai_credit_transactions, ai_credit_usage, ai_credit_pricing, organizations};

pub struct AICreditRepository;

impl AICreditRepository {
    // Transaction methods
    pub fn create_transaction(conn: &mut DbConn, new_transaction: &NewAICreditTransaction) -> Result<AICreditTransaction> {
        Self::create_transaction_pooled(conn, new_transaction)
    }

    pub fn create_transaction_pooled(conn: &mut PooledConn, new_transaction: &NewAICreditTransaction) -> Result<AICreditTransaction> {
        diesel::insert_into(ai_credit_transactions::table)
            .values(new_transaction)
            .get_result::<AICreditTransaction>(conn)
            .map_err(|e| anyhow!("Error creating AI credit transaction: {}", e))
    }

    pub fn find_transaction_by_id(conn: &mut DbConn, id: Uuid) -> Result<AICreditTransaction> {
        Self::find_transaction_by_id_pooled(conn, id)
    }

    pub fn find_transaction_by_id_pooled(conn: &mut PooledConn, id: Uuid) -> Result<AICreditTransaction> {
        ai_credit_transactions::table
            .find(id)
            .select(AICreditTransaction::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding AI credit transaction by ID: {}", e))
    }

    pub fn list_transactions_by_organization(
        conn: &mut DbConn,
        org_id: Uuid,
        limit: i64,
        offset: i64
    ) -> Result<Vec<AICreditTransaction>> {
        Self::list_transactions_by_organization_pooled(conn, org_id, limit, offset)
    }

    pub fn list_transactions_by_organization_pooled(
        conn: &mut PooledConn,
        org_id: Uuid,
        limit: i64,
        offset: i64
    ) -> Result<Vec<AICreditTransaction>> {
        ai_credit_transactions::table
            .filter(ai_credit_transactions::organization_id.eq(org_id))
            .order_by(ai_credit_transactions::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(AICreditTransaction::as_select())
            .load(conn)
            .map_err(|e| anyhow!("Error listing AI credit transactions: {}", e))
    }

    // Usage methods
    pub fn create_usage(conn: &mut DbConn, new_usage: &NewAICreditUsage) -> Result<AICreditUsage> {
        Self::create_usage_pooled(conn, new_usage)
    }

    pub fn create_usage_pooled(conn: &mut PooledConn, new_usage: &NewAICreditUsage) -> Result<AICreditUsage> {
        diesel::insert_into(ai_credit_usage::table)
            .values(new_usage)
            .returning(AICreditUsage::as_select())
            .get_result(conn)
            .map_err(|e| anyhow!("Error creating AI credit usage: {}", e))
    }

    pub fn find_usage_by_id(conn: &mut DbConn, id: Uuid) -> Result<AICreditUsage> {
        Self::find_usage_by_id_pooled(conn, id)
    }

    pub fn find_usage_by_id_pooled(conn: &mut PooledConn, id: Uuid) -> Result<AICreditUsage> {
        ai_credit_usage::table
            .find(id)
            .select(AICreditUsage::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding AI credit usage by ID: {}", e))
    }

    pub fn list_usage_by_organization(
        conn: &mut DbConn,
        org_id: Uuid,
        limit: i64,
        offset: i64
    ) -> Result<Vec<AICreditUsage>> {
        Self::list_usage_by_organization_pooled(conn, org_id, limit, offset)
    }

    pub fn list_usage_by_organization_pooled(
        conn: &mut PooledConn,
        org_id: Uuid,
        limit: i64,
        offset: i64
    ) -> Result<Vec<AICreditUsage>> {
        ai_credit_usage::table
            .filter(ai_credit_usage::organization_id.eq(org_id))
            .order_by(ai_credit_usage::created_at.desc())
            .limit(limit)
            .offset(offset)
            .select(AICreditUsage::as_select())
            .load(conn)
            .map_err(|e| anyhow!("Error listing AI credit usage: {}", e))
    }

    // Pricing methods
    pub fn list_pricing_tiers(conn: &mut DbConn) -> Result<Vec<AICreditPricing>> {
        Self::list_pricing_tiers_pooled(conn)
    }

    pub fn list_pricing_tiers_pooled(conn: &mut PooledConn) -> Result<Vec<AICreditPricing>> {
        use diesel::sql_query;
        use diesel::sql_types::{Uuid as SqlUuid, Text, Integer, Double, Bool, Nullable, Timestamptz};

        // Debug: Print a message to indicate we're trying to list pricing tiers
        eprintln!("Attempting to list AI credit pricing tiers");

        let query = sql_query(r#"
            SELECT
                id, min_credits, max_credits, price_per_credit, currency, is_active, created_at, updated_at
            FROM ai_credit_pricing
            WHERE is_active IS NULL OR is_active = true
            ORDER BY min_credits ASC
        "#);

        #[derive(QueryableByName, Debug)]
        struct PricingRow {
            #[diesel(sql_type = SqlUuid)]
            id: Uuid,
            #[diesel(sql_type = Integer)]
            min_credits: i32,
            #[diesel(sql_type = Nullable<Integer>)]
            max_credits: Option<i32>,
            #[diesel(sql_type = Double)]
            price_per_credit: f64,
            #[diesel(sql_type = Text)]
            currency: String,
            #[diesel(sql_type = Nullable<Bool>)]
            is_active: Option<bool>,
            #[diesel(sql_type = Nullable<Timestamptz>)]
            created_at: Option<DateTime<Utc>>,
            #[diesel(sql_type = Nullable<Timestamptz>)]
            updated_at: Option<DateTime<Utc>>,
        }

        let results = match query.load::<PricingRow>(conn) {
            Ok(rows) => {
                eprintln!("Successfully loaded {} pricing tiers", rows.len());
                rows
            },
            Err(e) => {
                eprintln!("Error loading pricing tiers: {}", e);
                return Err(anyhow!("Error listing AI credit pricing tiers: {}", e));
            }
        };

        // If no pricing tiers are found, create a default one
        if results.is_empty() {
            eprintln!("No pricing tiers found, returning default tier");
            // Return a default pricing tier
            return Ok(vec![AICreditPricing {
                id: Uuid::new_v4(),
                min_credits: 100,
                max_credits: None,
                price_per_credit: 0.01,
                currency: "USD".to_string(),
                is_active: Some(true),
                created_at: Some(Utc::now()),
                updated_at: Some(Utc::now()),
            }]);
        }

        let pricing_tiers: Vec<AICreditPricing> = results
            .into_iter()
            .map(|row| {
                AICreditPricing {
                    id: row.id,
                    min_credits: row.min_credits,
                    max_credits: row.max_credits,
                    price_per_credit: row.price_per_credit,
                    currency: row.currency,
                    is_active: row.is_active,
                    created_at: row.created_at,
                    updated_at: row.updated_at,
                }
            })
            .collect();

        eprintln!("Returning {} pricing tiers", pricing_tiers.len());
        Ok(pricing_tiers)
    }

    pub fn find_pricing_tier_for_amount(conn: &mut DbConn, amount: i32) -> Result<AICreditPricing> {
        Self::find_pricing_tier_for_amount_pooled(conn, amount)
    }

    pub fn find_pricing_tier_for_amount_pooled(conn: &mut PooledConn, amount: i32) -> Result<AICreditPricing> {
        use diesel::sql_query;
        use diesel::sql_types::{Uuid as SqlUuid, Text, Integer, Double, Bool, Nullable, Timestamptz};

        eprintln!("Finding pricing tier for amount: {}", amount);

        // Try to get a pricing tier that matches the amount
        let query = sql_query(r#"
            SELECT
                id, min_credits, max_credits, price_per_credit, currency, is_active, created_at, updated_at
            FROM ai_credit_pricing
            WHERE (is_active IS NULL OR is_active = true)
            AND (min_credits <= $1 OR min_credits IS NULL)
            AND (max_credits >= $1 OR max_credits IS NULL)
            ORDER BY price_per_credit ASC
            LIMIT 1
        "#)
        .bind::<Integer, _>(amount);

        #[derive(QueryableByName, Debug)]
        struct PricingRow {
            #[diesel(sql_type = SqlUuid)]
            id: Uuid,
            #[diesel(sql_type = Integer)]
            min_credits: i32,
            #[diesel(sql_type = Nullable<Integer>)]
            max_credits: Option<i32>,
            #[diesel(sql_type = Double)]
            price_per_credit: f64,
            #[diesel(sql_type = Text)]
            currency: String,
            #[diesel(sql_type = Nullable<Bool>)]
            is_active: Option<bool>,
            #[diesel(sql_type = Nullable<Timestamptz>)]
            created_at: Option<DateTime<Utc>>,
            #[diesel(sql_type = Nullable<Timestamptz>)]
            updated_at: Option<DateTime<Utc>>,
        }

        let pricing_tier_result = query.get_result::<PricingRow>(conn);

        match pricing_tier_result {
            Ok(row) => {
                eprintln!("Found pricing tier with ID: {}", row.id);
                Ok(AICreditPricing {
                    id: row.id,
                    min_credits: row.min_credits,
                    max_credits: row.max_credits,
                    price_per_credit: row.price_per_credit,
                    currency: row.currency,
                    is_active: row.is_active,
                    created_at: row.created_at,
                    updated_at: row.updated_at,
                })
            },
            Err(e) => {
                eprintln!("Error finding pricing tier: {}", e);

                // If no tier is found, try to get any tier
                let fallback_query = sql_query(r#"
                    SELECT
                        id, min_credits, max_credits, price_per_credit, currency, is_active, created_at, updated_at
                    FROM ai_credit_pricing
                    WHERE (is_active IS NULL OR is_active = true)
                    ORDER BY min_credits ASC
                    LIMIT 1
                "#);

                let fallback_result = fallback_query.get_result::<PricingRow>(conn);

                match fallback_result {
                    Ok(row) => {
                        eprintln!("Using fallback pricing tier with ID: {}", row.id);
                        Ok(AICreditPricing {
                            id: row.id,
                            min_credits: row.min_credits,
                            max_credits: row.max_credits,
                            price_per_credit: row.price_per_credit,
                            currency: row.currency,
                            is_active: row.is_active,
                            created_at: row.created_at,
                            updated_at: row.updated_at,
                        })
                    },
                    Err(_) => {
                        // If still no tier is found, return a default tier
                        eprintln!("No pricing tiers found, returning default tier");
                        Ok(AICreditPricing {
                            id: Uuid::new_v4(),
                            min_credits: 100,
                            max_credits: None,
                            price_per_credit: 0.01,
                            currency: "USD".to_string(),
                            is_active: Some(true),
                            created_at: Some(Utc::now()),
                            updated_at: Some(Utc::now()),
                        })
                    }
                }
            }
        }
    }

    // Credit balance methods
    pub fn get_organization_credits(conn: &mut DbConn, org_id: Uuid) -> Result<AICreditBalance> {
        Self::get_organization_credits_pooled(conn, org_id)
    }

    pub fn get_organization_credits_pooled(conn: &mut PooledConn, org_id: Uuid) -> Result<AICreditBalance> {
        let organization = organizations::table
            .find(org_id)
            .select(Organization::as_select())
            .first(conn)
            .map_err(|e| anyhow!("Error finding organization: {}", e))?;

        // Calculate total credits used
        let total_used = ai_credit_usage::table
            .filter(ai_credit_usage::organization_id.eq(org_id))
            .filter(
                ai_credit_usage::created_at.ge(
                    Utc::now().with_day(1).unwrap() // First day of current month
                )
            )
            .select(diesel::dsl::sum(ai_credit_usage::credits_used))
            .first::<Option<i64>>(conn)
            .map_err(|e| anyhow!("Error calculating total credits used: {}", e))?
            .unwrap_or(0) as i32;

        // Get subscription tier information
        let subscription_tier_id = organization.subscription_tier_id;
        let subscription_tier_name = match subscription_tier_id {
            Some(tier_id) => {
                use crate::schema::subscription_tiers;
                subscription_tiers::table
                    .find(tier_id)
                    .select(subscription_tiers::tier_name)
                    .first::<String>(conn)
                    .unwrap_or_else(|_| "Free".to_string())
            },
            None => "Free".to_string()
        };

        // For now, hardcode these values until we implement proper credit tracking
        let monthly_allowance = 1000; // Default monthly allowance
        let remaining = monthly_allowance - total_used;

        Ok(AICreditBalance {
            total: monthly_allowance,
            used: total_used,
            remaining,
            reset_date: Some(Utc::now().with_day(1).unwrap().checked_add_months(chrono::Months::new(1)).unwrap()),
            subscription_tier: subscription_tier_name,
            monthly_allowance,
        })
    }

    // Update organization credits
    pub fn update_organization_credits(
        conn: &mut DbConn,
        org_id: Uuid,
        credits_change: i32,
        transaction_type: &str,
        description: Option<&str>,
        created_by: Option<Uuid>,
        stripe_payment_intent_id: Option<&str>,
        stripe_invoice_id: Option<&str>,
    ) -> Result<(AICreditTransaction, i32)> {
        Self::update_organization_credits_pooled(
            conn,
            org_id,
            credits_change,
            transaction_type,
            description,
            created_by,
            stripe_payment_intent_id,
            stripe_invoice_id,
        )
    }

    // Update organization credits with pooled connection
    pub fn update_organization_credits_pooled(
        conn: &mut PooledConn,
        org_id: Uuid,
        credits_change: i32,
        transaction_type: &str,
        description: Option<&str>,
        created_by: Option<Uuid>,
        stripe_payment_intent_id: Option<&str>,
        stripe_invoice_id: Option<&str>,
    ) -> Result<(AICreditTransaction, i32)> {
        // Start a transaction
        conn.transaction(|conn| {
            // Get the organization
            let organization = organizations::table
                .find(org_id)
                .select(Organization::as_select())
                .first(conn)
                .map_err(|e| anyhow!("Error finding organization: {}", e))?;

            // For now, we'll use a hardcoded value for remaining credits
            // since we've removed the ai_credits_remaining field from the Organization model
            let current_remaining = 1000; // Default value
            let new_remaining = current_remaining + credits_change;

            // In a real implementation, we would store this in a separate table
            // For now, we'll just create the transaction record

            // Create a transaction record
            let new_transaction = NewAICreditTransaction {
                organization_id: org_id,
                amount: credits_change,
                transaction_type: transaction_type.to_string(),
                description: description.map(|s| s.to_string()),
                stripe_payment_intent_id: stripe_payment_intent_id.map(|s| s.to_string()),
                stripe_invoice_id: stripe_invoice_id.map(|s| s.to_string()),
                created_by: created_by,
            };

            let transaction = diesel::insert_into(ai_credit_transactions::table)
                .values(&new_transaction)
                .returning(AICreditTransaction::as_select())
                .get_result(conn)
                .map_err(|e| anyhow!("Error creating AI credit transaction: {}", e))?;

            Ok((transaction, new_remaining))
        })
    }

    // Record credit usage
    pub fn record_credit_usage(
        conn: &mut DbConn,
        org_id: Uuid,
        user_id: Option<Uuid>,
        credits_used: i32,
        operation: &str,
        source_locale: Option<&str>,
        target_locale: Option<&str>,
        text_length: Option<i32>,
        model_used: Option<&str>,
    ) -> Result<(AICreditUsage, i32)> {
        Self::record_credit_usage_pooled(
            conn,
            org_id,
            None,
            user_id,
            credits_used,
            "translation",
            operation,
            source_locale,
            target_locale,
            text_length,
            model_used,
            None,
        )
    }

    // Record credit usage with pooled connection
    pub fn record_credit_usage_pooled(
        conn: &mut PooledConn,
        org_id: Uuid,
        project_id: Option<Uuid>,
        user_id: Option<Uuid>,
        credits_used: i32,
        feature: &str,
        operation: &str,
        source_locale: Option<&str>,
        target_locale: Option<&str>,
        text_length: Option<i32>,
        model_used: Option<&str>,
        metadata: Option<serde_json::Value>,
    ) -> Result<(AICreditUsage, i32)> {
        // Start a transaction
        conn.transaction(|conn| {
            // Get the organization
            let organization = organizations::table
                .find(org_id)
                .select(Organization::as_select())
                .first(conn)
                .map_err(|e| anyhow!("Error finding organization: {}", e))?;

            // For now, we'll use a hardcoded value for remaining credits
            let current_remaining = 1000; // Default value
            if current_remaining < credits_used {
                return Err(anyhow!("Insufficient credits"));
            }

            // Calculate new remaining credits
            let new_remaining = current_remaining - credits_used;

            // Create a usage record with the new schema
            let new_usage = NewAICreditUsage {
                organization_id: org_id,
                user_id,
                credits_used,
                operation: operation.to_string(),
                source_locale: source_locale.map(|s| s.to_string()),
                target_locale: target_locale.map(|s| s.to_string()),
                text_length,
                model_used: model_used.map(|s| s.to_string()),
            };

            let usage = diesel::insert_into(ai_credit_usage::table)
                .values(&new_usage)
                .returning(AICreditUsage::as_select())
                .get_result(conn)
                .map_err(|e| anyhow!("Error creating AI credit usage: {}", e))?;

            // Create a transaction record for the usage with the new schema
            let new_transaction = NewAICreditTransaction {
                organization_id: org_id,
                amount: -credits_used,
                transaction_type: "usage".to_string(),
                description: Some(format!("{} operation", operation)),
                stripe_payment_intent_id: None,
                stripe_invoice_id: None,
                created_by: user_id,
            };

            diesel::insert_into(ai_credit_transactions::table)
                .values(&new_transaction)
                .execute(conn)
                .map_err(|e| anyhow!("Error creating AI credit transaction: {}", e))?;

            Ok((usage, new_remaining))
        })
    }
}
