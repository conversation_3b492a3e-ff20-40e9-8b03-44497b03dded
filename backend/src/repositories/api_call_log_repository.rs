use anyhow::{anyhow, Result};
use chrono::{DateTime, NaiveDate, TimeZone, Utc};
use diesel::prelude::*;
use diesel::sql_query;
use diesel::sql_types::{BigInt, Date, Integer, Text, Uuid as SqlUuid};
use uuid::Uuid;

use crate::db::DbConn;
use crate::models::api_call_log::{ApiCallLog, ApiCallLogEndpointSummary, ApiCallLogStatusSummary, ApiCallLogSummary, NewApiCallLog};
use crate::schema::api_call_logs;

pub struct ApiCallLogRepository;

impl ApiCallLogRepository {
    // Log a new API call
    pub fn log_api_call(
        conn: &mut DbConn,
        new_log: NewApiCallLog,
    ) -> Result<ApiCallLog> {
        diesel::insert_into(api_call_logs::table)
            .values(&new_log)
            .get_result::<ApiCallLog>(&mut **conn)
            .map_err(|e| anyhow!("Error logging API call: {}", e))
    }

    // Get API call logs for a specific API key
    pub fn get_api_key_logs(
        conn: &mut DbConn,
        api_key_id: Uuid,
        limit: i64,
        offset: i64,
    ) -> Result<Vec<ApiCallLog>> {
        api_call_logs::table
            .filter(api_call_logs::api_key_id.eq(api_key_id))
            .order(api_call_logs::created_at.desc())
            .limit(limit)
            .offset(offset)
            .load::<ApiCallLog>(&mut **conn)
            .map_err(|e| anyhow!("Error getting API key logs: {}", e))
    }

    // Count API calls for a specific API key within a time period
    pub fn count_api_key_calls(
        conn: &mut DbConn,
        api_key_id: Uuid,
        start_time: DateTime<Utc>,
        end_time: DateTime<Utc>,
    ) -> Result<i64> {
        api_call_logs::table
            .filter(api_call_logs::api_key_id.eq(api_key_id))
            .filter(api_call_logs::created_at.ge(start_time))
            .filter(api_call_logs::created_at.lt(end_time))
            .count()
            .get_result::<i64>(&mut **conn)
            .map_err(|e| anyhow!("Error counting API key calls: {}", e))
    }

    // Get API call count by day for a specific API key
    pub fn get_api_key_usage_by_day(
        conn: &mut DbConn,
        api_key_id: Uuid,
        days: i32,
    ) -> Result<Vec<ApiCallLogSummary>> {
        #[derive(QueryableByName, Debug)]
        struct DailyUsage {
            #[diesel(sql_type = Text)]
            date: String,
            #[diesel(sql_type = BigInt)]
            count: i64,
        }

        let start_date = Utc::now() - chrono::Duration::days(days as i64);

        let query = sql_query(r#"
            SELECT 
                TO_CHAR(created_at, 'YYYY-MM-DD') as date,
                COUNT(*) as count
            FROM api_call_logs
            WHERE api_key_id = $1
            AND created_at >= $2
            GROUP BY date
            ORDER BY date ASC
        "#)
        .bind::<SqlUuid, _>(api_key_id)
        .bind::<diesel::sql_types::Timestamptz, _>(start_date);

        let results = query
            .get_results::<DailyUsage>(&mut **conn)
            .map_err(|e| anyhow!("Error getting API key usage by day: {}", e))?;

        // Convert to ApiCallLogSummary
        let summaries = results
            .into_iter()
            .map(|usage| ApiCallLogSummary {
                date: usage.date,
                count: usage.count,
            })
            .collect();

        Ok(summaries)
    }

    // Get API call count by endpoint for a specific API key
    pub fn get_api_key_usage_by_endpoint(
        conn: &mut DbConn,
        api_key_id: Uuid,
        days: i32,
    ) -> Result<Vec<ApiCallLogEndpointSummary>> {
        #[derive(QueryableByName, Debug)]
        struct EndpointUsage {
            #[diesel(sql_type = Text)]
            endpoint: String,
            #[diesel(sql_type = BigInt)]
            count: i64,
        }

        let start_date = Utc::now() - chrono::Duration::days(days as i64);

        let query = sql_query(r#"
            SELECT 
                endpoint,
                COUNT(*) as count
            FROM api_call_logs
            WHERE api_key_id = $1
            AND created_at >= $2
            GROUP BY endpoint
            ORDER BY count DESC
        "#)
        .bind::<SqlUuid, _>(api_key_id)
        .bind::<diesel::sql_types::Timestamptz, _>(start_date);

        let results = query
            .get_results::<EndpointUsage>(&mut **conn)
            .map_err(|e| anyhow!("Error getting API key usage by endpoint: {}", e))?;

        // Convert to ApiCallLogEndpointSummary
        let summaries = results
            .into_iter()
            .map(|usage| ApiCallLogEndpointSummary {
                endpoint: usage.endpoint,
                count: usage.count,
            })
            .collect();

        Ok(summaries)
    }

    // Get API call count by status code for a specific API key
    pub fn get_api_key_usage_by_status(
        conn: &mut DbConn,
        api_key_id: Uuid,
        days: i32,
    ) -> Result<Vec<ApiCallLogStatusSummary>> {
        #[derive(QueryableByName, Debug)]
        struct StatusUsage {
            #[diesel(sql_type = Integer)]
            status_code: i32,
            #[diesel(sql_type = BigInt)]
            count: i64,
        }

        let start_date = Utc::now() - chrono::Duration::days(days as i64);

        let query = sql_query(r#"
            SELECT 
                status_code,
                COUNT(*) as count
            FROM api_call_logs
            WHERE api_key_id = $1
            AND created_at >= $2
            GROUP BY status_code
            ORDER BY count DESC
        "#)
        .bind::<SqlUuid, _>(api_key_id)
        .bind::<diesel::sql_types::Timestamptz, _>(start_date);

        let results = query
            .get_results::<StatusUsage>(&mut **conn)
            .map_err(|e| anyhow!("Error getting API key usage by status: {}", e))?;

        // Convert to ApiCallLogStatusSummary
        let summaries = results
            .into_iter()
            .map(|usage| ApiCallLogStatusSummary {
                status_code: usage.status_code,
                count: usage.count,
            })
            .collect();

        Ok(summaries)
    }
}
