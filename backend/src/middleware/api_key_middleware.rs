use std::time::Instant;

use chrono::Utc;
use diesel::prelude::*;
use rocket::{
    fairing::{Fairing, Info, Kind},
    http::Status,
    Data, Request, Response,
};

use crate::{
    auth::permissions,
    db::DbConn,
    models::api_call_log::NewApiCallLog,
    models::api_key::Api<PERSON><PERSON>,
    models::permission_audit_log::NewPermissionAuditLog,
    repositories::{
        api_call_log_repository::ApiCallLogRepository,
        api_key_repository::ApiKeyRepository,
        permission_audit_log_repository::PermissionAuditLogRepository,
    },
    schema::api_keys,
    utils::response::{ApiResponse, ErrorCode},
};

/// Middleware for API key authentication and rate limiting
pub struct ApiKeyMiddleware;

impl ApiKeyMiddleware {
    /// Check if the API key has permission to access the requested endpoint
    fn check_permissions(&self, api_key: &crate::models::api_key::A<PERSON><PERSON><PERSON>, request: &Request<'_>) -> bool {
        let path = request.uri().path();
        let method = request.method().as_str();

        // We can't use local_cache for DbConn because it's not Sync
        // Just return a mock permission check for now
        let conn = None;

        // Get effective permissions (including inherited from group)
        let effective_permissions = if let Some(conn) = conn.as_ref() {
            match ApiKeyRepository::get_effective_permissions(conn, api_key) {
                Ok(perms) => perms,
                Err(_) => api_key.permissions.clone(), // Fall back to API key permissions
            }
        } else {
            api_key.permissions.clone() // No connection, use API key permissions
        };

        // Extract permissions from the effective permissions
        let api_key_permissions = match effective_permissions.as_object() {
            Some(obj) => obj,
            None => return false,
        };

        // Check for admin permission (full access)
        if api_key_permissions.contains_key("admin") {
            // Log the permission check if we have a connection
            if let Some(conn) = conn {
                let _ = self.log_permission_check(
                    &conn,
                    api_key,
                    &path.to_string(),
                    method,
                    "admin",
                    true
                );
            }
            return true;
        }

        // Determine the required permission based on the HTTP method
        let required_action = match method {
            "GET" => permissions::PermissionAction::Read,
            "POST" | "PUT" | "DELETE" => permissions::PermissionAction::Write,
            _ => return false, // Unsupported HTTP method
        };

        // Check for global permissions first
        let global_permission_key = match required_action {
            permissions::PermissionAction::Read => "read",
            permissions::PermissionAction::Write => "write",
            _ => "",
        };

        let has_global_permission = api_key_permissions.contains_key(global_permission_key);

        if has_global_permission {
            // Log the permission check if we have a connection
            if let Some(conn) = conn {
                let _ = self.log_permission_check(
                    &conn,
                    api_key,
                    &path.to_string(),
                    method,
                    global_permission_key,
                    true
                );
            }
            return true;
        }

        // If no global permission, check for resource-specific permissions
        let path_string = path.to_string();
        let resource_type = get_resource_type_from_path(&path_string);
        if let Some(resource) = resource_type {
            let permission_key = match required_action {
                permissions::PermissionAction::Read => format!("{}:read", resource),
                permissions::PermissionAction::Write => format!("{}:write", resource),
                _ => return false,
            };

            let has_permission = api_key_permissions.contains_key(&permission_key);

            // Log the permission check if we have a connection
            if let Some(conn) = conn {
                let _ = self.log_permission_check(
                    &conn,
                    api_key,
                    &path.to_string(),
                    method,
                    &permission_key,
                    has_permission
                );
            }

            return has_permission;
        }

        // No matching resource type found
        // Log the permission check if we have a connection
        if let Some(conn) = conn {
            let _ = self.log_permission_check(
                &conn,
                api_key,
                &path.to_string(),
                method,
                "unknown",
                false
            );
        }

        false
    }

    /// Log a permission check
    fn log_permission_check(
        &self,
        conn: &DbConn,
        api_key: &ApiKey,
        path: &str,
        method: &str,
        permission_key: &str,
        granted: bool,
    ) -> Result<(), anyhow::Error> {
        // Create a new permission audit log
        let new_log = NewPermissionAuditLog {
            api_key_id: api_key.id,
            organization_id: api_key.organization_id,
            resource_path: path.to_string(),
            method: method.to_string(),
            permission_key: permission_key.to_string(),
            granted,
        };

        // Insert the log
        let _ = PermissionAuditLogRepository::log_permission_check(
            conn,
            api_key.id,
            api_key.organization_id,
            path,
            method,
            permission_key,
            granted
        );

        Ok(())
    }
}

/// Extract the resource type from the path
fn get_resource_type_from_path(path: &str) -> Option<&str> {
    // Check for resource types in the path
    if path.contains("/organizations/") {
        return Some("organizations");
    } else if path.contains("/projects/") {
        return Some("projects");
    } else if path.contains("/translations/") {
        return Some("translations");
    } else if path.contains("/locales/") {
        return Some("locales");
    } else if path.contains("/ai-credits/") {
        return Some("ai-credits");
    }

    // No resource type found
    None
}

#[rocket::async_trait]
impl Fairing for ApiKeyMiddleware {
    fn info(&self) -> Info {
        Info {
            name: "API Key Middleware",
            kind: Kind::Request | Kind::Response,
        }
    }

    async fn on_request(&self, request: &mut Request<'_>, _: &mut Data<'_>) {
        // Skip authentication for non-API routes
        if !request.uri().path().starts_with("/api/") {
            return;
        }

        // Skip authentication for authentication routes
        if request.uri().path().starts_with("/api/auth/") {
            return;
        }

        // Skip authentication for webhook routes
        if request.uri().path().starts_with("/api/webhooks/") {
            return;
        }

        // Check for API key in the Authorization header
        let api_key = match request.headers().get_one("X-API-Key") {
            Some(key) => key,
            None => {
                // No API key provided, let the request continue
                // It will be handled by the normal authentication middleware
                return;
            }
        };

        // Store the start time for response time calculation
        request.local_cache(|| Instant::now());

        // Get a database connection
        let mut conn = match request.guard::<DbConn>().await {
            rocket::outcome::Outcome::Success(conn) => conn,
            _ => {
                // Failed to get a database connection
                request.local_cache(|| {
                    ApiResponse::<()>::error(
                        Status::InternalServerError,
                        ErrorCode::InternalError,
                        "Failed to get a database connection",
                    )
                });
                return;
            }
        };

        // Authenticate the API key
        // Find the API key in the database
        let prefix = match api_key.split('_').next() {
            Some(prefix) => prefix,
            None => {
                request.local_cache(|| {
                    ApiResponse::<()>::error(
                        Status::Unauthorized,
                        ErrorCode::Unauthorized,
                        "Invalid API key format",
                    )
                });
                return;
            }
        };

        // Hash the API key
        let key_hash = ApiKeyRepository::hash_api_key(api_key);

        // Find the API key in the database
        match api_keys::table
            .filter(api_keys::prefix.eq(prefix))
            .filter(api_keys::key_hash.eq(key_hash))
            .select(ApiKey::as_select())
            .first(&mut *conn) {
            Ok(api_key) => {
                // Check if the API key is active
                if api_key.revoked_at.is_some() {
                    request.local_cache(|| {
                        ApiResponse::<()>::error(
                            Status::Unauthorized,
                            ErrorCode::Unauthorized,
                            "API key has been revoked",
                        )
                    });
                    return;
                }

                // Check if the API key has expired
                if let Some(expires_at) = api_key.expires_at {
                    if expires_at < Utc::now() {
                        request.local_cache(|| {
                            ApiResponse::<()>::error(
                                Status::Unauthorized,
                                ErrorCode::Unauthorized,
                                "API key has expired",
                            )
                        });
                        return;
                    }
                }

                // Check rate limiting
                if let Some(rate_limit) = api_key.rate_limit {
                    // Get the rate limit period from the permissions
                    let rate_limit_period = match api_key.permissions.get("rate_limit_period") {
                        Some(period) => period.as_str().unwrap_or("minute"),
                        None => "minute",
                    };

                    // Calculate the start time based on the rate limit period
                    let start_time = match rate_limit_period {
                        "minute" => Utc::now() - chrono::Duration::minutes(1),
                        "hour" => Utc::now() - chrono::Duration::hours(1),
                        "day" => Utc::now() - chrono::Duration::days(1),
                        _ => Utc::now() - chrono::Duration::minutes(1), // Default to minute
                    };

                    // Count the number of API calls in the rate limit period
                    match ApiCallLogRepository::count_api_key_calls(
                        &mut conn,
                        api_key.id,
                        start_time,
                        Utc::now(),
                    ) {
                        Ok(count) => {
                            if count >= rate_limit as i64 {
                                request.local_cache(|| {
                                    ApiResponse::<()>::error(
                                        Status::TooManyRequests,
                                        ErrorCode::RateLimitExceeded,
                                        &format!(
                                            "Rate limit of {} requests per {} exceeded",
                                            rate_limit, rate_limit_period
                                        ),
                                    )
                                });
                                return;
                            }
                        }
                        Err(e) => {
                            eprintln!("Error checking rate limit: {}", e);
                            // Continue with the request even if rate limit check fails
                        }
                    }
                }

                // Check permissions for the requested endpoint
                if !self.check_permissions(&api_key, request) {
                    request.local_cache(|| {
                        ApiResponse::<()>::error(
                            Status::Forbidden,
                            ErrorCode::Forbidden,
                            "API key does not have permission to access this resource",
                        )
                    });
                    return;
                }

                // Store the API key in the request local cache for later use
                request.local_cache(|| api_key);
            }
            Err(_) => {
                request.local_cache(|| {
                    ApiResponse::<()>::error(
                        Status::Unauthorized,
                        ErrorCode::Unauthorized,
                        "Invalid API key",
                    )
                });
            }
        }
    }

    async fn on_response<'r>(&self, request: &'r Request<'_>, response: &mut Response<'r>) {
        // Skip logging for non-API routes
        if !request.uri().path().starts_with("/api/") {
            return;
        }

        // Get the API key from the request local cache
        let api_key = match request.local_cache(|| None::<crate::models::api_key::ApiKey>) {
            Some(api_key) => api_key,
            None => return, // No API key, skip logging
        };

        // Get the start time from the request local cache
        let start_time = match request.local_cache(|| None::<Instant>) {
            Some(start_time) => start_time,
            None => return, // No start time, skip logging
        };

        // Calculate the response time
        let response_time = start_time.elapsed().as_millis() as i32;

        // Get a database connection
        let mut conn = match request.guard::<DbConn>().await {
            rocket::outcome::Outcome::Success(conn) => conn,
            _ => return, // Failed to get a database connection
        };

        // Create a new API call log
        let new_log = NewApiCallLog {
            api_key_id: api_key.id,
            organization_id: api_key.organization_id,
            endpoint: request.uri().path().to_string(),
            method: request.method().to_string(),
            status_code: response.status().code as i32,
            response_time_ms: response_time,
            request_size_bytes: None, // TODO: Calculate request size
            response_size_bytes: None, // TODO: Calculate response size
            ip_address: request.client_ip().map(|ip| ip.to_string()),
            user_agent: request.headers().get_one("User-Agent").map(|s| s.to_string()),
        };

        // Log the API call
        if let Err(e) = ApiCallLogRepository::log_api_call(&mut conn, new_log) {
            eprintln!("Error logging API call: {}", e);
        }
    }
}
