use rocket::Request;
use rocket::http::Status;
use rocket::serde::json::<PERSON><PERSON>;
use crate::utils::response::{ApiResponse, ErrorCode};

/// Catch 404 Not Found errors
#[catch(404)]
pub fn not_found(req: &Request) -> <PERSON><PERSON><ApiResponse<()>> {
    let path = req.uri().path();
    ApiResponse::error(
        Status::NotFound,
        ErrorCode::NotFound,
        &format!("The requested resource '{}' was not found", path)
    )
}

/// Catch 422 Unprocessable Entity errors (validation errors)
#[catch(422)]
pub fn unprocessable_entity(_req: &Request) -> Json<ApiResponse<()>> {
    ApiResponse::error(
        Status::UnprocessableEntity,
        ErrorCode::ValidationError,
        "The request data failed validation"
    )
}

/// Catch 500 Internal Server Error
#[catch(500)]
pub fn internal_error(_req: &Request) -> <PERSON><PERSON><ApiResponse<()>> {
    ApiResponse::error(
        Status::InternalServerError,
        ErrorCode::InternalError,
        "An internal server error occurred"
    )
}

/// Catch 401 Unauthorized errors
#[catch(401)]
pub fn unauthorized(_req: &Request) -> Json<ApiResponse<()>> {
    ApiResponse::error(
        Status::Unauthorized,
        ErrorCode::Unauthorized,
        "Authentication is required to access this resource"
    )
}

/// Catch 403 Forbidden errors
#[catch(403)]
pub fn forbidden(_req: &Request) -> Json<ApiResponse<()>> {
    ApiResponse::error(
        Status::Forbidden,
        ErrorCode::Forbidden,
        "You don't have permission to access this resource"
    )
}

/// Catch 400 Bad Request errors
#[catch(400)]
pub fn bad_request(_req: &Request) -> Json<ApiResponse<()>> {
    ApiResponse::error(
        Status::BadRequest,
        ErrorCode::BadRequest,
        "The request was malformed or contains invalid parameters"
    )
}

/// Catch 405 Method Not Allowed errors
#[catch(405)]
pub fn method_not_allowed(req: &Request) -> Json<ApiResponse<()>> {
    let method = req.method();
    let path = req.uri().path();
    ApiResponse::error(
        Status::MethodNotAllowed,
        ErrorCode::BadRequest,
        &format!("The {} method is not allowed for the resource '{}'", method, path)
    )
}

/// Catch 409 Conflict errors
#[catch(409)]
pub fn conflict(_req: &Request) -> Json<ApiResponse<()>> {
    ApiResponse::error(
        Status::Conflict,
        ErrorCode::Conflict,
        "The request conflicts with the current state of the resource"
    )
}

/// Catch 429 Too Many Requests errors
#[catch(429)]
pub fn too_many_requests(_req: &Request) -> Json<ApiResponse<()>> {
    ApiResponse::error(
        Status::TooManyRequests,
        ErrorCode::Custom("TOO_MANY_REQUESTS".to_string()),
        "You have sent too many requests in a short period of time"
    )
}

/// Catch all other errors
#[catch(default)]
pub fn default_catcher(status: Status, _req: &Request) -> Json<ApiResponse<()>> {
    let message = match status.code {
        402 => "Payment required",
        413 => "Payload too large",
        415 => "Unsupported media type",
        _ => "An unexpected error occurred",
    };

    ApiResponse::error(
        status,
        ErrorCode::Custom(format!("ERROR_{}", status.code)),
        message
    )
}
