# Standardized API Response Format

This document describes the standardized API response format used throughout the application.

## Overview

All API responses follow a consistent structure to make client-side handling more predictable and easier to implement. The response format includes information about the success status, HTTP status code, optional messages, data payload, and metadata about the response.

## Response Structure

### Success Response

```json
{
  "success": true,
  "status": 200,
  "data": {
    // Response data goes here
  },
  "meta": {
    "timestamp": "2023-05-20T15:30:45.123Z",
    "request_id": "unique-request-identifier",
    "version": "1.0",
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 100,
      "total_pages": 10
    }
  }
}
```

### Error Response

```json
{
  "success": false,
  "status": 400,
  "message": "Error message",
  "meta": {
    "timestamp": "2023-05-20T15:30:45.123Z",
    "request_id": "unique-request-identifier",
    "version": "1.0"
  }
}
```

### Fields

- `success`: <PERSON><PERSON>an indicating whether the request was successful
- `status`: HTTP status code
- `message`: Error message (only present in error responses)
- `data`: Object containing the response data (only present in successful responses)
- `meta`: Object containing metadata about the response
  - `timestamp`: ISO-8601 timestamp of when the response was generated
  - `request_id`: Unique identifier for the request (useful for debugging and tracking)
  - `version`: API version
  - `pagination`: Optional object with pagination information (only present in paginated responses)
    - `page`: Current page number
    - `per_page`: Number of items per page
    - `total`: Total number of items
    - `total_pages`: Total number of pages

## Usage Examples

### Success Response Example

```json
{
  "success": true,
  "status": 200,
  "data": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "name": "Example Organization",
    "created_at": "2023-01-01T12:00:00Z"
  },
  "meta": {
    "timestamp": "2023-05-20T15:30:45.123Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "version": "1.0"
  }
}
```

### Success Response with Pagination Example

```json
{
  "success": true,
  "status": 200,
  "data": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "name": "Example Organization 1",
      "created_at": "2023-01-01T12:00:00Z"
    },
    {
      "id": "223e4567-e89b-12d3-a456-426614174001",
      "name": "Example Organization 2",
      "created_at": "2023-01-02T12:00:00Z"
    }
  ],
  "meta": {
    "timestamp": "2023-05-20T15:30:45.123Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "version": "1.0",
    "pagination": {
      "page": 1,
      "per_page": 10,
      "total": 25,
      "total_pages": 3
    }
  }
}
```

### Error Response Example

```json
{
  "success": false,
  "status": 400,
  "message": "Invalid input data",
  "meta": {
    "timestamp": "2023-05-20T15:30:45.123Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "version": "1.0"
  }
}
```

### Error Response with Validation Example

```json
{
  "success": false,
  "status": 400,
  "message": "Validation failed: Name is required, Invalid email format",
  "meta": {
    "timestamp": "2023-05-20T15:30:45.123Z",
    "request_id": "550e8400-e29b-41d4-a716-446655440000",
    "version": "1.0"
  }
}
```

## Common Error Codes

- `BAD_REQUEST`: Invalid request parameters or body
- `UNAUTHORIZED`: Authentication required or failed
- `FORBIDDEN`: Authenticated user doesn't have required permissions
- `NOT_FOUND`: Requested resource not found
- `CONFLICT`: Request conflicts with current state (e.g., duplicate resource)
- `VALIDATION_ERROR`: Input validation failed
- `INTERNAL_ERROR`: Server-side error occurred

## Implementation in Rust

The response format is implemented in `src/utils/response.rs` and provides helper functions for creating standardized responses:

```rust
// Success response
ApiResponse::success(data)

// Success with message (message is not shown in the response but kept for backward compatibility)
ApiResponse::success_with_message(data, "Operation successful")

// Success with custom status
ApiResponse::success_with_status(data, Status::Created)

// Success with pagination
ApiResponse::success_with_pagination(data, 1, 10, 100)
// Or using the helper function
paginate(data, 1, 10, 100)

// Error response
ApiResponse::error(Status::BadRequest, ErrorCode::ValidationError, "Invalid input")

// Error with details (details are not shown in the response but kept for backward compatibility)
ApiResponse::error_with_details(
    Status::BadRequest,
    ErrorCode::ValidationError,
    "Invalid input",
    json!({
        "fields": {
            "name": "Name is required"
        }
    })
)

// Validation error
ApiResponse::validation_error(field_errors)
```

## Benefits of This Response Format

1. **Consistency**: All API responses follow the same structure, making client-side handling more predictable
2. **Clear Error Messages**: Provides clear error messages for client-side display
3. **Metadata**: Includes metadata about the response, such as timestamp and request ID for debugging
4. **Pagination Support**: Built-in support for paginated responses
5. **Versioning**: Includes API version information to help with client-side compatibility
6. **Request Tracking**: Includes a unique request ID for tracking and debugging purposes
7. **Simplicity**: Simple structure that's easy to understand and implement on the client side
