use rocket::http::Status;
use serde::{Deserialize, Serialize};
use std::fmt;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ApiError {
    pub code: String,
    pub message: String,
    pub status: u16,
}

impl ApiError {
    pub fn new(code: &str, message: &str, status: Status) -> Self {
        Self {
            code: code.to_string(),
            message: message.to_string(),
            status: status.code,
        }
    }

    pub fn bad_request(message: &str) -> Self {
        Self::new("BAD_REQUEST", message, Status::BadRequest)
    }

    pub fn not_found(message: &str) -> Self {
        Self::new("NOT_FOUND", message, Status::NotFound)
    }

    pub fn unauthorized(message: &str) -> Self {
        Self::new("UNAUTHORIZED", message, Status::Unauthorized)
    }

    pub fn forbidden(message: &str) -> Self {
        Self::new("FORBIDDEN", message, Status::Forbidden)
    }

    pub fn internal_server_error(message: &str) -> Self {
        Self::new("INTERNAL_SERVER_ERROR", message, Status::InternalServerError)
    }
}

impl fmt::Display for ApiError {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}: {}", self.code, self.message)
    }
}

impl std::error::Error for ApiError {}
