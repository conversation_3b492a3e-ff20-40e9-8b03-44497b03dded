use rocket::http::Status;
use rocket::serde::json::J<PERSON>;
use serde::{Serialize, Deserialize};
use std::fmt;
use chrono::{DateTime, Utc};
use uuid::Uuid;

/// Standard API response structure
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    /// Whether the request was successful
    pub success: bool,

    /// HTTP status code
    pub status: u16,

    /// Optional message providing additional information (used for error messages)
    #[serde(skip_serializing_if = "Option::is_none")]
    pub message: Option<String>,

    /// Optional data returned by the API
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,

    /// Optional metadata about the response
    #[serde(skip_serializing_if = "Option::is_none")]
    pub meta: Option<MetaInfo>,
}

/// Error information structure
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorInfo {
    /// Error code (can be used for client-side error handling)
    pub code: String,

    /// Human-readable error message
    pub message: String,

    /// Optional additional details about the error
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<serde_json::Value>,

    /// Optional list of field-specific errors
    #[serde(skip_serializing_if = "Option::is_none")]
    pub field_errors: Option<Vec<FieldError>>,
}

/// Field-specific error information
#[derive(Debug, Serialize, Deserialize)]
pub struct FieldError {
    /// Name of the field with the error
    pub field: String,

    /// Error code for the field
    pub code: String,

    /// Human-readable error message for the field
    pub message: String,
}

/// Metadata information structure
#[derive(Debug, Serialize, Deserialize)]
pub struct MetaInfo {
    /// Timestamp of when the response was generated
    pub timestamp: DateTime<Utc>,

    /// Unique identifier for the request
    #[serde(skip_serializing_if = "Option::is_none")]
    pub request_id: Option<String>,

    /// API version
    #[serde(skip_serializing_if = "Option::is_none")]
    pub version: Option<String>,

    /// Optional pagination information
    #[serde(skip_serializing_if = "Option::is_none")]
    pub pagination: Option<PaginationInfo>,
}

/// Pagination information structure
#[derive(Debug, Serialize, Deserialize)]
pub struct PaginationInfo {
    /// Current page number
    pub page: u32,

    /// Number of items per page
    pub per_page: u32,

    /// Total number of items
    pub total: u64,

    /// Total number of pages
    pub total_pages: u32,
}

/// Error codes for common error scenarios
pub enum ErrorCode {
    BadRequest,
    Unauthorized,
    Forbidden,
    NotFound,
    Conflict,
    InternalError,
    ValidationError,
    PaymentRequired,
    RateLimitExceeded,
    Custom(String),
}

impl fmt::Display for ErrorCode {
    fn fmt(&self, f: &mut fmt::Formatter) -> fmt::Result {
        match self {
            ErrorCode::BadRequest => write!(f, "BAD_REQUEST"),
            ErrorCode::Unauthorized => write!(f, "UNAUTHORIZED"),
            ErrorCode::Forbidden => write!(f, "FORBIDDEN"),
            ErrorCode::NotFound => write!(f, "NOT_FOUND"),
            ErrorCode::Conflict => write!(f, "CONFLICT"),
            ErrorCode::InternalError => write!(f, "INTERNAL_ERROR"),
            ErrorCode::ValidationError => write!(f, "VALIDATION_ERROR"),
            ErrorCode::PaymentRequired => write!(f, "PAYMENT_REQUIRED"),
            ErrorCode::RateLimitExceeded => write!(f, "RATE_LIMIT_EXCEEDED"),
            ErrorCode::Custom(code) => write!(f, "{}", code),
        }
    }
}

impl<T> ApiResponse<T> {
    /// Create a successful response with data
    pub fn success(data: T) -> Json<ApiResponse<T>> {
        Json(ApiResponse {
            success: true,
            status: 200,
            message: None,
            data: Some(data),
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }

    /// Create a successful response with data and a custom message
    pub fn success_with_message(data: T, message: &str) -> Json<ApiResponse<T>> {
        Json(ApiResponse {
            success: true,
            status: 200,
            message: Some(message.to_string()),
            data: Some(data),
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }

    /// Create a successful response with data and a custom status code
    pub fn success_with_status(data: T, status: Status) -> Json<ApiResponse<T>> {
        Json(ApiResponse {
            success: true,
            status: status.code,
            message: None,
            data: Some(data),
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }

    /// Create a successful response with pagination
    pub fn success_with_pagination(
        data: T,
        page: u32,
        per_page: u32,
        total: u64
    ) -> Json<ApiResponse<T>> {
        let total_pages = ((total as f64) / (per_page as f64)).ceil() as u32;

        Json(ApiResponse {
            success: true,
            status: 200,
            message: None,
            data: Some(data),
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: Some(PaginationInfo {
                    page,
                    per_page,
                    total,
                    total_pages,
                }),
            }),
        })
    }

    /// Create an error response
    pub fn error(status: Status, _code: ErrorCode, message: &str) -> Json<ApiResponse<T>> {
        Json(ApiResponse {
            success: false,
            status: status.code,
            message: Some(message.to_string()),
            data: None,
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }

    /// Create an error response with additional details
    pub fn error_with_details(
        status: Status,
        _code: ErrorCode,
        message: &str,
        _details: serde_json::Value
    ) -> Json<ApiResponse<T>> {
        // In the new format, we don't include details in the response
        Json(ApiResponse {
            success: false,
            status: status.code,
            message: Some(message.to_string()),
            data: None,
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }

    /// Create an error response with field errors
    pub fn validation_error(
        field_errors: Vec<FieldError>
    ) -> Json<ApiResponse<T>> {
        // Convert field errors to a message
        let message = "Validation failed";

        Json(ApiResponse {
            success: false,
            status: 400,
            message: Some(message.to_string()),
            data: None,
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }
}

/// Trait to convert Rocket Status to ApiResponse
pub trait IntoApiResponse<T> {
    fn into_api_response(self, message: &str) -> Json<ApiResponse<T>>;
}

impl<T> IntoApiResponse<T> for Status {
    fn into_api_response(self, message: &str) -> Json<ApiResponse<T>> {
        Json(ApiResponse {
            success: false,
            status: self.code,
            message: Some(message.to_string()),
            data: None,
            meta: Some(MetaInfo {
                timestamp: Utc::now(),
                request_id: Some(Uuid::new_v4().to_string()),
                version: Some("1.0".to_string()),
                pagination: None,
            }),
        })
    }
}

/// Helper function to convert Result<Json<T>, Status> to Json<ApiResponse<T>>
pub fn from_result<T>(result: Result<Json<T>, Status>, success_message: Option<&str>) -> Json<ApiResponse<T>> {
    match result {
        Ok(json) => {
            if let Some(message) = success_message {
                ApiResponse::success_with_message(json.into_inner(), message)
            } else {
                ApiResponse::success(json.into_inner())
            }
        },
        Err(status) => {
            let message = if status.code == 400 {
                "Bad request"
            } else if status.code == 401 {
                "Unauthorized"
            } else if status.code == 402 {
                "Payment required"
            } else if status.code == 403 {
                "Forbidden"
            } else if status.code == 404 {
                "Resource not found"
            } else if status.code == 409 {
                "Resource conflict"
            } else if status.code == 500 {
                "Internal server error"
            } else {
                "An error occurred"
            };

            status.into_api_response(message)
        }
    }
}

/// Helper function to convert anyhow::Result<T, E> to Json<ApiResponse<T>>
pub fn from_anyhow_result<T, E: std::fmt::Display>(
    result: std::result::Result<T, E>,
    success_message: Option<&str>
) -> Json<ApiResponse<T>> {
    match result {
        Ok(data) => {
            if let Some(message) = success_message {
                ApiResponse::success_with_message(data, message)
            } else {
                ApiResponse::success(data)
            }
        },
        Err(err) => {
            ApiResponse::error(
                Status::InternalServerError,
                ErrorCode::InternalError,
                &format!("Operation failed: {}", err)
            )
        }
    }
}

/// Helper function to create a field error
pub fn field_error(field: &str, code: &str, message: &str) -> FieldError {
    FieldError {
        field: field.to_string(),
        code: code.to_string(),
        message: message.to_string(),
    }
}

/// Helper function to create a paginated response
pub fn paginate<T>(
    data: T,
    page: u32,
    per_page: u32,
    total: u64
) -> Json<ApiResponse<T>> {
    ApiResponse::success_with_pagination(data, page, per_page, total)
}
