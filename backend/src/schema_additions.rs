// Add these tables to the schema.rs file

diesel::table! {
    ai_credit_transactions (id) {
        id -> Uuid,
        organization_id -> Uuid,
        amount -> Int4,
        #[max_length = 50]
        transaction_type -> Varchar,
        description -> Nullable<Text>,
        #[max_length = 255]
        stripe_payment_intent_id -> Nullable<Varchar>,
        #[max_length = 255]
        stripe_invoice_id -> Nullable<Varchar>,
        created_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    ai_credit_usage (id) {
        id -> Uuid,
        organization_id -> Uuid,
        user_id -> Nullable<Uuid>,
        credits_used -> Int4,
        #[max_length = 50]
        operation -> Varchar,
        #[max_length = 10]
        source_locale -> Nullable<Varchar>,
        #[max_length = 10]
        target_locale -> Nullable<Varchar>,
        text_length -> Nullable<Int4>,
        #[max_length = 50]
        model_used -> Nullable<Varchar>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    ai_credit_pricing (id) {
        id -> Uuid,
        min_credits -> Int4,
        max_credits -> Nullable<Int4>,
        price_per_credit -> Float8,
        #[max_length = 3]
        currency -> Varchar,
        is_active -> Nullable<Bool>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

// Add these joinable statements
diesel::joinable!(ai_credit_transactions -> organizations (organization_id));
diesel::joinable!(ai_credit_transactions -> users (created_by));
diesel::joinable!(ai_credit_usage -> organizations (organization_id));
diesel::joinable!(ai_credit_usage -> users (user_id));

// Update the allow_tables_to_appear_in_same_query! macro
diesel::allow_tables_to_appear_in_same_query!(
    ai_credit_transactions,
    ai_credit_usage,
    ai_credit_pricing,
    api_keys,
    email_logs,
    locales,
    organization_members,
    organization_usage,
    organizations,
    payment_methods,
    project_locales,
    projects,
    resources,
    subscription_tiers,
    translation_history,
    translation_keys,
    translations,
    users,
);
