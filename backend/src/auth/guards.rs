use rocket::http::Status;
use rocket::request::{self, FromRequest, Request};
use rocket::outcome::Outcome;
use uuid::Uuid;
use std::str::FromStr;

use crate::auth::jwt::decode_token;
use crate::auth::roles::Role;

pub struct AuthUser {
    pub user_id: Uuid,
    pub role: Role,
}

#[derive(Debug)]
pub enum AuthError {
    Missing,
    Invalid,
    Forbidden,
}

#[rocket::async_trait]
impl<'r> FromRequest<'r> for AuthUser {
    type Error = AuthError;

    async fn from_request(request: &'r Request<'_>) -> request::Outcome<Self, Self::Error> {
        // Get the authorization header
        let auth_header = request.headers().get_one("Authorization");

        match auth_header {
            Some(header) => {
                // Check if it's a Bearer token
                if !header.starts_with("Bearer ") {
                    return Outcome::Error((Status::Unauthorized, AuthError::Invalid));
                }

                // Extract the token
                let token = &header[7..];

                // Decode the token to get the claims
                match decode_token(token) {
                    Ok(claims) => {
                        // Parse the user ID
                        let user_id = match Uuid::parse_str(&claims.sub) {
                            Ok(id) => id,
                            Err(_) => return Outcome::Error((Status::Unauthorized, AuthError::Invalid)),
                        };

                        // Parse the role
                        let role = match Role::from_str(&claims.role) {
                            Ok(role) => role,
                            Err(_) => return Outcome::Error((Status::Unauthorized, AuthError::Invalid)),
                        };

                        Outcome::Success(AuthUser { user_id, role })
                    },
                    Err(_) => Outcome::Error((Status::Unauthorized, AuthError::Invalid)),
                }
            },
            None => Outcome::Error((Status::Unauthorized, AuthError::Missing)),
        }
    }
}

pub struct RequireRole<R> {
    pub required_role: Role,
    _marker: std::marker::PhantomData<R>,
}

impl<R> RequireRole<R> {
    pub fn new(required_role: Role) -> Self {
        Self {
            required_role,
            _marker: std::marker::PhantomData,
        }
    }
}

#[rocket::async_trait]
impl<'r> FromRequest<'r> for RequireRole<AuthUser> {
    type Error = AuthError;

    async fn from_request(request: &'r Request<'_>) -> request::Outcome<Self, Self::Error> {
        // First, get the authenticated user
        let outcome = AuthUser::from_request(request).await;

        match outcome {
            Outcome::Success(auth_user) => {
                // Check if the user has the required role
                let required_role = Role::Admin; // Default to Admin for now

                if auth_user.role.has_permission(&required_role) {
                    Outcome::Success(Self {
                        required_role,
                        _marker: std::marker::PhantomData,
                    })
                } else {
                    Outcome::Error((Status::Forbidden, AuthError::Forbidden))
                }
            },
            Outcome::Error(e) => Outcome::Error(e),
            Outcome::Forward(f) => Outcome::Forward(f),
        }
    }
}