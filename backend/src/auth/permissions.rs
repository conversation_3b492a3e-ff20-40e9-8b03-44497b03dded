use std::collections::HashMap;
use lazy_static::lazy_static;

/// Permission types for API keys
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, PartialEq, Eq)]
pub enum PermissionType {
    /// Global permissions that apply to all resources
    Global,
    /// Resource-specific permissions
    Resource(String),
}

/// Permission actions for API keys
#[derive(<PERSON>bu<PERSON>, <PERSON>lone, PartialEq, Eq)]
pub enum PermissionAction {
    /// Read permission (GET requests)
    Read,
    /// Write permission (POST, PUT, DELETE requests)
    Write,
    /// Admin permission (full access)
    Admin,
    /// Custom action
    Custom(String),
}

/// Permission struct representing a single permission
#[derive(Debug, <PERSON>lone, PartialEq, Eq)]
pub struct Permission {
    /// The permission key (e.g., "read", "projects:write")
    pub key: String,
    /// The permission type
    pub permission_type: PermissionType,
    /// The permission action
    pub action: PermissionAction,
    /// Description of the permission
    pub description: String,
}

impl Permission {
    /// Create a new permission
    pub fn new(
        key: &str,
        permission_type: PermissionType,
        action: PermissionAction,
        description: &str,
    ) -> Self {
        Self {
            key: key.to_string(),
            permission_type,
            action,
            description: description.to_string(),
        }
    }

    /// Check if this permission is a global permission
    pub fn is_global(&self) -> bool {
        matches!(self.permission_type, PermissionType::Global)
    }

    /// Check if this permission is a resource-specific permission
    pub fn is_resource_specific(&self) -> bool {
        matches!(self.permission_type, PermissionType::Resource(_))
    }

    /// Get the resource type if this is a resource-specific permission
    pub fn resource_type(&self) -> Option<&str> {
        match &self.permission_type {
            PermissionType::Resource(resource) => Some(resource),
            _ => None,
        }
    }
}

lazy_static! {
    /// All available permissions
    pub static ref PERMISSIONS: HashMap<String, Permission> = {
        let mut permissions = HashMap::new();

        // Global permissions
        permissions.insert(
            "read".to_string(),
            Permission::new(
                "read",
                PermissionType::Global,
                PermissionAction::Read,
                "Read access to all resources",
            ),
        );
        permissions.insert(
            "write".to_string(),
            Permission::new(
                "write",
                PermissionType::Global,
                PermissionAction::Write,
                "Write access to all resources",
            ),
        );
        permissions.insert(
            "admin".to_string(),
            Permission::new(
                "admin",
                PermissionType::Global,
                PermissionAction::Admin,
                "Full access to all resources",
            ),
        );

        // Project permissions
        permissions.insert(
            "projects:read".to_string(),
            Permission::new(
                "projects:read",
                PermissionType::Resource("projects".to_string()),
                PermissionAction::Read,
                "Read access to projects",
            ),
        );
        permissions.insert(
            "projects:write".to_string(),
            Permission::new(
                "projects:write",
                PermissionType::Resource("projects".to_string()),
                PermissionAction::Write,
                "Write access to projects",
            ),
        );

        // Translation permissions
        permissions.insert(
            "translations:read".to_string(),
            Permission::new(
                "translations:read",
                PermissionType::Resource("translations".to_string()),
                PermissionAction::Read,
                "Read access to translations",
            ),
        );
        permissions.insert(
            "translations:write".to_string(),
            Permission::new(
                "translations:write",
                PermissionType::Resource("translations".to_string()),
                PermissionAction::Write,
                "Write access to translations",
            ),
        );

        // Locale permissions
        permissions.insert(
            "locales:read".to_string(),
            Permission::new(
                "locales:read",
                PermissionType::Resource("locales".to_string()),
                PermissionAction::Read,
                "Read access to locales",
            ),
        );
        permissions.insert(
            "locales:write".to_string(),
            Permission::new(
                "locales:write",
                PermissionType::Resource("locales".to_string()),
                PermissionAction::Write,
                "Write access to locales",
            ),
        );

        // Organization permissions
        permissions.insert(
            "organizations:read".to_string(),
            Permission::new(
                "organizations:read",
                PermissionType::Resource("organizations".to_string()),
                PermissionAction::Read,
                "Read access to organizations",
            ),
        );
        permissions.insert(
            "organizations:write".to_string(),
            Permission::new(
                "organizations:write",
                PermissionType::Resource("organizations".to_string()),
                PermissionAction::Write,
                "Write access to organizations",
            ),
        );

        // AI Credits permissions
        permissions.insert(
            "ai-credits:read".to_string(),
            Permission::new(
                "ai-credits:read",
                PermissionType::Resource("ai-credits".to_string()),
                PermissionAction::Read,
                "Read access to AI credits",
            ),
        );
        permissions.insert(
            "ai-credits:write".to_string(),
            Permission::new(
                "ai-credits:write",
                PermissionType::Resource("ai-credits".to_string()),
                PermissionAction::Write,
                "Write access to AI credits",
            ),
        );

        permissions
    };
}

/// Get all available permissions
pub fn get_all_permissions() -> &'static HashMap<String, Permission> {
    &PERMISSIONS
}

/// Get a permission by key
pub fn get_permission(key: &str) -> Option<&'static Permission> {
    PERMISSIONS.get(key)
}

/// Validate a list of permission keys
pub fn validate_permissions(permissions: &[String]) -> Result<(), String> {
    for permission in permissions {
        if !PERMISSIONS.contains_key(permission) {
            return Err(format!("Invalid permission: {}", permission));
        }
    }
    Ok(())
}

/// Get all permissions for a specific resource
pub fn get_resource_permissions(resource: &str) -> Vec<&'static Permission> {
    PERMISSIONS
        .values()
        .filter(|p| {
            if let PermissionType::Resource(r) = &p.permission_type {
                r == resource
            } else {
                false
            }
        })
        .collect()
}

/// Get all global permissions
pub fn get_global_permissions() -> Vec<&'static Permission> {
    PERMISSIONS
        .values()
        .filter(|p| matches!(p.permission_type, PermissionType::Global))
        .collect()
}
