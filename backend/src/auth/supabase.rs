use anyhow::{anyhow, Result};
use chrono::{DateTime, Utc};
use reqwest::Client;
use serde::{Deserialize, Serialize};
use std::env;
use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct SupabaseUser {
    pub id: Uuid,
    pub email: String,
    pub app_metadata: AppMetadata,
    pub user_metadata: UserMetadata,
    pub created_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AppMetadata {
    pub provider: String,
    pub providers: Vec<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct UserMetadata {
    pub username: Option<String>,
    pub full_name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SignUpRequest {
    pub email: String,
    pub password: String,
    pub data: UserMetadata,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct SignInRequest {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    pub access_token: String,
    pub refresh_token: String,
    pub user: SupabaseUser,
}

pub struct SupabaseAuth {
    client: Client,
    supabase_url: String,
    supabase_key: String,
}

impl SupabaseAuth {
    pub fn new() -> Self {
        let supabase_url = env::var("SUPABASE_URL").expect("SUPABASE_URL must be set");
        let supabase_key = env::var("SUPABASE_KEY").expect("SUPABASE_KEY must be set");
        
        Self {
            client: Client::new(),
            supabase_url,
            supabase_key,
        }
    }
    
    pub async fn sign_up(&self, email: &str, password: &str, username: &str, full_name: Option<&str>) -> Result<AuthResponse> {
        let url = format!("{}/auth/v1/signup", self.supabase_url);
        
        let user_metadata = UserMetadata {
            username: Some(username.to_string()),
            full_name: full_name.map(|s| s.to_string()),
        };
        
        let request = SignUpRequest {
            email: email.to_string(),
            password: password.to_string(),
            data: user_metadata,
        };
        
        let response = self.client
            .post(&url)
            .header("apikey", &self.supabase_key)
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await?;
            
        if response.status().is_success() {
            let auth_response = response.json::<AuthResponse>().await?;
            Ok(auth_response)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Supabase signup failed: {}", error_text))
        }
    }
    
    pub async fn sign_in(&self, email: &str, password: &str) -> Result<AuthResponse> {
        let url = format!("{}/auth/v1/token?grant_type=password", self.supabase_url);
        
        let request = SignInRequest {
            email: email.to_string(),
            password: password.to_string(),
        };
        
        let response = self.client
            .post(&url)
            .header("apikey", &self.supabase_key)
            .header("Content-Type", "application/json")
            .json(&request)
            .send()
            .await?;
            
        if response.status().is_success() {
            let auth_response = response.json::<AuthResponse>().await?;
            Ok(auth_response)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Supabase signin failed: {}", error_text))
        }
    }
    
    pub async fn get_user(&self, token: &str) -> Result<SupabaseUser> {
        let url = format!("{}/auth/v1/user", self.supabase_url);
        
        let response = self.client
            .get(&url)
            .header("apikey", &self.supabase_key)
            .header("Authorization", format!("Bearer {}", token))
            .send()
            .await?;
            
        if response.status().is_success() {
            let user = response.json::<SupabaseUser>().await?;
            Ok(user)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Failed to get user: {}", error_text))
        }
    }
}
