use anyhow::Result;
use jsonwebtoken::{decode, encode, Decod<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Algorithm};
use serde::{Deserialize, Serialize};
use std::env;
use uuid::Uuid;
use chrono::{Utc, Duration};

use crate::auth::roles::Role;

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,     // Subject (user ID)
    pub exp: i64,        // Expiration time
    pub iat: i64,        // Issued at
    pub email: String,   // User email
    pub username: String, // Username
    pub role: String,    // User role
}

pub fn generate_token(user_id: Uuid, email: &str, username: &str, role: Role) -> Result<String> {
    let jwt_secret = env::var("JWT_SECRET").expect("JWT_SECRET must be set");

    // Current time
    let now = Utc::now();

    // Token expiration time (24 hours from now)
    let expiration = now + Duration::hours(24);

    // Create claims
    let claims = Claims {
        sub: user_id.to_string(),
        exp: expiration.timestamp(),
        iat: now.timestamp(),
        email: email.to_string(),
        username: username.to_string(),
        role: role.to_string(),
    };

    // Generate token
    let token = encode(
        &Header::new(Algorithm::HS256),
        &claims,
        &EncodingKey::from_secret(jwt_secret.as_bytes()),
    )?;

    Ok(token)
}

pub fn generate_refresh_token(user_id: Uuid) -> Result<String> {
    let jwt_secret = env::var("JWT_SECRET").expect("JWT_SECRET must be set");

    // Current time
    let now = Utc::now();

    // Token expiration time (30 days from now)
    let expiration = now + Duration::days(30);

    // Create claims (simplified for refresh token)
    let claims = Claims {
        sub: user_id.to_string(),
        exp: expiration.timestamp(),
        iat: now.timestamp(),
        email: "".to_string(),
        username: "".to_string(),
        role: "refresh".to_string(),
    };

    // Generate token
    let token = encode(
        &Header::new(Algorithm::HS256),
        &claims,
        &EncodingKey::from_secret(jwt_secret.as_bytes()),
    )?;

    Ok(token)
}

pub fn generate_password_reset_token(user_id: Uuid, email: &str) -> Result<String> {
    let jwt_secret = env::var("JWT_SECRET").expect("JWT_SECRET must be set");

    // Current time
    let now = Utc::now();

    // Token expiration time (1 hour from now)
    let expiration = now + Duration::hours(1);

    // Create claims for password reset
    let claims = Claims {
        sub: user_id.to_string(),
        exp: expiration.timestamp(),
        iat: now.timestamp(),
        email: email.to_string(),
        username: "".to_string(),
        role: "password_reset".to_string(),
    };

    // Generate token
    let token = encode(
        &Header::new(Algorithm::HS256),
        &claims,
        &EncodingKey::from_secret(jwt_secret.as_bytes()),
    )?;

    Ok(token)
}

pub fn generate_email_verification_token(user_id: Uuid, email: &str, username: &str) -> Result<String> {
    let jwt_secret = env::var("JWT_SECRET").expect("JWT_SECRET must be set");

    // Current time
    let now = Utc::now();

    // Token expiration time (24 hours from now)
    let expiration = now + Duration::hours(24);

    // Create claims for email verification
    let claims = Claims {
        sub: user_id.to_string(),
        exp: expiration.timestamp(),
        iat: now.timestamp(),
        email: email.to_string(),
        username: username.to_string(),
        role: "email_verification".to_string(),
    };

    // Generate token
    let token = encode(
        &Header::new(Algorithm::HS256),
        &claims,
        &EncodingKey::from_secret(jwt_secret.as_bytes()),
    )?;

    Ok(token)
}

pub fn decode_token(token: &str) -> Result<Claims> {
    let jwt_secret = env::var("JWT_SECRET").expect("JWT_SECRET must be set");

    let validation = Validation::new(Algorithm::HS256);

    let token_data = decode::<Claims>(
        token,
        &DecodingKey::from_secret(jwt_secret.as_bytes()),
        &validation,
    )?;

    Ok(token_data.claims)
}

pub fn validate_token(token: &str) -> Result<Uuid> {
    let claims = decode_token(token)?;

    // Convert subject to UUID
    let user_id = Uuid::parse_str(&claims.sub)?;

    Ok(user_id)
}
