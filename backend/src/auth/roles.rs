use std::str::FromStr;
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};

#[derive(Debug, C<PERSON>, PartialEq, Eq, Serialize, Deserialize)]
pub enum Role {
    User,
    Admin,
    SuperAdmin,
}

impl FromStr for Role {
    type Err = anyhow::Error;

    fn from_str(s: &str) -> Result<Self, Self::Err> {
        match s.to_lowercase().as_str() {
            "user" => Ok(Role::User),
            "admin" => Ok(Role::Admin),
            "superadmin" => Ok(Role::SuperAdmin),
            _ => Err(anyhow!("Invalid role: {}", s)),
        }
    }
}

impl ToString for Role {
    fn to_string(&self) -> String {
        match self {
            Role::User => "user".to_string(),
            Role::Admin => "admin".to_string(),
            Role::SuperAdmin => "superadmin".to_string(),
        }
    }
}

impl Role {
    pub fn has_permission(&self, required_role: &Role) -> bool {
        match (self, required_role) {
            // SuperAdmin has all permissions
            (Role::SuperAdmin, _) => true,
            // Admin has Admin and User permissions
            (Role::Admin, Role::Admin | Role::User) => true,
            // User has only User permissions
            (Role::User, Role::User) => true,
            // Any other combination is not allowed
            _ => false,
        }
    }
}
