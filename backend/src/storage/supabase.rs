use anyhow::{anyhow, Result};
use reqwest::{Client, multipart};
use serde::{Deserialize, Serialize};
use std::env;

use uuid::Uuid;

#[derive(Debug, Serialize, Deserialize)]
pub struct FileObject {
    pub name: String,
    pub bucket_id: String,
    pub owner: String,
    pub id: Uuid,
    pub updated_at: String,
    pub created_at: String,
    pub last_accessed_at: String,
    pub metadata: serde_json::Value,
    pub buckets: serde_json::Value,
}

pub struct SupabaseStorage {
    client: Client,
    supabase_url: String,
    supabase_key: String,
}

impl SupabaseStorage {
    pub fn new() -> Self {
        let supabase_url = env::var("SUPABASE_URL").expect("SUPABASE_URL must be set");
        let supabase_key = env::var("SUPABASE_KEY").expect("SUPABASE_KEY must be set");

        Self {
            client: Client::new(),
            supabase_url,
            supabase_key,
        }
    }

    pub async fn upload_file(&self, bucket: &str, path: &str, file_data: Vec<u8>, content_type: &str, token: &str) -> Result<FileObject> {
        let url = format!("{}/storage/v1/object/{}/{}", self.supabase_url, bucket, path);

        let form = multipart::Form::new()
            .part("file", multipart::Part::bytes(file_data)
                .file_name(path.split('/').last().unwrap_or(path).to_string())
                .mime_str(content_type)?);

        let response = self.client
            .post(&url)
            .header("apikey", &self.supabase_key)
            .header("Authorization", format!("Bearer {}", token))
            .multipart(form)
            .send()
            .await?;

        if response.status().is_success() {
            let file_object = response.json::<FileObject>().await?;
            Ok(file_object)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Failed to upload file: {}", error_text))
        }
    }

    pub async fn download_file(&self, bucket: &str, path: &str, token: &str) -> Result<Vec<u8>> {
        let url = format!("{}/storage/v1/object/{}/{}", self.supabase_url, bucket, path);

        let response = self.client
            .get(&url)
            .header("apikey", &self.supabase_key)
            .header("Authorization", format!("Bearer {}", token))
            .send()
            .await?;

        if response.status().is_success() {
            let bytes = response.bytes().await?;
            Ok(bytes.to_vec())
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Failed to download file: {}", error_text))
        }
    }

    pub async fn delete_file(&self, bucket: &str, path: &str, token: &str) -> Result<()> {
        let url = format!("{}/storage/v1/object/{}/{}", self.supabase_url, bucket, path);

        let response = self.client
            .delete(&url)
            .header("apikey", &self.supabase_key)
            .header("Authorization", format!("Bearer {}", token))
            .send()
            .await?;

        if response.status().is_success() {
            Ok(())
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Failed to delete file: {}", error_text))
        }
    }

    pub async fn list_files(&self, bucket: &str, path: &str, token: &str) -> Result<Vec<FileObject>> {
        let url = format!("{}/storage/v1/object/list/{}", self.supabase_url, bucket);

        let response = self.client
            .get(&url)
            .header("apikey", &self.supabase_key)
            .header("Authorization", format!("Bearer {}", token))
            .query(&[("prefix", path)])
            .send()
            .await?;

        if response.status().is_success() {
            let files = response.json::<Vec<FileObject>>().await?;
            Ok(files)
        } else {
            let error_text = response.text().await?;
            Err(anyhow!("Failed to list files: {}", error_text))
        }
    }

    pub fn get_public_url(&self, bucket: &str, path: &str) -> String {
        format!("{}/storage/v1/object/public/{}/{}", self.supabase_url, bucket, path)
    }
}
