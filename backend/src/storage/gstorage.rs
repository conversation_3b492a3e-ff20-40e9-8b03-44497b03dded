use anyhow::{anyhow, Result};
use google_cloud_storage::client::{Client, ClientConfig};
use google_cloud_storage::http::objects::{
    get::GetObjectRequest, list::ListObjectsRequest,
    delete::DeleteObjectRequest,
};
use google_cloud_storage::http::objects::download::Range;
use google_cloud_storage::http::objects::upload::{Media, UploadObjectRequest, UploadType};
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, Serialize, Deserialize)]
pub struct FileObject {
    pub name: String,
    pub bucket: String,
    pub id: String,
    pub updated: String,
    pub created: String,
    pub size: u64,
    pub content_type: String,
    pub metadata: Option<serde_json::Value>,
}

impl From<google_cloud_storage::http::objects::Object> for FileObject {
    fn from(obj: google_cloud_storage::http::objects::Object) -> Self {
        Self {
            name: obj.name,
            bucket: obj.bucket,
            id: obj.id,
            updated: obj.updated.map_or_else(|| "".to_string(), |dt| dt.to_string()),
            created: obj.time_created.map_or_else(|| "".to_string(), |dt| dt.to_string()),
            size: obj.size as u64,
            content_type: obj.content_type.unwrap_or_default(),
            metadata: obj.metadata.map(|m| serde_json::to_value(m).unwrap_or_default()),
        }
    }
}

pub struct GStorage {
    client: Client,
    project_id: String,
}

impl GStorage {
    pub async fn new() -> Result<Self> {
        // Try to get project ID from environment variables
        let project_id = match env::var("GOOGLE_CLOUD_PROJECT") {
            Ok(id) => id,
            Err(_) => "default-project".to_string(), // Fallback project ID
        };

        // We don't need to create an authentication manager explicitly, the with_auth() method will handle it

        // Create client config with auth
        let config = ClientConfig::default()
            .with_auth()
            .await
            .map_err(|e| anyhow!("Failed to create Google Cloud Storage client config: {}", e))?;

        let client = Client::new(config);

        Ok(Self {
            client,
            project_id,
        })
    }

    pub async fn upload_file(&self, bucket: &str, path: &str, file_data: Vec<u8>, _content_type: &str) -> Result<FileObject> {
        let request = UploadObjectRequest {
            bucket: bucket.to_string(),
            ..Default::default()
        };

        let media = Media::new(path.to_string());
        let upload_type = UploadType::Simple(media);

        let result = self.client
            .upload_object(&request, file_data, &upload_type)
            .await
            .map_err(|e| anyhow!("Failed to upload file: {}", e))?;

        Ok(FileObject::from(result))
    }

    pub async fn download_file(&self, bucket: &str, path: &str) -> Result<Vec<u8>> {
        let request = GetObjectRequest {
            bucket: bucket.to_string(),
            object: path.to_string(),
            ..Default::default()
        };

        let data = self.client
            .download_object(&request, &Range::default())
            .await
            .map_err(|e| anyhow!("Failed to download file: {}", e))?;

        Ok(data)
    }

    pub async fn delete_file(&self, bucket: &str, path: &str) -> Result<()> {
        let request = DeleteObjectRequest {
            bucket: bucket.to_string(),
            object: path.to_string(),
            ..Default::default()
        };

        self.client
            .delete_object(&request)
            .await
            .map_err(|e| anyhow!("Failed to delete file: {}", e))?;

        Ok(())
    }

    pub async fn list_files(&self, bucket: &str, prefix: Option<&str>) -> Result<Vec<FileObject>> {
        let request = ListObjectsRequest {
            bucket: bucket.to_string(),
            prefix: prefix.map(|s| s.to_string()),
            ..Default::default()
        };

        let response = self.client
            .list_objects(&request)
            .await
            .map_err(|e| anyhow!("Failed to list files: {}", e))?;

        let files = response.items
            .unwrap_or_default()
            .into_iter()
            .map(FileObject::from)
            .collect();

        Ok(files)
    }

    pub fn get_public_url(&self, bucket: &str, path: &str) -> String {
        format!("https://storage.googleapis.com/{}/{}", bucket, path)
    }
}
