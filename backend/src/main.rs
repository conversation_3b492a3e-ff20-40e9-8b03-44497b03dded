#[macro_use] extern crate rocket;
extern crate diesel;

mod schema;
mod models;
mod db;
mod routes;
mod repositories;
mod auth;
mod storage;
mod email;
mod utils;
mod ai;
mod tasks;
mod payment;
mod middleware;

use dotenv::dotenv;
use rocket::fs::{FileServer, relative};
use rocket::http::Method;
use rocket_cors::{AllowedHeaders, AllowedOrigins};
use diesel::prelude::*;
use diesel::pg::PgConnection;
use diesel::connection::SimpleConnection;
use std::env;

use crate::utils::catchers;

use crate::storage::gstorage::GStorage;
use crate::email::cloud_notification::CloudNotificationService;
use crate::ai::gemini::GeminiTranslationService;
use crate::tasks::credit_reset::CreditResetTask;
use crate::payment::StripeService;
use crate::middleware::ApiKeyMiddleware;

#[get("/")]
fn index() -> &'static str {
    "Welcome to ADC Multi-Languages API!"
}

#[get("/health")]
fn health() -> rocket::serde::json::Json<serde_json::Value> {
    rocket::serde::json::Json(serde_json::json!({
        "data": {
            "status": "up",
            "timestamp": chrono::Utc::now().format("%Y-%m-%dT%H:%M:%SZ").to_string()
        },
        "message": "Service is healthy",
        "success": true
    }))
}

#[launch]
async fn rocket() -> _ {
    dotenv().ok();

    // Initialize database schema
    let database_url = env::var("DATABASE_URL").expect("DATABASE_URL must be set");
    let mut conn = PgConnection::establish(&database_url)
        .expect("Could not establish connection to the database");

    // Check if tables exist before trying to create them
    let table_exists = conn.batch_execute("SELECT 1 FROM information_schema.tables WHERE table_name = 'users' LIMIT 1")
        .is_ok();

    if !table_exists {
        println!("Creating database tables...");
        // Create tables if they don't exist
        match conn.batch_execute(include_str!("../migrations/2023-01-01-000000_create_tables/up.sql")) {
            Ok(_) => println!("Database tables created successfully"),
            Err(e) => {
                // If tables already exist, this is not a critical error
                if e.to_string().contains("already exists") {
                    println!("Tables already exist, skipping creation");
                } else {
                    // For other errors, we should still panic
                    panic!("Failed to initialize database schema: {}", e);
                }
            }
        }
    } else {
        println!("Database tables already exist, skipping creation");
    }

    // Set up CORS
    let allowed_origins = AllowedOrigins::all();
    let cors = rocket_cors::CorsOptions {
        allowed_origins,
        allowed_methods: vec![Method::Get, Method::Post, Method::Put, Method::Delete, Method::Options]
            .into_iter()
            .map(From::from)
            .collect(),
        allowed_headers: AllowedHeaders::all(),
        allow_credentials: true,
        expose_headers: vec!["Content-Type", "Authorization"]
            .into_iter()
            .map(|h| h.into())
            .collect(),
        max_age: Some(86400),
        ..Default::default()
    }
    .to_cors()
    .expect("CORS configuration error");

    // Initialize database connection pool
    let pool = db::init_pool();

    // Start the credit reset task
    let credit_reset_task = CreditResetTask::new(std::sync::Arc::new(pool.clone()));
    credit_reset_task.start().await;

    // Initialize Google Cloud Storage client
    let gstorage = match GStorage::new().await {
        Ok(storage) => storage,
        Err(e) => {
            eprintln!("Failed to initialize Google Cloud Storage: {}", e);
            eprintln!("Make sure GOOGLE_APPLICATION_CREDENTIALS and GOOGLE_CLOUD_PROJECT environment variables are set");
            panic!("Google Cloud Storage initialization failed");
        }
    };

    // Initialize Cloud Notification service
    let cloud_notification_service = match CloudNotificationService::new() {
        Ok(service) => service,
        Err(e) => {
            eprintln!("Failed to initialize Cloud Notification service: {}", e);
            eprintln!("Make sure BREVO_API_KEY, EMAIL_SENDER_ADDRESS, and EMAIL_SENDER_NAME environment variables are set");
            eprintln!("Also ensure NOTIFICATION_CLOUD_FUNCTION_URL is set to your deployed cloud function URL");
            eprintln!("Continuing with mock email service...");

            // Create a mock implementation instead of panicking
            CloudNotificationService::mock()
        }
    };



    // Initialize AI Translation service
    let ai_translation_service = match GeminiTranslationService::new() {
        Ok(service) => service,
        Err(e) => {
            eprintln!("Failed to initialize Gemini service: {}", e);
            eprintln!("Make sure GEMINI_API_KEY environment variable is set");
            eprintln!("Continuing with mock AI translation...");

            // Create a mock implementation instead of trying to create a real one again
            GeminiTranslationService::mock()
        }
    };

    // Initialize Stripe service
    let stripe_service = match StripeService::new() {
        Ok(service) => service,
        Err(e) => {
            eprintln!("Failed to initialize Stripe service: {}", e);
            eprintln!("Make sure STRIPE_SECRET_KEY environment variable is set");
            eprintln!("Continuing with mock Stripe service...");

            // Create a mock implementation instead of panicking
            StripeService::mock()
        }
    };

    rocket::build()
        .mount("/", routes![index, health])
        .mount("/api", routes![
            routes::api::hello,
            routes::api::echo,
            routes::api::error_example,
        ])
        .mount("/api/auth", routes![
            routes::auth::signup,
            routes::auth::signin,
            routes::auth::refresh_token,
            routes::auth::request_password_reset,
            routes::auth::confirm_password_reset,
            routes::auth::request_email_verification,
            routes::auth::verify_email,
            routes::auth::google_auth,
        ])
        .mount("/api/users", routes![
            routes::users::get_profile,
            routes::users::update_profile,
            routes::users::get_user,
        ])
        .mount("/api/organizations", routes![
            routes::organizations::list_organizations,
            routes::organizations::create_organization,
            routes::organizations::get_organization,
            routes::organizations::update_organization,
            routes::ai_credits::get_ai_credits,
            routes::ai_credits::purchase_ai_credits,
            routes::ai_credits::get_ai_credits_history,
            routes::credit_limit::get_credit_limit,
            routes::credit_limit::update_credit_limit,
            routes::credit_usage::get_credit_usage_history,
            routes::credit_usage::get_credit_usage_analytics,
            routes::organization_subscription::get_organization_subscription,
            routes::organization_subscription::create_organization_subscription,
            routes::organization_subscription::cancel_organization_subscription,
            routes::organization_subscription::verify_organization_subscription,
            routes::api_keys::list_api_keys,
            routes::api_keys::create_api_key,
            routes::api_keys::revoke_api_key,
            routes::api_keys::get_api_key_usage,
            routes::api_keys::get_api_key_usage_by_endpoint,
            routes::api_keys::get_api_key_usage_by_day,
            routes::api_keys::get_permissions,
            routes::permission_groups::list_permission_groups,
            routes::permission_groups::get_permission_group,
            routes::permission_groups::create_permission_group,
            routes::permission_groups::update_permission_group,
            routes::permission_groups::delete_permission_group,
            routes::permission_audit_logs::get_audit_logs,
            routes::permission_audit_logs::get_api_key_audit_logs
            // routes::subscription_history::get_subscription_history,
            // routes::subscription_history::get_ai_credit_usage
        ])
        .mount("/api/projects", routes![
            routes::projects::list_projects,
            routes::projects::create_project,
            routes::projects::get_project,
            routes::projects::update_project,
            routes::projects::delete_project,
            routes::projects::list_project_locales,
            routes::projects::add_project_locale,
            routes::projects::create_resource,
            routes::projects::list_resources,
        ])
        .mount("/api/projects/slug", routes![
            routes::projects::get_project_by_slug,
        ])
        .mount("/api/locales", routes![
            routes::locales::list_locales,
            routes::locales::get_locale_by_code,
            routes::locales::create_locale,
        ])
        .mount("/api/translations", routes![
            routes::translations::list_translation_keys,
            routes::translations::create_translation_key,
            routes::translations::get_translation_key,
            routes::translations::create_translation,
            routes::translations::list_translations,
            routes::translations::get_translation,
            routes::translations::update_translation,
            routes::translations::get_translation_history,
            routes::ai_credits::ai_translate,
        ])
        .mount("/api/admin", routes![
            routes::admin::admin_dashboard,
            routes::admin::admin_users,
        ])
        .mount("/api/test", routes![
            routes::test::test_email,
            routes::test::test_verification_email,
        ])

        .mount("/api/webhooks", routes![
            routes::webhooks_new::stripe_webhook,
        ])

        .mount("/api/ai-credits", routes![
            routes::ai_credits::get_ai_credits_pricing,
        ])
        .mount("/api/subscription-plans", routes![
            routes::subscription_plans::list_subscription_plans,
        ])
        .mount("/api/organizations/slug", routes![
            routes::organizations::get_organization_by_slug,
        ])
        .mount("/static", FileServer::from(env::var("ROCKET_STATIC_DIR").unwrap_or_else(|_| relative!("static").to_string())))
        .manage(pool)
        .manage(gstorage)
        .manage(cloud_notification_service)
        .manage(ai_translation_service)
        .manage(stripe_service)
        .attach(cors)
        .attach(ApiKeyMiddleware)
        // Register custom error catchers
        .register("/", catchers![
            catchers::not_found,
            catchers::unprocessable_entity,
            catchers::internal_error,
            catchers::unauthorized,
            catchers::forbidden,
            catchers::bad_request,
            catchers::method_not_allowed,
            catchers::conflict,
            catchers::too_many_requests,
            catchers::default_catcher
        ])
}
