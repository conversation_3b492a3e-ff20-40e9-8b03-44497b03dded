// Add these tables to the schema.rs file

diesel::table! {
    permission_groups (id) {
        id -> Uuid,
        organization_id -> Uuid,
        name -> Varchar,
        description -> Text,
        permissions -> Jsonb,
        created_by -> Uuid,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    permission_audit_logs (id) {
        id -> Uuid,
        api_key_id -> Uuid,
        organization_id -> Uuid,
        endpoint -> Varchar,
        method -> Varchar,
        permission_key -> Varchar,
        granted -> <PERSON><PERSON>,
        created_at -> Timestamptz,
    }
}

// Add permission_group_id to api_keys table
diesel::table! {
    use diesel::sql_types::*;
    use crate::schema::sql_types::*;

    api_keys (id) {
        id -> Uuid,
        organization_id -> Uuid,
        name -> Varchar,
        key_hash -> Varchar,
        prefix -> Varchar,
        permissions -> Jsonb,
        rate_limit -> Nullable<Int4>,
        expires_at -> Nullable<Timestamptz>,
        last_used_at -> Nullable<Timestamptz>,
        created_at -> Nullable<Timestamptz>,
        created_by -> Uuid,
        revoked_at -> Nullable<Timestamptz>,
        permission_group_id -> Nullable<Uuid>,
    }
}

// Add these joinable statements
diesel::joinable!(permission_groups -> organizations (organization_id));
diesel::joinable!(permission_groups -> users (created_by));
diesel::joinable!(permission_audit_logs -> api_keys (api_key_id));
diesel::joinable!(permission_audit_logs -> organizations (organization_id));
diesel::joinable!(api_keys -> permission_groups (permission_group_id));

// Update the allow_tables_to_appear_in_same_query! macro
diesel::allow_tables_to_appear_in_same_query!(
    permission_groups,
    permission_audit_logs,
    api_keys,
    organizations,
    users,
);
