// @generated automatically by Diesel CLI.

diesel::table! {
    ai_credit_pricing (id) {
        id -> Uuid,
        min_credits -> Int4,
        max_credits -> Nullable<Int4>,
        price_per_credit -> Numeric,
        #[max_length = 3]
        currency -> Varchar,
        is_active -> Nullable<Bool>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    ai_credit_transactions (id) {
        id -> Uuid,
        organization_id -> Uuid,
        amount -> Int4,
        #[max_length = 50]
        transaction_type -> Varchar,
        description -> Nullable<Text>,
        #[max_length = 255]
        stripe_payment_intent_id -> Nullable<Varchar>,
        #[max_length = 255]
        stripe_invoice_id -> Nullable<Varchar>,
        created_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    ai_credit_usage (id) {
        id -> Uuid,
        organization_id -> Uuid,
        user_id -> Nullable<Uuid>,
        credits_used -> Int4,
        #[max_length = 50]
        operation -> Varchar,
        #[max_length = 10]
        source_locale -> Nullable<Varchar>,
        #[max_length = 10]
        target_locale -> Nullable<Varchar>,
        text_length -> Nullable<Int4>,
        #[max_length = 50]
        model_used -> Nullable<Varchar>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    api_call_logs (id) {
        id -> Uuid,
        api_key_id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 255]
        endpoint -> Varchar,
        #[max_length = 10]
        method -> Varchar,
        status_code -> Int4,
        response_time_ms -> Int4,
        request_size_bytes -> Nullable<Int4>,
        response_size_bytes -> Nullable<Int4>,
        #[max_length = 45]
        ip_address -> Nullable<Varchar>,
        user_agent -> Nullable<Text>,
        created_at -> Timestamptz,
    }
}

diesel::table! {
    api_keys (id) {
        id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 255]
        name -> Varchar,
        #[max_length = 255]
        key_hash -> Varchar,
        #[max_length = 20]
        prefix -> Varchar,
        permissions -> Jsonb,
        rate_limit -> Nullable<Int4>,
        expires_at -> Nullable<Timestamptz>,
        last_used_at -> Nullable<Timestamptz>,
        created_by -> Uuid,
        created_at -> Nullable<Timestamptz>,
        revoked_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    billing_events (id) {
        id -> Uuid,
        organization_id -> Uuid,
        amount -> Numeric,
        #[max_length = 3]
        currency -> Varchar,
        description -> Nullable<Text>,
        #[max_length = 50]
        status -> Varchar,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
        #[max_length = 255]
        stripe_invoice_id -> Nullable<Varchar>,
        #[max_length = 255]
        stripe_payment_intent_id -> Nullable<Varchar>,
        #[max_length = 50]
        payment_status -> Nullable<Varchar>,
    }
}

diesel::table! {
    email_logs (id) {
        id -> Uuid,
        user_id -> Nullable<Uuid>,
        #[max_length = 50]
        email_type -> Varchar,
        #[max_length = 255]
        recipient -> Varchar,
        #[max_length = 255]
        subject -> Varchar,
        body -> Text,
        #[max_length = 50]
        status -> Varchar,
        #[max_length = 50]
        provider -> Varchar,
        #[max_length = 255]
        provider_message_id -> Nullable<Varchar>,
        sent_at -> Nullable<Timestamptz>,
        error_message -> Nullable<Text>,
    }
}

diesel::table! {
    locales (id) {
        id -> Uuid,
        #[max_length = 10]
        code -> Varchar,
        #[max_length = 100]
        name -> Varchar,
        #[max_length = 100]
        native_name -> Varchar,
        #[max_length = 3]
        text_direction -> Nullable<Varchar>,
        is_active -> Nullable<Bool>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    organization_members (id) {
        id -> Uuid,
        organization_id -> Uuid,
        user_id -> Uuid,
        #[max_length = 50]
        role -> Varchar,
        invited_at -> Nullable<Timestamptz>,
        joined_at -> Nullable<Timestamptz>,
        is_active -> Nullable<Bool>,
    }
}

diesel::table! {
    organization_usage (id) {
        id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 100]
        metric_name -> Varchar,
        current_value -> Int4,
        limit_value -> Int4,
        billing_period_start -> Timestamptz,
        billing_period_end -> Timestamptz,
        last_updated -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    organizations (id) {
        id -> Uuid,
        #[max_length = 255]
        name -> Varchar,
        #[max_length = 100]
        slug -> Varchar,
        owner_id -> Uuid,
        #[max_length = 50]
        subscription_tier -> Varchar,
        #[max_length = 20]
        subscription_status -> Nullable<Varchar>,
        billing_period_start -> Nullable<Timestamptz>,
        billing_period_end -> Nullable<Timestamptz>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
        deleted_at -> Nullable<Timestamptz>,
        #[max_length = 255]
        stripe_customer_id -> Nullable<Varchar>,
        #[max_length = 255]
        stripe_subscription_id -> Nullable<Varchar>,
        ai_credits_monthly_allowance -> Nullable<Int4>,
        ai_credits_remaining -> Nullable<Int4>,
        ai_credits_reset_date -> Nullable<Timestamptz>,
        description -> Nullable<Text>,
        #[max_length = 255]
        logo_url -> Nullable<Varchar>,
        #[max_length = 255]
        website -> Nullable<Varchar>,
        subscription_tier_id -> Nullable<Uuid>,
        subscription_auto_renew -> Nullable<Bool>,
    }
}

diesel::table! {
    payment_methods (id) {
        id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 255]
        stripe_payment_method_id -> Varchar,
        #[max_length = 50]
        card_brand -> Nullable<Varchar>,
        #[max_length = 4]
        card_last4 -> Nullable<Varchar>,
        card_exp_month -> Nullable<Int4>,
        card_exp_year -> Nullable<Int4>,
        is_default -> Nullable<Bool>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    permission_audit_logs (id) {
        id -> Uuid,
        api_key_id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 255]
        permission_key -> Varchar,
        #[max_length = 255]
        resource_path -> Varchar,
        #[max_length = 10]
        method -> Varchar,
        granted -> Bool,
        created_at -> Timestamptz,
    }
}

diesel::table! {
    permission_groups (id) {
        id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 255]
        name -> Varchar,
        description -> Nullable<Text>,
        permissions -> Jsonb,
        created_by -> Uuid,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    project_locales (id) {
        id -> Uuid,
        project_id -> Uuid,
        locale_id -> Uuid,
        is_source -> Nullable<Bool>,
        is_active -> Nullable<Bool>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    projects (id) {
        id -> Uuid,
        organization_id -> Uuid,
        #[max_length = 255]
        name -> Varchar,
        #[max_length = 100]
        slug -> Varchar,
        description -> Nullable<Text>,
        #[max_length = 10]
        default_locale -> Nullable<Varchar>,
        is_public -> Nullable<Bool>,
        created_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
        deleted_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    resources (id) {
        id -> Uuid,
        project_id -> Uuid,
        #[max_length = 255]
        name -> Varchar,
        #[sql_name = "type"]
        #[max_length = 50]
        type_ -> Varchar,
        #[max_length = 255]
        path -> Nullable<Varchar>,
        description -> Nullable<Text>,
        created_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
        deleted_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    subscription_tiers (id) {
        id -> Uuid,
        #[max_length = 50]
        tier_name -> Varchar,
        #[max_length = 100]
        display_name -> Varchar,
        description -> Nullable<Text>,
        monthly_price -> Numeric,
        yearly_price -> Nullable<Numeric>,
        limits -> Jsonb,
        features -> Jsonb,
        is_active -> Nullable<Bool>,
        sort_order -> Nullable<Int4>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    translation_history (id) {
        id -> Uuid,
        translation_id -> Uuid,
        content -> Text,
        #[max_length = 50]
        action -> Varchar,
        performed_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    translation_keys (id) {
        id -> Uuid,
        resource_id -> Nullable<Uuid>,
        #[max_length = 255]
        key_name -> Varchar,
        description -> Nullable<Text>,
        context -> Nullable<Text>,
        is_plural -> Nullable<Bool>,
        max_length -> Nullable<Int4>,
        #[max_length = 255]
        screenshot_url -> Nullable<Varchar>,
        created_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
        deleted_at -> Nullable<Timestamptz>,
        project_id -> Nullable<Uuid>,
    }
}

diesel::table! {
    translations (id) {
        id -> Uuid,
        key_id -> Uuid,
        locale_id -> Uuid,
        content -> Text,
        is_fuzzy -> Nullable<Bool>,
        is_reviewed -> Nullable<Bool>,
        reviewed_by -> Nullable<Uuid>,
        reviewed_at -> Nullable<Timestamptz>,
        created_by -> Nullable<Uuid>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::table! {
    users (id) {
        id -> Uuid,
        #[max_length = 255]
        email -> Varchar,
        #[max_length = 100]
        username -> Varchar,
        #[max_length = 255]
        password_hash -> Varchar,
        #[max_length = 255]
        full_name -> Nullable<Varchar>,
        #[max_length = 255]
        profile_image_url -> Nullable<Varchar>,
        #[max_length = 10]
        preferred_language -> Nullable<Varchar>,
        is_active -> Nullable<Bool>,
        email_verified -> Nullable<Bool>,
        last_login_at -> Nullable<Timestamptz>,
        created_at -> Nullable<Timestamptz>,
        updated_at -> Nullable<Timestamptz>,
    }
}

diesel::joinable!(ai_credit_transactions -> organizations (organization_id));
diesel::joinable!(ai_credit_transactions -> users (created_by));
diesel::joinable!(ai_credit_usage -> organizations (organization_id));
diesel::joinable!(ai_credit_usage -> users (user_id));
diesel::joinable!(api_call_logs -> api_keys (api_key_id));
diesel::joinable!(api_call_logs -> organizations (organization_id));
diesel::joinable!(api_keys -> organizations (organization_id));
diesel::joinable!(api_keys -> users (created_by));
diesel::joinable!(billing_events -> organizations (organization_id));
diesel::joinable!(email_logs -> users (user_id));
diesel::joinable!(organization_members -> organizations (organization_id));
diesel::joinable!(organization_members -> users (user_id));
diesel::joinable!(organization_usage -> organizations (organization_id));
diesel::joinable!(organizations -> users (owner_id));
diesel::joinable!(payment_methods -> organizations (organization_id));
diesel::joinable!(permission_audit_logs -> api_keys (api_key_id));
diesel::joinable!(permission_audit_logs -> organizations (organization_id));
diesel::joinable!(permission_groups -> organizations (organization_id));
diesel::joinable!(permission_groups -> users (created_by));
diesel::joinable!(project_locales -> locales (locale_id));
diesel::joinable!(project_locales -> projects (project_id));
diesel::joinable!(projects -> organizations (organization_id));
diesel::joinable!(projects -> users (created_by));
diesel::joinable!(resources -> projects (project_id));
diesel::joinable!(resources -> users (created_by));
diesel::joinable!(translation_history -> translations (translation_id));
diesel::joinable!(translation_history -> users (performed_by));
diesel::joinable!(translation_keys -> projects (project_id));
diesel::joinable!(translation_keys -> resources (resource_id));
diesel::joinable!(translation_keys -> users (created_by));
diesel::joinable!(translations -> locales (locale_id));
diesel::joinable!(translations -> translation_keys (key_id));

diesel::allow_tables_to_appear_in_same_query!(
    ai_credit_pricing,
    ai_credit_transactions,
    ai_credit_usage,
    api_call_logs,
    api_keys,
    billing_events,
    email_logs,
    locales,
    organization_members,
    organization_usage,
    organizations,
    payment_methods,
    permission_audit_logs,
    permission_groups,
    project_locales,
    projects,
    resources,
    subscription_tiers,
    translation_history,
    translation_keys,
    translations,
    users,
);
