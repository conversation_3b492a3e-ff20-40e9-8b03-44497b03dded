use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use serde_json::Value;

#[derive(Debug, Deserialize, Serialize)]
pub struct Event {
    pub id: String,
    pub object: String,
    #[serde(rename = "type")]
    pub type_str: String,
    pub data: EventData,
}

#[derive(Debug, Deserialize, Serialize)]
pub struct EventData {
    pub object: Value,
}

pub struct Webhook;

impl Webhook {
    pub fn construct_event(
        payload: &str,
        signature: &str,
        secret: &str,
    ) -> Result<Event> {
        // Verify the signature
        // This is a simplified implementation of the signature verification
        // In a production environment, you would use a more robust method

        // Extract timestamp and signatures from the signature header
        let parts: Vec<&str> = signature.split(',').collect();
        if parts.len() < 2 {
            return Err(anyhow!("Invalid signature format"));
        }

        let timestamp_str = parts[0].trim().strip_prefix("t=").unwrap_or("");
        let signature_str = parts[1].trim().strip_prefix("v1=").unwrap_or("");

        if timestamp_str.is_empty() || signature_str.is_empty() {
            return Err(anyhow!("Invalid signature components"));
        }

        // In a real implementation, you would:
        // 1. Parse the timestamp
        // 2. Verify it's not too old (tolerance of 5 minutes)
        // 3. Compute the expected signature using HMAC with SHA-256
        // 4. Compare with the provided signature

        // For now, we'll just log the verification attempt and parse the payload
        println!("Verifying webhook signature: {}", signature);
        println!("Using webhook secret: {}", secret);

        // Parse the JSON
        serde_json::from_str(payload)
            .map_err(|e| anyhow!("Failed to parse webhook payload: {}", e))
    }
}

// Helper functions for processing specific event types
pub mod handlers {
    use super::*;
    use anyhow::Result;
    use chrono::{DateTime, Utc};
    use uuid::Uuid;

    pub fn extract_subscription_data(event: &Event) -> Result<SubscriptionData> {
        let subscription_json = &event.data.object;

        let subscription_id = subscription_json.get("id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing subscription ID in webhook"))?
            .to_string();

        let status = subscription_json.get("status")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing subscription status in webhook"))?
            .to_string();

        let customer_id = subscription_json.get("customer")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing customer ID in webhook"))?
            .to_string();

        let current_period_end = subscription_json.get("current_period_end")
            .and_then(|v| v.as_i64())
            .map(|ts| DateTime::<Utc>::from_timestamp(ts, 0).unwrap_or_else(|| Utc::now()));

        let cancel_at_period_end = subscription_json.get("cancel_at_period_end")
            .and_then(|v| v.as_bool())
            .unwrap_or(false);

        Ok(SubscriptionData {
            subscription_id,
            status,
            customer_id,
            current_period_end,
            cancel_at_period_end,
        })
    }

    pub fn extract_invoice_data(event: &Event) -> Result<InvoiceData> {
        let invoice_json = &event.data.object;

        let invoice_id = invoice_json.get("id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing invoice ID in webhook"))?
            .to_string();

        let customer_id = invoice_json.get("customer")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing customer ID in webhook"))?
            .to_string();

        let subscription_id = invoice_json.get("subscription")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let amount_paid = invoice_json.get("amount_paid")
            .and_then(|v| v.as_i64())
            .unwrap_or(0);

        let status = invoice_json.get("status")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing invoice status in webhook"))?
            .to_string();

        let payment_intent_id = invoice_json.get("payment_intent")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let metadata = invoice_json.get("metadata")
            .and_then(|v| v.as_object())
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            })
            .unwrap_or_default();

        Ok(InvoiceData {
            invoice_id,
            customer_id,
            subscription_id,
            amount_paid,
            status,
            payment_intent_id,
            metadata,
        })
    }

    pub fn extract_payment_intent_data(event: &Event) -> Result<PaymentIntentData> {
        let payment_intent_json = &event.data.object;

        let payment_intent_id = payment_intent_json.get("id")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing payment intent ID in webhook"))?
            .to_string();

        let customer_id = payment_intent_json.get("customer")
            .and_then(|v| v.as_str())
            .map(|s| s.to_string());

        let amount = payment_intent_json.get("amount")
            .and_then(|v| v.as_i64())
            .unwrap_or(0);

        let status = payment_intent_json.get("status")
            .and_then(|v| v.as_str())
            .ok_or_else(|| anyhow!("Missing payment intent status in webhook"))?
            .to_string();

        let metadata = payment_intent_json.get("metadata")
            .and_then(|v| v.as_object())
            .map(|obj| {
                obj.iter()
                    .filter_map(|(k, v)| v.as_str().map(|s| (k.clone(), s.to_string())))
                    .collect()
            })
            .unwrap_or_default();

        Ok(PaymentIntentData {
            payment_intent_id,
            customer_id,
            amount,
            status,
            metadata,
        })
    }

    #[derive(Debug)]
    pub struct SubscriptionData {
        pub subscription_id: String,
        pub status: String,
        pub customer_id: String,
        pub current_period_end: Option<DateTime<Utc>>,
        pub cancel_at_period_end: bool,
    }

    #[derive(Debug)]
    pub struct InvoiceData {
        pub invoice_id: String,
        pub customer_id: String,
        pub subscription_id: Option<String>,
        pub amount_paid: i64,
        pub status: String,
        pub payment_intent_id: Option<String>,
        pub metadata: std::collections::HashMap<String, String>,
    }

    #[derive(Debug)]
    pub struct PaymentIntentData {
        pub payment_intent_id: String,
        pub customer_id: Option<String>,
        pub amount: i64,
        pub status: String,
        pub metadata: std::collections::HashMap<String, String>,
    }
}
