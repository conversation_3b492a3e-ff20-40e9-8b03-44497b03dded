use anyhow::{anyhow, Result};
use std::env;
use std::collections::HashMap;
use uuid::Uuid;
use chrono::Utc;
use reqwest::{Client, header};
use serde::{Deserialize, Serialize};

use crate::payment::stripe::{
    Customer, Subscription, PaymentIntent, SubscriptionStatus, PaymentIntentStatus
};

#[derive(Debug, Serialize, Deserialize)]
struct StripeCustomerResponse {
    id: String,
    email: Option<String>,
    name: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct StripeSubscriptionResponse {
    id: String,
    status: String,
    current_period_end: u64,
    cancel_at_period_end: bool,
}

#[derive(Debug, Serialize, Deserialize)]
struct StripePaymentIntentResponse {
    id: String,
    client_secret: String,
    amount: u64,
    currency: String,
    status: String,
}

pub struct StripeService {
    client: Client,
    api_key: String,
    price_ids: HashMap<String, String>, // Maps tier_name to price_id
}

impl StripeService {
    pub fn new() -> Result<Self> {
        let api_key = env::var("STRIPE_SECRET_KEY")
            .map_err(|_| anyhow!("STRIPE_SECRET_KEY must be set"))?;

        // Create HTTP client with default headers
        let mut headers = header::HeaderMap::new();
        let auth_value = format!("Bearer {}", api_key);
        let mut auth_header = header::HeaderValue::from_str(&auth_value)
            .map_err(|_| anyhow!("Invalid API key format"))?;
        auth_header.set_sensitive(true);
        headers.insert(header::AUTHORIZATION, auth_header);
        headers.insert(header::CONTENT_TYPE, header::HeaderValue::from_static("application/x-www-form-urlencoded"));

        let client = Client::builder()
            .default_headers(headers)
            .build()
            .map_err(|e| anyhow!("Failed to create HTTP client: {}", e))?;

        // Load price IDs from environment variables
        let mut price_ids = HashMap::new();

        // Free tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_FREE") {
            price_ids.insert("free".to_string(), price_id);
        }

        // Standard tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_STANDARD") {
            price_ids.insert("standard_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_STANDARD_YEARLY") {
            price_ids.insert("standard_yearly".to_string(), price_id);
        }

        // Premium tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_PREMIUM") {
            price_ids.insert("premium_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_PREMIUM_YEARLY") {
            price_ids.insert("premium_yearly".to_string(), price_id);
        }

        // Enterprise tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_ENTERPRISE") {
            price_ids.insert("enterprise_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_ENTERPRISE_YEARLY") {
            price_ids.insert("enterprise_yearly".to_string(), price_id);
        }

        // Add starter, pro, enterprise tiers (for compatibility with the current code)
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_STARTER") {
            price_ids.insert("starter_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_STARTER_YEARLY") {
            price_ids.insert("starter_yearly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_PRO") {
            price_ids.insert("pro_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_PRO_YEARLY") {
            price_ids.insert("pro_yearly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_ENTERPRISE") {
            price_ids.insert("enterprise_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_ENTERPRISE_YEARLY") {
            price_ids.insert("enterprise_yearly".to_string(), price_id);
        }

        Ok(Self {
            client,
            api_key,
            price_ids,
        })
    }

    /// Creates a mock implementation of the Stripe service for development/testing
    pub fn mock() -> Self {
        // Create a default HTTP client
        let client = Client::new();

        // Create mock price IDs
        let mut price_ids = HashMap::new();
        price_ids.insert("free".to_string(), "price_free".to_string());
        price_ids.insert("starter_monthly".to_string(), "price_starter_monthly".to_string());
        price_ids.insert("starter_yearly".to_string(), "price_starter_yearly".to_string());
        price_ids.insert("pro_monthly".to_string(), "price_pro_monthly".to_string());
        price_ids.insert("pro_yearly".to_string(), "price_pro_yearly".to_string());
        price_ids.insert("enterprise_monthly".to_string(), "price_enterprise_monthly".to_string());
        price_ids.insert("enterprise_yearly".to_string(), "price_enterprise_yearly".to_string());

        // Also add standard/premium tiers for compatibility
        price_ids.insert("standard_monthly".to_string(), "price_standard_monthly".to_string());
        price_ids.insert("standard_yearly".to_string(), "price_standard_yearly".to_string());
        price_ids.insert("premium_monthly".to_string(), "price_premium_monthly".to_string());
        price_ids.insert("premium_yearly".to_string(), "price_premium_yearly".to_string());

        Self {
            client,
            api_key: "sk_test_mock".to_string(),
            price_ids,
        }
    }

    pub async fn create_customer(&self, email: &str, name: &str, org_id: Uuid) -> Result<Customer> {
        let mut params = HashMap::new();
        params.insert("email".to_string(), email.to_string());
        params.insert("name".to_string(), name.to_string());

        // Create a string that lives for the duration of the function
        let org_id_str = org_id.to_string();
        params.insert("metadata[organization_id]".to_string(), org_id_str);

        let response = self.client.post("https://api.stripe.com/v1/customers")
            .form(&params)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to create Stripe customer: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Stripe API error: {}", error_text));
        }

        let customer: StripeCustomerResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Stripe customer response: {}", e))?;

        Ok(Customer {
            id: customer.id,
        })
    }

    pub async fn create_subscription(
        &self,
        customer_id: &str,
        price_id: &str,
        payment_method_id: Option<&str>
    ) -> Result<Subscription> {
        let mut params = HashMap::new();
        params.insert("customer".to_string(), customer_id.to_string());
        params.insert("items[0][price]".to_string(), price_id.to_string());
        params.insert("items[0][quantity]".to_string(), "1".to_string());

        if let Some(payment_id) = payment_method_id {
            params.insert("default_payment_method".to_string(), payment_id.to_string());
        }

        let response = self.client.post("https://api.stripe.com/v1/subscriptions")
            .form(&params)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to create Stripe subscription: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Stripe API error: {}", error_text));
        }

        let subscription: StripeSubscriptionResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Stripe subscription response: {}", e))?;

        let status = match subscription.status.as_str() {
            "active" => SubscriptionStatus::Active,
            "past_due" => SubscriptionStatus::PastDue,
            "unpaid" => SubscriptionStatus::Unpaid,
            "canceled" => SubscriptionStatus::Canceled,
            "trialing" => SubscriptionStatus::Trialing,
            "incomplete" => SubscriptionStatus::Incomplete,
            "incomplete_expired" => SubscriptionStatus::IncompleteExpired,
            _ => SubscriptionStatus::Incomplete,
        };

        Ok(Subscription {
            id: subscription.id,
            status,
            current_period_end: Some(subscription.current_period_end),
            cancel_at_period_end: Some(subscription.cancel_at_period_end),
        })
    }

    pub fn get_price_id_for_tier(&self, tier_name: &str, is_yearly: bool) -> Result<String> {
        let key = if tier_name == "free" {
            "free".to_string()
        } else {
            format!("{}_{}", tier_name, if is_yearly { "yearly" } else { "monthly" })
        };

        self.price_ids.get(&key)
            .cloned()
            .ok_or_else(|| anyhow!("No price ID found for tier: {} (yearly: {})", tier_name, is_yearly))
    }

    // Implementation of Stripe API methods

    pub async fn cancel_subscription(&self, subscription_id: &str) -> Result<Subscription> {
        let mut params = HashMap::new();
        params.insert("cancel_at_period_end".to_string(), "true".to_string());

        let url = format!("https://api.stripe.com/v1/subscriptions/{}", subscription_id);
        let response = self.client.post(&url)
            .form(&params)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to cancel Stripe subscription: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Stripe API error: {}", error_text));
        }

        let subscription: StripeSubscriptionResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Stripe subscription response: {}", e))?;

        let status = match subscription.status.as_str() {
            "active" => SubscriptionStatus::Active,
            "past_due" => SubscriptionStatus::PastDue,
            "unpaid" => SubscriptionStatus::Unpaid,
            "canceled" => SubscriptionStatus::Canceled,
            "trialing" => SubscriptionStatus::Trialing,
            "incomplete" => SubscriptionStatus::Incomplete,
            "incomplete_expired" => SubscriptionStatus::IncompleteExpired,
            _ => SubscriptionStatus::Incomplete,
        };

        Ok(Subscription {
            id: subscription.id,
            status,
            current_period_end: Some(subscription.current_period_end),
            cancel_at_period_end: Some(subscription.cancel_at_period_end),
        })
    }

    pub async fn get_subscription(&self, subscription_id: &str) -> Result<Subscription> {
        let url = format!("https://api.stripe.com/v1/subscriptions/{}", subscription_id);
        let response = self.client.get(&url)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to retrieve Stripe subscription: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Stripe API error: {}", error_text));
        }

        let subscription: StripeSubscriptionResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Stripe subscription response: {}", e))?;

        let status = match subscription.status.as_str() {
            "active" => SubscriptionStatus::Active,
            "past_due" => SubscriptionStatus::PastDue,
            "unpaid" => SubscriptionStatus::Unpaid,
            "canceled" => SubscriptionStatus::Canceled,
            "trialing" => SubscriptionStatus::Trialing,
            "incomplete" => SubscriptionStatus::Incomplete,
            "incomplete_expired" => SubscriptionStatus::IncompleteExpired,
            _ => SubscriptionStatus::Incomplete,
        };

        Ok(Subscription {
            id: subscription.id,
            status,
            current_period_end: Some(subscription.current_period_end),
            cancel_at_period_end: Some(subscription.cancel_at_period_end),
        })
    }

    pub async fn create_payment_intent(
        &self,
        amount: u64,
        currency: &str,
        customer_id: &str,
        payment_method_id: Option<&str>,
        description: Option<&str>,
        metadata: Option<HashMap<String, String>>
    ) -> Result<PaymentIntent> {
        let mut params = HashMap::new();
        params.insert("amount".to_string(), amount.to_string());
        params.insert("currency".to_string(), currency.to_string());
        params.insert("customer".to_string(), customer_id.to_string());

        if let Some(payment_id) = payment_method_id {
            params.insert("payment_method".to_string(), payment_id.to_string());
            params.insert("confirm".to_string(), "true".to_string());
        }

        if let Some(desc) = description {
            params.insert("description".to_string(), desc.to_string());
        }

        if let Some(meta) = &metadata {
            for (key, value) in meta {
                params.insert(format!("metadata[{}]", key), value.clone());
            }
        }

        let response = self.client.post("https://api.stripe.com/v1/payment_intents")
            .form(&params)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to create Stripe payment intent: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Stripe API error: {}", error_text));
        }

        let payment_intent: StripePaymentIntentResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Stripe payment intent response: {}", e))?;

        let status = match payment_intent.status.as_str() {
            "requires_payment_method" => PaymentIntentStatus::RequiresPaymentMethod,
            "requires_confirmation" => PaymentIntentStatus::RequiresConfirmation,
            "requires_action" => PaymentIntentStatus::RequiresAction,
            "processing" => PaymentIntentStatus::Processing,
            "succeeded" => PaymentIntentStatus::Succeeded,
            "canceled" => PaymentIntentStatus::Canceled,
            _ => PaymentIntentStatus::RequiresPaymentMethod,
        };

        Ok(PaymentIntent {
            id: payment_intent.id,
            client_secret: payment_intent.client_secret,
            amount: payment_intent.amount,
            currency: payment_intent.currency,
            status,
        })
    }

    pub async fn get_payment_intent(&self, payment_intent_id: &str) -> Result<PaymentIntent> {
        let url = format!("https://api.stripe.com/v1/payment_intents/{}", payment_intent_id);
        let response = self.client.get(&url)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to retrieve Stripe payment intent: {}", e))?;

        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Stripe API error: {}", error_text));
        }

        let payment_intent: StripePaymentIntentResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Stripe payment intent response: {}", e))?;

        let status = match payment_intent.status.as_str() {
            "requires_payment_method" => PaymentIntentStatus::RequiresPaymentMethod,
            "requires_confirmation" => PaymentIntentStatus::RequiresConfirmation,
            "requires_action" => PaymentIntentStatus::RequiresAction,
            "processing" => PaymentIntentStatus::Processing,
            "succeeded" => PaymentIntentStatus::Succeeded,
            "canceled" => PaymentIntentStatus::Canceled,
            _ => PaymentIntentStatus::RequiresPaymentMethod,
        };

        Ok(PaymentIntent {
            id: payment_intent.id,
            client_secret: payment_intent.client_secret,
            amount: payment_intent.amount,
            currency: payment_intent.currency,
            status,
        })
    }
}
