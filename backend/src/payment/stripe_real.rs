use anyhow::{anyhow, Result};
use std::env;
use std::collections::HashMap;
use uuid::Uuid;
use stripe::{
    Client, Customer, Subscription, PaymentIntent, 
    CreateCustomer, CreateSubscription, CreateSubscriptionItems,
    CreatePaymentIntent, ConfirmPaymentIntent,
    SubscriptionStatus, PaymentIntentStatus
};

pub struct StripeService {
    client: Client,
    price_ids: HashMap<String, String>, // Maps tier_name to price_id
}

impl StripeService {
    pub fn new() -> Result<Self> {
        let secret_key = env::var("STRIPE_SECRET_KEY")
            .map_err(|_| anyhow!("STRIPE_SECRET_KEY must be set"))?;

        // Initialize the real Stripe client
        let client = Client::new(secret_key);

        // Load price IDs from environment variables
        let mut price_ids = HashMap::new();

        // Free tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_FREE") {
            price_ids.insert("free".to_string(), price_id);
        }

        // Standard tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_STANDARD") {
            price_ids.insert("standard_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_STANDARD_YEARLY") {
            price_ids.insert("standard_yearly".to_string(), price_id);
        }

        // Premium tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_PREMIUM") {
            price_ids.insert("premium_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_PREMIUM_YEARLY") {
            price_ids.insert("premium_yearly".to_string(), price_id);
        }

        // Enterprise tier
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_ENTERPRISE") {
            price_ids.insert("enterprise_monthly".to_string(), price_id);
        }
        if let Ok(price_id) = env::var("STRIPE_PRICE_ID_ENTERPRISE_YEARLY") {
            price_ids.insert("enterprise_yearly".to_string(), price_id);
        }

        // If no price IDs were found, use test price IDs
        if price_ids.is_empty() {
            // Create test price IDs for development
            price_ids.insert("free".to_string(), "price_free".to_string());
            price_ids.insert("standard_monthly".to_string(), "price_1QtW6QCtNXkGk5bXQXXXXXXX".to_string());
            price_ids.insert("standard_yearly".to_string(), "price_1QtW6QCtNXkGk5bXYYYYYYYY".to_string());
            price_ids.insert("premium_monthly".to_string(), "price_1QtW6QCtNXkGk5bXZZZZZZZZ".to_string());
            price_ids.insert("premium_yearly".to_string(), "price_1QtW6QCtNXkGk5bXAAAAAAAa".to_string());
            price_ids.insert("enterprise_monthly".to_string(), "price_1QtW6QCtNXkGk5bXBBBBBBBB".to_string());
            price_ids.insert("enterprise_yearly".to_string(), "price_1QtW6QCtNXkGk5bXCCCCCCCC".to_string());
        }

        Ok(Self {
            client,
            price_ids,
        })
    }

    pub async fn create_customer(&self, email: &str, name: &str, org_id: Uuid) -> Result<Customer> {
        let mut create_customer = CreateCustomer::new();
        create_customer.email = Some(email);
        create_customer.name = Some(name);
        create_customer.metadata = Some([(
            "organization_id".to_string(), 
            org_id.to_string()
        )].iter().cloned().collect());

        self.client.customers()
            .create(&create_customer)
            .await
            .map_err(|e| anyhow!("Failed to create Stripe customer: {}", e))
    }

    pub async fn create_subscription(
        &self,
        customer_id: &str,
        price_id: &str,
        payment_method_id: Option<&str>
    ) -> Result<Subscription> {
        let mut create_subscription = CreateSubscription::new(customer_id);
        create_subscription.items = Some(vec![
            CreateSubscriptionItems {
                price: Some(price_id.to_string()),
                quantity: Some(1),
                ..Default::default()
            }
        ]);

        if let Some(payment_id) = payment_method_id {
            create_subscription.default_payment_method = Some(payment_id);
        }

        self.client.subscriptions()
            .create(&create_subscription)
            .await
            .map_err(|e| anyhow!("Failed to create Stripe subscription: {}", e))
    }

    pub async fn cancel_subscription(&self, subscription_id: &str) -> Result<Subscription> {
        self.client.subscriptions()
            .cancel(subscription_id, &[])
            .await
            .map_err(|e| anyhow!("Failed to cancel Stripe subscription: {}", e))
    }

    pub async fn get_subscription(&self, subscription_id: &str) -> Result<Subscription> {
        self.client.subscriptions()
            .retrieve(subscription_id, &[])
            .await
            .map_err(|e| anyhow!("Failed to retrieve Stripe subscription: {}", e))
    }

    pub fn get_price_id_for_tier(&self, tier_name: &str, is_yearly: bool) -> Result<String> {
        let key = if tier_name == "free" {
            "free".to_string()
        } else {
            format!("{}_{}", tier_name, if is_yearly { "yearly" } else { "monthly" })
        };

        self.price_ids.get(&key)
            .cloned()
            .ok_or_else(|| anyhow!("No price ID found for tier: {} (yearly: {})", tier_name, is_yearly))
    }

    pub async fn create_payment_intent(
        &self,
        amount: u64,
        currency: &str,
        customer_id: &str,
        payment_method_id: Option<&str>,
        description: Option<&str>,
        metadata: Option<HashMap<String, String>>
    ) -> Result<PaymentIntent> {
        let mut create_intent = CreatePaymentIntent::new(amount, currency.parse()?);
        create_intent.customer = Some(customer_id);
        
        if let Some(payment_id) = payment_method_id {
            create_intent.payment_method = Some(payment_id);
        }
        
        if let Some(desc) = description {
            create_intent.description = Some(desc);
        }
        
        if let Some(meta) = metadata {
            create_intent.metadata = Some(meta);
        }

        self.client.payment_intents()
            .create(&create_intent)
            .await
            .map_err(|e| anyhow!("Failed to create Stripe payment intent: {}", e))
    }

    pub async fn confirm_payment_intent(
        &self,
        payment_intent_id: &str,
        payment_method_id: Option<&str>
    ) -> Result<PaymentIntent> {
        let mut confirm_intent = ConfirmPaymentIntent::new();
        
        if let Some(payment_id) = payment_method_id {
            confirm_intent.payment_method = Some(payment_id);
        }

        self.client.payment_intents()
            .confirm(payment_intent_id, &confirm_intent)
            .await
            .map_err(|e| anyhow!("Failed to confirm Stripe payment intent: {}", e))
    }

    pub async fn get_payment_intent(&self, payment_intent_id: &str) -> Result<PaymentIntent> {
        self.client.payment_intents()
            .retrieve(payment_intent_id, &[])
            .await
            .map_err(|e| anyhow!("Failed to retrieve Stripe payment intent: {}", e))
    }
}
