use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::env;
use reqwest::Client;
use std::time::Duration;

use super::translation::{AITranslationRequest, AITranslationResponse};

#[derive(Debug, Serialize, Deserialize)]
struct GeminiContent {
    role: String,
    parts: Vec<GeminiPart>,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiPart {
    text: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiRequest {
    contents: Vec<GeminiContent>,
    generation_config: GeminiGenerationConfig,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiGenerationConfig {
    temperature: f32,
    max_output_tokens: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiResponse {
    candidates: Vec<GeminiCandidate>,
    usage_metadata: Option<GeminiUsageMetadata>,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiCandidate {
    content: GeminiContent,
    finish_reason: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct GeminiUsageMetadata {
    prompt_token_count: Option<i32>,
    candidates_token_count: Option<i32>,
    total_token_count: Option<i32>,
}

pub struct GeminiTranslationService {
    client: Client,
    api_key: String,
    default_model: String,
}

impl GeminiTranslationService {
    pub fn new() -> Result<Self> {
        let api_key = env::var("GEMINI_API_KEY")
            .map_err(|_| anyhow!("GEMINI_API_KEY environment variable not set"))?;
        
        let default_model = env::var("GEMINI_DEFAULT_MODEL")
            .unwrap_or_else(|_| "gemini-2.5-flash-preview-04-17".to_string());
        
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| anyhow!("Failed to create HTTP client: {}", e))?;
        
        Ok(Self {
            client,
            api_key,
            default_model,
        })
    }
    
    pub fn mock() -> Self {
        Self {
            client: Client::new(),
            api_key: "mock_api_key".to_string(),
            default_model: "gemini-2.5-flash-preview-04-17".to_string(),
        }
    }
    
    pub async fn translate(&self, request: &AITranslationRequest) -> Result<AITranslationResponse> {
        // For mock implementation, just return a simulated translation
        if self.api_key == "mock_api_key" {
            return self.mock_translate(request);
        }
        
        let model = request.model.clone().unwrap_or_else(|| self.default_model.clone());
        
        // Create a prompt for translation
        let prompt = format!(
            "You are a professional translator. Translate the following text from {} to {}. \
            Maintain the original meaning, tone, and formatting. \
            Only return the translated text without any additional comments or explanations.",
            request.source_locale, request.target_locale
        );
        
        // Add context if provided
        let user_text = if let Some(context) = &request.context {
            format!(
                "{}\n\nContext: {}\n\nText to translate: {}",
                prompt, context, request.text
            )
        } else {
            format!("{}\n\nText to translate: {}", prompt, request.text)
        };
        
        // Create the Gemini request
        let gemini_request = GeminiRequest {
            contents: vec![
                GeminiContent {
                    role: "user".to_string(),
                    parts: vec![
                        GeminiPart {
                            text: user_text,
                        },
                    ],
                },
            ],
            generation_config: GeminiGenerationConfig {
                temperature: 0.3,
                max_output_tokens: 2000,
            },
        };
        
        // Send the request to Gemini API
        let url = format!(
            "https://generativelanguage.googleapis.com/v1beta/models/{}:generateContent?key={}",
            model, self.api_key
        );
        
        let response = self.client
            .post(&url)
            .header("Content-Type", "application/json")
            .json(&gemini_request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send request to Gemini API: {}", e))?;
        
        // Check if the request was successful
        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("Gemini API error: {}", error_text));
        }
        
        // Parse the response
        let gemini_response: GeminiResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse Gemini response: {}", e))?;
        
        // Extract the translated text
        let translated_text = gemini_response.candidates
            .first()
            .ok_or_else(|| anyhow!("No translation candidates returned"))?
            .content
            .parts
            .first()
            .ok_or_else(|| anyhow!("No content parts in response"))?
            .text
            .clone();
        
        // Calculate credits used based on tokens (if available)
        let credits_used = if let Some(usage) = gemini_response.usage_metadata {
            let total_tokens = usage.total_token_count.unwrap_or(0);
            (total_tokens as f32 / 1000.0).ceil() as i32
        } else {
            // If token count not available, estimate based on text length
            let text_length = request.text.len() as i32;
            (text_length / 100).max(1) // 1 credit per 100 chars, minimum 1
        };
        
        Ok(AITranslationResponse {
            translated_text,
            model_used: model,
            credits_used: credits_used.max(1), // Minimum 1 credit
        })
    }
    
    fn mock_translate(&self, request: &AITranslationRequest) -> Result<AITranslationResponse> {
        // Simple mock translation for testing
        let translated_text = match (request.source_locale.as_str(), request.target_locale.as_str()) {
            ("en", "fr") => format!("Traduit (Gemini): {}", request.text),
            ("en", "es") => format!("Traducido (Gemini): {}", request.text),
            ("en", "de") => format!("Übersetzt (Gemini): {}", request.text),
            ("en", "ja") => format!("翻訳 (Gemini): {}", request.text),
            ("en", "zh") => format!("翻译 (Gemini): {}", request.text),
            _ => format!("Translated (Gemini): {}", request.text),
        };
        
        // Calculate credits based on text length
        let text_length = request.text.len() as i32;
        let credits_used = (text_length / 100).max(1); // 1 credit per 100 chars, minimum 1
        
        Ok(AITranslationResponse {
            translated_text,
            model_used: request.model.clone().unwrap_or_else(|| self.default_model.clone()),
            credits_used,
        })
    }
}
