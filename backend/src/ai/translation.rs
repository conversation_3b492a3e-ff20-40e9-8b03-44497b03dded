use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::env;
use reqwest::Client;
use std::time::Duration;

use super::gemini::GeminiTranslationService;

#[derive(Debug, Serialize, Deserialize)]
pub struct AITranslationRequest {
    pub text: String,
    pub source_locale: String,
    pub target_locale: String,
    pub context: Option<String>,
    pub model: Option<String>,
    pub provider: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AITranslationResponse {
    pub translated_text: String,
    pub model_used: String,
    pub credits_used: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIMessage {
    role: String,
    content: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIRequest {
    model: String,
    messages: Vec<OpenAIMessage>,
    temperature: f32,
    max_tokens: i32,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIResponse {
    id: String,
    object: String,
    created: i64,
    model: String,
    choices: Vec<OpenAIChoice>,
    usage: OpenAIUsage,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIChoice {
    index: i32,
    message: OpenAIMessage,
    finish_reason: String,
}

#[derive(Debug, Serialize, Deserialize)]
struct OpenAIUsage {
    prompt_tokens: i32,
    completion_tokens: i32,
    total_tokens: i32,
}

#[derive(Debug, Clone)]
pub enum AIProvider {
    OpenAI,
    Gemini,
    Mock,
}

pub struct AITranslationService {
    client: Client,
    openai_api_key: String,
    openai_default_model: String,
    gemini_service: Option<GeminiTranslationService>,
    default_provider: AIProvider,
}

impl AITranslationService {
    pub fn new() -> Result<Self> {
        let client = Client::builder()
            .timeout(Duration::from_secs(30))
            .build()
            .map_err(|e| anyhow!("Failed to create HTTP client: {}", e))?;

        // Initialize OpenAI configuration
        let openai_api_key = env::var("OPENAI_API_KEY")
            .unwrap_or_else(|_| "".to_string());

        let openai_default_model = env::var("OPENAI_DEFAULT_MODEL")
            .unwrap_or_else(|_| "gpt-4".to_string());

        // Initialize Gemini service if API key is available
        let gemini_service = match GeminiTranslationService::new() {
            Ok(service) => Some(service),
            Err(e) => {
                eprintln!("Failed to initialize Gemini service: {}", e);
                None
            }
        };

        // Determine default provider based on available API keys
        let default_provider = if !openai_api_key.is_empty() {
            AIProvider::OpenAI
        } else if gemini_service.is_some() {
            AIProvider::Gemini
        } else {
            AIProvider::Mock
        };

        Ok(Self {
            client,
            openai_api_key,
            openai_default_model,
            gemini_service,
            default_provider,
        })
    }

    pub fn mock() -> Self {
        Self {
            client: Client::new(),
            openai_api_key: "mock_api_key".to_string(),
            openai_default_model: "gpt-4".to_string(),
            gemini_service: None,
            default_provider: AIProvider::Mock,
        }
    }

    pub async fn translate(&self, request: &AITranslationRequest) -> Result<AITranslationResponse> {
        // Determine which provider to use
        let provider = match &request.provider {
            Some(provider_str) => {
                match provider_str.to_lowercase().as_str() {
                    "openai" => AIProvider::OpenAI,
                    "gemini" => AIProvider::Gemini,
                    "mock" => AIProvider::Mock,
                    _ => self.default_provider.clone(),
                }
            },
            None => self.default_provider.clone(),
        };

        match provider {
            AIProvider::OpenAI => {
                // Check if OpenAI API key is available
                if self.openai_api_key.is_empty() || self.openai_api_key == "mock_api_key" {
                    return self.mock_translate(request);
                }

                self.translate_with_openai(request).await
            },
            AIProvider::Gemini => {
                // Check if Gemini service is available
                if let Some(gemini_service) = &self.gemini_service {
                    gemini_service.translate(request).await
                } else {
                    // Fall back to mock if Gemini is not available
                    self.mock_translate(request)
                }
            },
            AIProvider::Mock => {
                self.mock_translate(request)
            },
        }
    }

    async fn translate_with_openai(&self, request: &AITranslationRequest) -> Result<AITranslationResponse> {
        let model = request.model.clone().unwrap_or_else(|| self.openai_default_model.clone());

        // Create a system prompt for translation
        let system_prompt = format!(
            "You are a professional translator. Translate the following text from {} to {}. \
            Maintain the original meaning, tone, and formatting. \
            Only return the translated text without any additional comments or explanations.",
            request.source_locale, request.target_locale
        );

        // Add context if provided
        let user_prompt = if let Some(context) = &request.context {
            format!(
                "Context: {}\n\nText to translate: {}",
                context, request.text
            )
        } else {
            format!("Text to translate: {}", request.text)
        };

        // Create the OpenAI request
        let openai_request = OpenAIRequest {
            model: model.clone(),
            messages: vec![
                OpenAIMessage {
                    role: "system".to_string(),
                    content: system_prompt,
                },
                OpenAIMessage {
                    role: "user".to_string(),
                    content: user_prompt,
                },
            ],
            temperature: 0.3,
            max_tokens: 2000,
        };

        // Send the request to OpenAI
        let response = self.client
            .post("https://api.openai.com/v1/chat/completions")
            .header("Authorization", format!("Bearer {}", self.openai_api_key))
            .header("Content-Type", "application/json")
            .json(&openai_request)
            .send()
            .await
            .map_err(|e| anyhow!("Failed to send request to OpenAI: {}", e))?;

        // Check if the request was successful
        if !response.status().is_success() {
            let error_text = response.text().await
                .unwrap_or_else(|_| "Unknown error".to_string());
            return Err(anyhow!("OpenAI API error: {}", error_text));
        }

        // Parse the response
        let openai_response: OpenAIResponse = response.json().await
            .map_err(|e| anyhow!("Failed to parse OpenAI response: {}", e))?;

        // Extract the translated text
        let translated_text = openai_response.choices
            .first()
            .ok_or_else(|| anyhow!("No translation choices returned"))?
            .message
            .content
            .clone();

        // Calculate credits used based on tokens
        // This is a simplified calculation - you might want to adjust based on your pricing model
        let credits_used = (openai_response.usage.total_tokens as f32 / 1000.0).ceil() as i32;

        Ok(AITranslationResponse {
            translated_text,
            model_used: model,
            credits_used: credits_used.max(1), // Minimum 1 credit
        })
    }

    fn mock_translate(&self, request: &AITranslationRequest) -> Result<AITranslationResponse> {
        // Simple mock translation for testing
        let translated_text = match (request.source_locale.as_str(), request.target_locale.as_str()) {
            ("en", "fr") => format!("Traduit: {}", request.text),
            ("en", "es") => format!("Traducido: {}", request.text),
            ("en", "de") => format!("Übersetzt: {}", request.text),
            ("en", "ja") => format!("翻訳: {}", request.text),
            ("en", "zh") => format!("翻译: {}", request.text),
            _ => format!("Translated: {}", request.text),
        };

        // Calculate credits based on text length
        let text_length = request.text.len() as i32;
        let credits_used = (text_length / 100).max(1); // 1 credit per 100 chars, minimum 1

        let model = request.model.clone().unwrap_or_else(|| "mock-model".to_string());

        Ok(AITranslationResponse {
            translated_text,
            model_used: model,
            credits_used,
        })
    }
}
