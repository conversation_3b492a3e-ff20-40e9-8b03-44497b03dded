steps:
  # Step 1: Build the Docker image
  - name: 'gcr.io/cloud-builders/docker'
    args: [
      'build',
      '--target', 'prod',
      '-t', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest',
      '.'
    ]
    id: 'build-image'
    timeout: '1800s'

  # Step 2: Push the Docker image to Container Registry
  - name: 'gcr.io/cloud-builders/docker'
    args: ['push', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest']
    id: 'push-image'
    waitFor: ['build-image']

  # Step 3: Deploy to Cloud Run
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'gcloud'
    args: [
      'run', 'deploy', '${_SERVICE_NAME}',
      '--image', 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest',
      '--region', '${_REGION}',
      '--platform', 'managed',
      '--allow-unauthenticated',
      '--memory', '${_MEMORY}',
      '--cpu', '${_CPU}',
      '--min-instances', '${_MIN_INSTANCES}',
      '--max-instances', '${_MAX_INSTANCES}',
      '--set-env-vars', 'ROCKET_ADDRESS=0.0.0.0,ROCKET_PORT=8080,ROCKET_LOG_LEVEL=normal,APP_URL=${_APP_URL},APP_NAME=ADC_Multi_Languages,EMAIL_SENDER_NAME=${_EMAIL_SENDER_NAME},EMAIL_SENDER_ADDRESS=${_EMAIL_SENDER_ADDRESS},GOOGLE_CLOUD_PROJECT=$PROJECT_ID,GOOGLE_CLIENT_EMAIL=${_GOOGLE_CLIENT_EMAIL},GCS_BUCKET_NAME=${_GCS_BUCKET_NAME},NOTIFICATION_CLOUD_FUNCTION_URL=${_NOTIFICATION_CLOUD_FUNCTION_URL},DATABASE_URL=${_DATABASE_URL}',
      '--set-secrets', 'JWT_SECRET=${_SERVICE_NAME}-jwt-secret:latest,GOOGLE_PRIVATE_KEY=${_SERVICE_NAME}-google-private-key:latest,BREVO_API_KEY=${_SERVICE_NAME}-brevo-api-key:latest,NOTIFICATION_SERVICE_API_KEY=${_SERVICE_NAME}-notification-api-key:latest,STRIPE_SECRET_KEY=${_SERVICE_NAME}-stripe-secret-key:latest'
    ]
    id: 'deploy-to-cloud-run'
    waitFor: ['push-image']

  # Step 4 (Optional): Run database migrations
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    entrypoint: 'bash'
    args: ['-c', '
      if [ "${_RUN_MIGRATIONS}" = "true" ]; then
        echo "Running database migrations...";
        gcloud run jobs create ${_SERVICE_NAME}-migrations \
          --image gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest \
          --region ${_REGION} \
          --command /app/run-migrations.sh \
          --set-env-vars "DATABASE_URL=${_DATABASE_URL}" \
          --memory ${_MIGRATIONS_MEMORY} \
          --cpu ${_MIGRATIONS_CPU} \
          --max-retries 3 \
          --task-timeout 10m \
          --execute-now;
      else
        echo "Skipping migrations as _RUN_MIGRATIONS is not set to true";
      fi
    ']
    id: 'run-migrations'
    waitFor: ['deploy-to-cloud-run']

# Images to be pushed to Container Registry
images:
  - 'gcr.io/$PROJECT_ID/${_SERVICE_NAME}:latest'

# Substitution variables with default values
substitutions:
  _SERVICE_NAME: 'adc-muti-languages'
  _REGION: 'asia-southeast1'
  _MEMORY: '256Mi'
  _CPU: '1'
  _MIN_INSTANCES: '0'
  _MAX_INSTANCES: '1'
  _RUN_MIGRATIONS: 'false'
  _MIGRATIONS_MEMORY: '256Mi'
  _MIGRATIONS_CPU: '1'
  _APP_URL: 'https://adc-muti-languages-${_VERSION}-${PROJECT_ID}.${_REGION}.run.app'
  _EMAIL_SENDER_NAME: 'ADC Multi-Languages'
  _EMAIL_SENDER_ADDRESS: '<EMAIL>'
  _GOOGLE_CLIENT_EMAIL: 'adc-muti-languages@${PROJECT_ID}.iam.gserviceaccount.com'
  _GCS_BUCKET_NAME: '${PROJECT_ID}-storage'
  _NOTIFICATION_CLOUD_FUNCTION_URL: 'https://us-central1-${PROJECT_ID}.cloudfunctions.net/handleNotification'
  _DATABASE_URL: 'postgresql://postgres.hsroaoiorfxgwzdiwsmh:<EMAIL>:5432/postgres'

# Timeout for the entire build process
timeout: '3600s'

# Options for the build
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
