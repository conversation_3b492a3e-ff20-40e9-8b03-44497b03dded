# ADC Multi-Languages

A translation management system built with Rust and the Rocket framework, using Supabase for authentication and storage.

## Features

- **Multi-language Support**: Manage translations across multiple languages
- **Project Management**: Organize translations by projects and resources
- **Tiered Subscription System**: Free, Standard, Premium, and Enterprise tiers
- **User Management**: Authentication and user profiles
- **Storage Integration**: File storage via Google Cloud Storage
- **AI Translation**: Powered by Google's Gemini AI
- **AI Credits System**: Purchase and manage AI translation credits
- **Webhook-based Payment Processing**: Process payments via webhooks
- **RESTful API**: Comprehensive API for all operations

## Prerequisites

- Rust (latest stable version)
- Cargo (comes with Rust)
- PostgreSQL database
- Google Cloud Storage account (for image storage)
- Google Gemini API key (for AI translation)

## Getting Started

### Option 1: Using the Mock API

If you're experiencing issues with the database schema, you can use the mock API which provides static responses for testing and development purposes.

1. Build the Docker image:
   ```
   docker build -t adc-muti-languages-nginx -f Dockerfile.nginx .
   ```

2. Run the Docker container:
   ```
   docker run -p 8080:8080 adc-muti-languages-nginx
   ```

3. Access the mock API at http://localhost:8080

### Option 2: Running the Full Application

1. Clone the repository:
   ```
   git clone <repository-url>
   cd adc-muti-languages
   ```

2. Set up environment variables:
   Create a `.env` file in the project root with the following variables:
   ```
   # Database
   DATABASE_URL=postgresql://username:password@localhost:5432/adc_multi_languages

   # JWT
   JWT_SECRET=your_jwt_secret_key
   JWT_EXPIRATION=86400  # 24 hours in seconds
   JWT_REFRESH_EXPIRATION=604800  # 7 days in seconds

   # Google Cloud
   GOOGLE_APPLICATION_CREDENTIALS=path/to/your/credentials.json
   GOOGLE_CLOUD_PROJECT=your-project-id
   GOOGLE_CLOUD_STORAGE_BUCKET=your-bucket-name

   # Email (Brevo)
   BREVO_API_KEY=your_brevo_api_key
   EMAIL_SENDER_ADDRESS=<EMAIL>
   EMAIL_SENDER_NAME=ADC Multi-Languages

   # AI Translation
   GEMINI_API_KEY=your_gemini_api_key

   # Server
   ROCKET_ADDRESS=127.0.0.1
   ROCKET_PORT=8000
   ROCKET_STATIC_DIR=static
   ```

3. Set up the database:
   ```
   cargo install diesel_cli --no-default-features --features postgres
   diesel setup
   diesel migration run
   ```

4. Set up subscription plans:
   ```
   chmod +x scripts/setup_subscription_plans.sh
   ./scripts/setup_subscription_plans.sh
   ```
   This creates the default subscription plans in the database.

5. (Optional) Seed the database with test data:
   ```
   chmod +x db/seed.sh
   ./db/seed.sh
   ```
   This creates test users, organizations, projects, and translations for development.

6. Build and run the application:
   ```
   cargo run
   ```

6. Access the application:
   - API: http://localhost:8000
   - Health check: http://localhost:8000/health

## Project Structure

- `src/main.rs`: Main application entry point
- `src/models/`: Database models
- `src/schema.rs`: Diesel schema definitions
- `src/repositories/`: Database access layer
- `src/routes/`: API route handlers
- `src/auth/`: Authentication and authorization
- `src/storage/`: File storage integration
- `src/db.rs`: Database connection management
- `db/migrations/`: Database migrations
- `db/schema.md`: Database schema documentation
- `static/`: Static assets

## Payment Processing with Webhooks

The application uses webhooks for payment processing instead of a direct SDK integration. This approach provides several benefits:

1. **Decoupling**: The backend doesn't need to integrate directly with payment provider SDKs
2. **Flexibility**: Can easily switch payment providers without major code changes
3. **Security**: Payment details are handled by the payment provider, not our application
4. **Reliability**: Webhooks ensure payment status is updated even if the user closes their browser

### How it Works

1. When a user purchases AI credits:
   - The frontend calls `POST /api/organizations/:id/ai-credits/purchase` to get pricing information
   - The frontend handles the payment process with the payment provider
   - The payment provider sends a webhook notification to `/api/webhooks/stripe`
   - The webhook handler processes the payment and adds credits to the organization

2. For subscription management:
   - Subscription status changes trigger webhooks
   - The webhook handler updates the organization's subscription status
   - Monthly subscription renewals automatically reset AI credits

### Setting Up Webhooks

To set up webhooks for your payment provider:

1. Create a webhook endpoint in your payment provider dashboard
2. Point it to `https://your-api-domain.com/api/webhooks/stripe`
3. Ensure your server is publicly accessible or use a service like ngrok for local testing

## API Endpoints

### Authentication
- `POST /api/auth/signup`: Register a new user
- `POST /api/auth/signin`: Sign in a user
- `POST /api/auth/refresh-token`: Refresh JWT token
- `POST /api/auth/request-password-reset`: Request password reset
- `POST /api/auth/confirm-password-reset`: Confirm password reset
- `POST /api/auth/request-email-verification`: Request email verification
- `POST /api/auth/verify-email`: Verify email

### Users
- `GET /api/users/profile`: Get current user profile
- `PUT /api/users/profile`: Update current user profile
- `GET /api/users/:id`: Get user by ID

### Organizations
- `GET /api/organizations`: List user's organizations
- `POST /api/organizations`: Create a new organization
- `GET /api/organizations/:id`: Get organization details
- `PUT /api/organizations/:id`: Update organization
- `GET /api/organizations/:id/ai-credits`: Get organization's AI credits
- `POST /api/organizations/:id/ai-credits/purchase`: Purchase AI credits
- `GET /api/organizations/:id/ai-credits/history`: Get AI credits usage history

### Projects
- `GET /api/projects`: List projects
- `POST /api/projects`: Create a new project
- `GET /api/projects/:id`: Get project details
- `PUT /api/projects/:id`: Update project
- `DELETE /api/projects/:id`: Delete project
- `GET /api/projects/:id/locales`: List project locales
- `POST /api/projects/:id/locales`: Add locale to project
- `POST /api/projects/:id/resources`: Create a resource
- `GET /api/projects/:id/resources`: List resources

### Translations
- `GET /api/translations/keys`: List translation keys
- `POST /api/translations/keys`: Create a translation key
- `GET /api/translations/keys/:id`: Get translation key details
- `POST /api/translations`: Create a translation
- `GET /api/translations`: List translations
- `GET /api/translations/:id`: Get translation details
- `PUT /api/translations/:id`: Update translation
- `GET /api/translations/:id/history`: Get translation history
- `POST /api/translations/ai-translate`: Translate using AI

### Subscription Plans
- `GET /api/subscription-plans`: List subscription plans

### Webhooks
- `POST /api/webhooks/stripe`: Stripe webhook endpoint

### Mock API Endpoints

The mock API provides static responses for the following endpoints:

#### Authentication
- `POST /api/auth/signup`: Register a new user
  - Response: `{"status":"success","message":"User created successfully","data":{"id":1,"email":"<EMAIL>","name":"User"}}`
- `POST /api/auth/login`: Sign in a user
  - Response: `{"status":"success","message":"Login successful","data":{"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6IlVzZXIiLCJpYXQiOjE1MTYyMzkwMjJ9.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"}}`

#### Organizations
- `GET /api/organizations`: List organizations
  - Response: `{"status":"success","message":"Organizations retrieved successfully","data":[{"id":1,"name":"Organization 1","description":"Description 1"},{"id":2,"name":"Organization 2","description":"Description 2"}]}`
- `GET /api/organizations/:id`: Get organization by ID
  - Response: `{"status":"success","message":"Organization retrieved successfully","data":{"id":1,"name":"Organization 1","description":"Description 1"}}`

#### Users
- `GET /api/users`: List users
  - Response: `{"status":"success","message":"Users retrieved successfully","data":[{"id":1,"email":"<EMAIL>","name":"User"},{"id":2,"email":"<EMAIL>","name":"User 2"}]}`
- `GET /api/users/:id`: Get user by ID
  - Response: `{"status":"success","message":"User retrieved successfully","data":{"id":1,"email":"<EMAIL>","name":"User"}}`

## Development

To run the application in development mode with auto-reload:

```
cargo install cargo-watch
cargo watch -x run
```

## Testing

### Automated Tests

Run the tests with:

```
cargo test
```

### API Testing with Postman

A Postman collection and environment are provided for testing the API:

1. Import the collection from `postman/adc-multi-languages.postman_collection.json`
2. Import the environment from `postman/adc-multi-languages.postman_environment.json`
3. Select the "ADC Multi-Languages Local" environment
4. Start with the "Sign In" request in the Authentication folder using these credentials:
   - Email: <EMAIL>
   - Password: password123

The collection includes automatic token extraction, so after signing in, the access token will be saved to the environment variables and used for subsequent requests.

## Database Schema

See [db/schema.md](db/schema.md) for detailed database schema documentation.

## Deployment

### Deploying to Google Cloud Run

#### Option 1: Using the Deployment Script

The application can be deployed to Google Cloud Run using the provided deployment script:

1. Authenticate with Google Cloud:
   ```
   gcloud auth login
   ```

2. Set the project ID:
   ```
   gcloud config set project YOUR_PROJECT_ID
   ```

3. Run the deployment script:
   ```
   ./deploy-to-cloud-run.sh
   ```

This will:
- Build the Docker image using the `Dockerfile.deploy` file
- Push it to Google Container Registry
- Create necessary secrets if they don't exist
- Deploy the application to Google Cloud Run

#### Option 2: Using Cloud Build

Alternatively, you can use Google Cloud Build to build and deploy the application:

1. Authenticate with Google Cloud:
   ```
   gcloud auth login
   ```

2. Set the project ID:
   ```
   gcloud config set project YOUR_PROJECT_ID
   ```

3. Submit the build:
   ```
   gcloud builds submit --config cloudbuild.yaml
   ```

This will build the Docker image using the `Dockerfile.deploy` file and deploy it to Google Cloud Run.

### Database Migrations

#### Setting Up the Migration Job

To set up a Cloud Run job for database migrations:

```bash
./setup-migration-job.sh
```

This will:
- Create a Docker image for running migrations
- Push it to Google Container Registry
- Create a Cloud Run job for running migrations

#### Running Migrations Manually

To run migrations manually:

```bash
gcloud run jobs execute db-migrations --region us-central1
```

#### Running Migrations Locally

To run migrations locally:

```bash
export DATABASE_URL=postgresql://username:password@localhost:5432/database_name
./run-migrations.sh
```

## License

[MIT](LICENSE)
