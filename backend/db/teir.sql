-- Enhanced schema with tiered subscription system

-- Create organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    owner_id UUID NOT NULL, -- Reference to users table
    subscription_tier VARCHAR(50) NOT NULL DEFAULT 'free',
    subscription_status VARCHAR(20) DEFAULT 'active', -- active, cancelled, past_due
    billing_period_start TIMESTAMP WITH TIME ZONE,
    billing_period_end TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE NULL
);

-- Create subscription tiers table
CREATE TABLE subscription_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tier_name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    monthly_price DECIMAL(10, 2) NOT NULL DEFAULT 0,
    yearly_price DECIMAL(10, 2),
    limits JSONB NOT NULL DEFAULT '{}',
    features JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create organization members table
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'member', -- admin, member, translator, viewer
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    joined_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(organization_id, user_id)
);

-- Update projects table to reference organizations
ALTER TABLE projects ADD COLUMN organization_id UUID REFERENCES organizations(id);

-- Create usage tracking table
CREATE TABLE organization_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL, -- content_keys, api_calls, storage_mb, etc.
    current_value INTEGER NOT NULL DEFAULT 0,
    limit_value INTEGER NOT NULL DEFAULT 0,
    billing_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    billing_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, metric_name, billing_period_start)
);

-- Create API keys table
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    prefix VARCHAR(20) NOT NULL,
    permissions JSONB NOT NULL DEFAULT '[]',
    rate_limit INTEGER DEFAULT 1000, -- requests per hour
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(key_hash)
);

-- Create rate limiting table
CREATE TABLE api_rate_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    ip_address INET,
    window_start TIMESTAMP WITH TIME ZONE NOT NULL,
    request_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_rate_limits_lookup (api_key_id, window_start),
    INDEX idx_rate_limits_org (organization_id, window_start),
    INDEX idx_rate_limits_ip (ip_address, window_start)
);

-- Create billing events table
CREATE TABLE billing_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    event_type VARCHAR(50) NOT NULL, -- upgrade, downgrade, payment, overage
    old_tier VARCHAR(50),
    new_tier VARCHAR(50),
    amount DECIMAL(10, 2),
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default subscription tiers
INSERT INTO subscription_tiers (tier_name, display_name, description, monthly_price, yearly_price, limits, features)
VALUES
('free', 'Free', 'Perfect for personal projects', 0, 0, 
    '{
        "content_keys": 1000,
        "api_calls_per_hour": 100,
        "projects": 1,
        "team_members": 1,
        "languages": 2,
        "storage_mb": 100,
        "mtranslate_chars": 10000
    }'::jsonb,
    '["Basic translation editor", "2 languages", "API access", "Community support"]'::jsonb),
    
('starter', 'Starter', 'For small teams and startups', 19, 190,
    '{
        "content_keys": 10000,
        "api_calls_per_hour": 1000,
        "projects": 3,
        "team_members": 5,
        "languages": 5,
        "storage_mb": 1000,
        "mtranslate_chars": 100000,
        "version_history": true
    }'::jsonb,
    '["Everything in Free", "Version history", "5 languages", "Team collaboration", "Email support"]'::jsonb),
    
('professional', 'Professional', 'For growing businesses', 99, 990,
    '{
        "content_keys": 50000,
        "api_calls_per_hour": 5000,
        "projects": 10,
        "team_members": 15,
        "languages": 20,
        "storage_mb": 10000,
        "mtranslate_chars": 500000,
        "version_history": true,
        "custom_workflows": true,
        "advanced_analytics": true
    }'::jsonb,
    '["Everything in Starter", "Advanced analytics", "Custom workflows", "Priority support", "SLA guarantee"]'::jsonb),
    
('enterprise', 'Enterprise', 'For large organizations', 499, 4990,
    '{
        "content_keys": -1,
        "api_calls_per_hour": -1,
        "projects": -1,
        "team_members": -1,
        "languages": -1,
        "storage_mb": -1,
        "mtranslate_chars": -1,
        "version_history": true,
        "custom_workflows": true,
        "advanced_analytics": true,
        "sso": true,
        "dedicated_support": true
    }'::jsonb,
    '["Everything in Professional", "Unlimited everything", "SSO", "Custom integrations", "Dedicated support", "Custom SLA"]'::jsonb);

-- Function to check usage limits
CREATE OR REPLACE FUNCTION check_usage_limit(
    p_organization_id UUID,
    p_metric_name VARCHAR,
    p_increment INTEGER DEFAULT 1
)
RETURNS BOOLEAN AS $$
DECLARE
    v_tier_limits JSONB;
    v_current_usage INTEGER;
    v_metric_limit INTEGER;
    v_billing_start TIMESTAMP WITH TIME ZONE;
    v_billing_end TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Get organization tier and billing period
    SELECT 
        st.limits,
        o.billing_period_start,
        o.billing_period_end
    INTO 
        v_tier_limits,
        v_billing_start,
        v_billing_end
    FROM organizations o
    JOIN subscription_tiers st ON st.tier_name = o.subscription_tier
    WHERE o.id = p_organization_id;
    
    -- Get the limit for this metric
    v_metric_limit := (v_tier_limits->p_metric_name)::INTEGER;
    
    -- -1 means unlimited
    IF v_metric_limit = -1 THEN
        RETURN TRUE;
    END IF;
    
    -- Get current usage
    SELECT COALESCE(current_value, 0)
    INTO v_current_usage
    FROM organization_usage
    WHERE organization_id = p_organization_id
        AND metric_name = p_metric_name
        AND billing_period_start = v_billing_start;
    
    -- Check if adding increment would exceed limit
    RETURN (v_current_usage + p_increment) <= v_metric_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to increment usage
CREATE OR REPLACE FUNCTION increment_usage(
    p_organization_id UUID,
    p_metric_name VARCHAR,
    p_increment INTEGER DEFAULT 1
)
RETURNS VOID AS $$
DECLARE
    v_billing_start TIMESTAMP WITH TIME ZONE;
    v_billing_end TIMESTAMP WITH TIME ZONE;
    v_metric_limit INTEGER;
BEGIN
    -- Get organization billing period and limit
    SELECT 
        o.billing_period_start,
        o.billing_period_end,
        (st.limits->p_metric_name)::INTEGER
    INTO 
        v_billing_start,
        v_billing_end,
        v_metric_limit
    FROM organizations o
    JOIN subscription_tiers st ON st.tier_name = o.subscription_tier
    WHERE o.id = p_organization_id;
    
    -- Insert or update usage
    INSERT INTO organization_usage (
        organization_id,
        metric_name,
        current_value,
        limit_value,
        billing_period_start,
        billing_period_end
    )
    VALUES (
        p_organization_id,
        p_metric_name,
        p_increment,
        v_metric_limit,
        v_billing_start,
        v_billing_end
    )
    ON CONFLICT (organization_id, metric_name, billing_period_start)
    DO UPDATE SET
        current_value = organization_usage.current_value + p_increment,
        last_updated = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- Trigger to check limits before inserting content keys
CREATE OR REPLACE FUNCTION check_content_key_limit()
RETURNS TRIGGER AS $$
DECLARE
    v_org_id UUID;
    v_can_create BOOLEAN;
BEGIN
    -- Get organization ID from project
    SELECT p.organization_id
    INTO v_org_id
    FROM pages pg
    JOIN sites s ON s.id = pg.site_id
    JOIN projects p ON p.id = s.project_id
    WHERE pg.id = NEW.page_id;
    
    -- Check if organization can create more content keys
    SELECT check_usage_limit(v_org_id, 'content_keys', 1)
    INTO v_can_create;
    
    IF NOT v_can_create THEN
        RAISE EXCEPTION 'Content key limit exceeded for this organization';
    END IF;
    
    -- Increment usage counter
    PERFORM increment_usage(v_org_id, 'content_keys', 1);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_content_key_limit_trigger
    BEFORE INSERT ON content_keys
    FOR EACH ROW
    EXECUTE FUNCTION check_content_key_limit();

-- Views for dashboard and analytics
CREATE VIEW organization_usage_dashboard AS
SELECT 
    o.id as organization_id,
    o.name as organization_name,
    o.subscription_tier,
    st.display_name as tier_display_name,
    jsonb_build_object(
        'content_keys', ou_keys.current_value,
        'api_calls', ou_api.current_value,
        'projects', project_count.count,
        'team_members', member_count.count,
        'languages', language_count.count
    ) as current_usage,
    st.limits as tier_limits,
    o.billing_period_end as current_period_end
FROM organizations o
JOIN subscription_tiers st ON st.tier_name = o.subscription_tier
LEFT JOIN organization_usage ou_keys ON ou_keys.organization_id = o.id 
    AND ou_keys.metric_name = 'content_keys'
    AND ou_keys.billing_period_start = o.billing_period_start
LEFT JOIN organization_usage ou_api ON ou_api.organization_id = o.id 
    AND ou_api.metric_name = 'api_calls_per_hour'
    AND ou_api.billing_period_start = o.billing_period_start
LEFT JOIN (
    SELECT organization_id, COUNT(*) as count
    FROM projects WHERE deleted_at IS NULL
    GROUP BY organization_id
) project_count ON project_count.organization_id = o.id
LEFT JOIN (
    SELECT organization_id, COUNT(*) as count
    FROM organization_members WHERE is_active = true
    GROUP BY organization_id
) member_count ON member_count.organization_id = o.id
LEFT JOIN (
    SELECT DISTINCT p.organization_id, COUNT(DISTINCT l.code) as count
    FROM locales l
    JOIN translations t ON t.locale_id = l.id
    JOIN content_keys ck ON ck.id = t.content_key_id
    JOIN pages pg ON pg.id = ck.page_id
    JOIN sites s ON s.id = pg.site_id
    JOIN projects p ON p.id = s.project_id
    WHERE t.status = 'published'
    GROUP BY p.organization_id
) language_count ON language_count.organization_id = o.id
WHERE o.deleted_at IS NULL;

-- API quota management
CREATE OR REPLACE FUNCTION check_api_rate_limit(
    p_api_key_id UUID,
    p_organization_id UUID,
    p_ip_address INET
)
RETURNS BOOLEAN AS $$
DECLARE
    v_hour_start TIMESTAMP WITH TIME ZONE := date_trunc('hour', CURRENT_TIMESTAMP);
    v_api_key_limit INTEGER;
    v_org_limit INTEGER;
    v_current_count INTEGER;
BEGIN
    -- Get API key rate limit
    SELECT rate_limit INTO v_api_key_limit
    FROM api_keys
    WHERE id = p_api_key_id;
    
    -- Get organization rate limit from tier
    SELECT (st.limits->>'api_calls_per_hour')::INTEGER
    INTO v_org_limit
    FROM organizations o
    JOIN subscription_tiers st ON st.tier_name = o.subscription_tier
    WHERE o.id = p_organization_id;
    
    -- Use the lower of the two limits
    v_org_limit := LEAST(COALESCE(v_api_key_limit, v_org_limit), v_org_limit);
    
    -- Get current count for this hour
    SELECT COALESCE(request_count, 0)
    INTO v_current_count
    FROM api_rate_limits
    WHERE api_key_id = p_api_key_id
        AND window_start = v_hour_start;
    
    -- Check if under limit
    RETURN v_current_count < v_org_limit;
END;
$$ LANGUAGE plpgsql;

-- Function to record API call
CREATE OR REPLACE FUNCTION record_api_call(
    p_api_key_id UUID,
    p_organization_id UUID,
    p_ip_address INET
)
RETURNS VOID AS $$
DECLARE
    v_hour_start TIMESTAMP WITH TIME ZONE := date_trunc('hour', CURRENT_TIMESTAMP);
BEGIN
    -- Record the API call
    INSERT INTO api_rate_limits (api_key_id, organization_id, ip_address, window_start, request_count)
    VALUES (p_api_key_id, p_organization_id, p_ip_address, v_hour_start, 1)
    ON CONFLICT (api_key_id, window_start)
    DO UPDATE SET request_count = api_rate_limits.request_count + 1;
    
    -- Also update organization usage
    PERFORM increment_usage(p_organization_id, 'api_calls', 1);
    
    -- Update last used timestamp on API key
    UPDATE api_keys
    SET last_used_at = CURRENT_TIMESTAMP
    WHERE id = p_api_key_id;
END;
$$ LANGUAGE plpgsql;