# Database Schema Documentation

## Overview

This document describes the database schema for the ADC Multi-Languages application, a translation management system with tiered subscription capabilities.

## Core Tables

### Users
Stores user information and authentication details.
- `id`: UUID, primary key
- `email`: <PERSON><PERSON><PERSON><PERSON>(255), unique
- `username`: <PERSON><PERSON><PERSON><PERSON>(100), unique
- `password_hash`: <PERSON><PERSON><PERSON><PERSON>(255), bcrypt hash
- `full_name`: <PERSON><PERSON><PERSON><PERSON>(255)
- `profile_image_url`: <PERSON><PERSON><PERSON><PERSON>(255)
- `preferred_language`: VA<PERSON>HAR(10), default 'en'
- `is_active`: B<PERSON><PERSON><PERSON><PERSON>, default true
- `email_verified`: B<PERSON><PERSON><PERSON>N, default false
- `last_login_at`: TIMESTAMP WITH TIME ZONE
- `created_at`: TIMES<PERSON>MP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE

### Organizations
Stores organization information and subscription details.
- `id`: UUID, primary key
- `name`: <PERSON><PERSON><PERSON><PERSON>(255)
- `slug`: VA<PERSON><PERSON><PERSON>(100), unique
- `owner_id`: UUI<PERSON>, references users
- `subscription_tier`: <PERSON><PERSON><PERSON><PERSON>(50), default 'free'
- `subscription_status`: <PERSON><PERSON><PERSON>R(20), default 'active'
- `billing_period_start`: TIMES<PERSON>MP WITH TIME ZONE
- `billing_period_end`: TIMESTAMP WITH TIME ZONE
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE
- `deleted_at`: TIMESTAMP WITH TIME ZONE

### Subscription Tiers
Defines available subscription tiers and their limits.
- `id`: UUID, primary key
- `tier_name`: VARCHAR(50), unique
- `display_name`: VARCHAR(100)
- `description`: TEXT
- `monthly_price`: DECIMAL(10, 2)
- `yearly_price`: DECIMAL(10, 2)
- `limits`: JSONB, stores tier limits
- `features`: JSONB, stores tier features
- `is_active`: BOOLEAN, default true
- `sort_order`: INTEGER, default 0
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE

### Organization Members
Manages organization membership and roles.
- `id`: UUID, primary key
- `organization_id`: UUID, references organizations
- `user_id`: UUID, references users
- `role`: VARCHAR(50), default 'member'
- `invited_at`: TIMESTAMP WITH TIME ZONE
- `joined_at`: TIMESTAMP WITH TIME ZONE
- `is_active`: BOOLEAN, default true

## Project Management

### Projects
Stores translation projects.
- `id`: UUID, primary key
- `organization_id`: UUID, references organizations
- `name`: VARCHAR(255)
- `slug`: VARCHAR(100)
- `description`: TEXT
- `default_locale`: VARCHAR(10), default 'en'
- `is_public`: BOOLEAN, default false
- `created_by`: UUID, references users
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE
- `deleted_at`: TIMESTAMP WITH TIME ZONE

### Locales
Stores available languages/locales.
- `id`: UUID, primary key
- `code`: VARCHAR(10), unique
- `name`: VARCHAR(100)
- `native_name`: VARCHAR(100)
- `text_direction`: VARCHAR(3), default 'ltr'
- `is_active`: BOOLEAN, default true
- `created_at`: TIMESTAMP WITH TIME ZONE

### Project Locales
Maps locales to projects.
- `id`: UUID, primary key
- `project_id`: UUID, references projects
- `locale_id`: UUID, references locales
- `is_source`: BOOLEAN, default false
- `is_active`: BOOLEAN, default true
- `created_at`: TIMESTAMP WITH TIME ZONE

## Translation Management

### Resources
Stores translation resources (files, pages, etc.).
- `id`: UUID, primary key
- `project_id`: UUID, references projects
- `name`: VARCHAR(255)
- `type`: VARCHAR(50)
- `path`: VARCHAR(255)
- `description`: TEXT
- `created_by`: UUID, references users
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE
- `deleted_at`: TIMESTAMP WITH TIME ZONE

### Translation Keys
Stores translation keys.
- `id`: UUID, primary key
- `resource_id`: UUID, references resources
- `key_name`: VARCHAR(255)
- `description`: TEXT
- `context`: TEXT
- `is_plural`: BOOLEAN, default false
- `max_length`: INTEGER
- `screenshot_url`: VARCHAR(255)
- `created_by`: UUID, references users
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE
- `deleted_at`: TIMESTAMP WITH TIME ZONE

### Translations
Stores translations for keys in different locales.
- `id`: UUID, primary key
- `key_id`: UUID, references translation_keys
- `locale_id`: UUID, references locales
- `content`: TEXT
- `is_fuzzy`: BOOLEAN, default false
- `is_reviewed`: BOOLEAN, default false
- `reviewed_by`: UUID, references users
- `reviewed_at`: TIMESTAMP WITH TIME ZONE
- `created_by`: UUID, references users
- `created_at`: TIMESTAMP WITH TIME ZONE
- `updated_at`: TIMESTAMP WITH TIME ZONE

### Translation History
Tracks changes to translations.
- `id`: UUID, primary key
- `translation_id`: UUID, references translations
- `content`: TEXT
- `action`: VARCHAR(50)
- `performed_by`: UUID, references users
- `created_at`: TIMESTAMP WITH TIME ZONE

## Additional Features

### Machine Translation
- `mt_providers`: Stores MT provider information
- `organization_mt_settings`: Organization-specific MT settings

### Collaboration
- `translation_comments`: Comments on translations
- `glossary_terms`: Organization glossary terms
- `glossary_translations`: Translations of glossary terms

### Integration
- `webhooks`: Webhook configurations
- `webhook_logs`: Webhook delivery logs

### Usage & Billing
- `organization_usage`: Tracks resource usage
- `api_keys`: API authentication keys
- `api_rate_limits`: API rate limiting
- `billing_events`: Billing-related events

### Audit
- `audit_logs`: System-wide audit logging

## Relationships

- Users belong to Organizations through Organization Members
- Organizations have Projects
- Projects have Resources
- Resources have Translation Keys
- Translation Keys have Translations in different Locales
- Translations have History and Comments

## Usage Limits

Usage limits are enforced through database triggers and functions:
- `check_usage_limit`: Checks if an action would exceed limits
- `increment_usage`: Increments usage counters
- `check_content_key_limit`: Trigger to enforce content key limits
- `check_api_rate_limit`: Checks API rate limits
