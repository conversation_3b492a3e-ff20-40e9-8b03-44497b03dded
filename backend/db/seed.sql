-- Seed script for ADC Multi-Languages application
-- This script will create test data for development and testing

-- Clear existing data (if any)
TRUNCATE TABLE translation_history CASCADE;
TRUNCATE TABLE translations CASCADE;
TRUNCATE TABLE translation_keys CASCADE;
TRUNCATE TABLE resources CASCADE;
TRUNCATE TABLE project_locales CASCADE;
TRUNCATE TABLE projects CASCADE;
TRUNCATE TABLE organization_members CASCADE;
TRUNCATE TABLE organizations CASCADE;
TRUNCATE TABLE users CASCADE;

-- Insert test users
INSERT INTO users (id, email, username, password_hash, full_name, is_active, email_verified)
VALUES
    ('11111111-1111-1111-1111-111111111111', '<EMAIL>', 'admin', 
     '$2b$12$K8Y6F.6/Tg5Z/j8VB7ebAO6ebRvH/z0gUWO8Hj.OFQ8s1l5hULpjm', 'Admin User', true, true),
    ('22222222-2222-2222-2222-222222222222', '<EMAIL>', 'translator', 
     '$2b$12$K8Y6F.6/Tg5Z/j8VB7ebAO6ebRvH/z0gUWO8Hj.OFQ8s1l5hULpjm', 'Translator User', true, true);
-- Note: Password hash is for 'password123'

-- Insert test organization
INSERT INTO organizations (id, name, slug, owner_id, subscription_tier, subscription_status)
VALUES
    ('33333333-3333-3333-3333-333333333333', 'Test Organization', 'test-org', 
     '11111111-1111-1111-1111-111111111111', 'professional', 'active');

-- Insert organization members
INSERT INTO organization_members (id, organization_id, user_id, role, joined_at, is_active)
VALUES
    ('44444444-4444-4444-4444-444444444444', '33333333-3333-3333-3333-333333333333', 
     '11111111-1111-1111-1111-111111111111', 'admin', NOW(), true),
    ('55555555-5555-5555-5555-555555555555', '33333333-3333-3333-3333-333333333333', 
     '22222222-2222-2222-2222-222222222222', 'translator', NOW(), true);

-- Insert test project
INSERT INTO projects (id, organization_id, name, slug, description, default_locale, is_public, created_by)
VALUES
    ('66666666-6666-6666-6666-666666666666', '33333333-3333-3333-3333-333333333333', 
     'Website Translation', 'website', 'Website translation project', 'en', false, 
     '11111111-1111-1111-1111-111111111111');

-- Insert project locales (languages for the project)
INSERT INTO project_locales (id, project_id, locale_id, is_source, is_active)
VALUES
    -- English (source language)
    ('77777777-7777-7777-7777-777777777777', '66666666-6666-6666-6666-666666666666', 
     (SELECT id FROM locales WHERE code = 'en'), true, true),
    -- Spanish
    ('88888888-8888-8888-8888-888888888888', '66666666-6666-6666-6666-666666666666', 
     (SELECT id FROM locales WHERE code = 'es'), false, true),
    -- French
    ('99999999-9999-9999-9999-999999999999', '66666666-6666-6666-6666-666666666666', 
     (SELECT id FROM locales WHERE code = 'fr'), false, true);

-- Insert resources
INSERT INTO resources (id, project_id, name, type, path, description, created_by)
VALUES
    ('aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', '66666666-6666-6666-6666-666666666666', 
     'Homepage', 'page', '/home', 'Homepage translations', '11111111-1111-1111-1111-111111111111'),
    ('bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', '66666666-6666-6666-6666-666666666666', 
     'About Page', 'page', '/about', 'About page translations', '11111111-1111-1111-1111-111111111111');

-- Insert translation keys
INSERT INTO translation_keys (id, resource_id, key_name, description, context, created_by)
VALUES
    -- Homepage keys
    ('cccccccc-cccc-cccc-cccc-cccccccccccc', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
     'welcome_message', 'Welcome message on homepage', 'Shown at the top of the homepage', 
     '11111111-1111-1111-1111-111111111111'),
    ('dddddddd-dddd-dddd-dddd-dddddddddddd', 'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa', 
     'get_started_button', 'Get started button text', 'Call to action button', 
     '11111111-1111-1111-1111-111111111111'),
    -- About page keys
    ('eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
     'about_title', 'About page title', 'Main heading on about page', 
     '11111111-1111-1111-1111-111111111111'),
    ('ffffffff-ffff-ffff-ffff-ffffffffffff', 'bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb', 
     'about_description', 'About page description', 'Main content on about page', 
     '11111111-1111-1111-1111-111111111111');

-- Insert translations for English (source language)
INSERT INTO translations (id, key_id, locale_id, content, is_reviewed, created_by)
VALUES
    -- Homepage translations (English)
    ('11111111-aaaa-bbbb-cccc-dddddddddddd', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 
     (SELECT id FROM locales WHERE code = 'en'), 
     'Welcome to our website!', true, '11111111-1111-1111-1111-111111111111'),
    ('22222222-aaaa-bbbb-cccc-dddddddddddd', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 
     (SELECT id FROM locales WHERE code = 'en'), 
     'Get Started', true, '11111111-1111-1111-1111-111111111111'),
    -- About page translations (English)
    ('33333333-aaaa-bbbb-cccc-dddddddddddd', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 
     (SELECT id FROM locales WHERE code = 'en'), 
     'About Us', true, '11111111-1111-1111-1111-111111111111'),
    ('44444444-aaaa-bbbb-cccc-dddddddddddd', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 
     (SELECT id FROM locales WHERE code = 'en'), 
     'We are a company dedicated to providing the best service.', 
     true, '11111111-1111-1111-1111-111111111111');

-- Insert translations for Spanish
INSERT INTO translations (id, key_id, locale_id, content, is_reviewed, created_by)
VALUES
    -- Homepage translations (Spanish)
    ('55555555-aaaa-bbbb-cccc-dddddddddddd', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 
     (SELECT id FROM locales WHERE code = 'es'), 
     '¡Bienvenido a nuestro sitio web!', true, '22222222-2222-2222-2222-222222222222'),
    ('66666666-aaaa-bbbb-cccc-dddddddddddd', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 
     (SELECT id FROM locales WHERE code = 'es'), 
     'Comenzar', true, '22222222-2222-2222-2222-222222222222'),
    -- About page translations (Spanish)
    ('77777777-aaaa-bbbb-cccc-dddddddddddd', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 
     (SELECT id FROM locales WHERE code = 'es'), 
     'Sobre Nosotros', true, '22222222-2222-2222-2222-222222222222'),
    ('88888888-aaaa-bbbb-cccc-dddddddddddd', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 
     (SELECT id FROM locales WHERE code = 'es'), 
     'Somos una empresa dedicada a proporcionar el mejor servicio.', 
     true, '22222222-2222-2222-2222-222222222222');

-- Insert translations for French (some not reviewed yet)
INSERT INTO translations (id, key_id, locale_id, content, is_reviewed, created_by)
VALUES
    -- Homepage translations (French)
    ('99999999-aaaa-bbbb-cccc-dddddddddddd', 'cccccccc-cccc-cccc-cccc-cccccccccccc', 
     (SELECT id FROM locales WHERE code = 'fr'), 
     'Bienvenue sur notre site web!', true, '22222222-2222-2222-2222-222222222222'),
    ('aaaaaaaa-aaaa-bbbb-cccc-dddddddddddd', 'dddddddd-dddd-dddd-dddd-dddddddddddd', 
     (SELECT id FROM locales WHERE code = 'fr'), 
     'Commencer', true, '22222222-2222-2222-2222-222222222222'),
    -- About page translations (French - not reviewed)
    ('bbbbbbbb-aaaa-bbbb-cccc-dddddddddddd', 'eeeeeeee-eeee-eeee-eeee-eeeeeeeeeeee', 
     (SELECT id FROM locales WHERE code = 'fr'), 
     'À Propos de Nous', false, '22222222-2222-2222-2222-222222222222'),
    ('cccccccc-aaaa-bbbb-cccc-dddddddddddd', 'ffffffff-ffff-ffff-ffff-ffffffffffff', 
     (SELECT id FROM locales WHERE code = 'fr'), 
     'Nous sommes une entreprise dédiée à fournir le meilleur service.', 
     false, '22222222-2222-2222-2222-222222222222');

-- Insert translation history
INSERT INTO translation_history (id, translation_id, content, action, performed_by)
VALUES
    -- History for French about title
    ('dddddddd-aaaa-bbbb-cccc-dddddddddddd', 'bbbbbbbb-aaaa-bbbb-cccc-dddddddddddd', 
     'À Propos', 'create', '22222222-2222-2222-2222-222222222222'),
    ('eeeeeeee-aaaa-bbbb-cccc-dddddddddddd', 'bbbbbbbb-aaaa-bbbb-cccc-dddddddddddd', 
     'À Propos de Nous', 'update', '22222222-2222-2222-2222-222222222222');
