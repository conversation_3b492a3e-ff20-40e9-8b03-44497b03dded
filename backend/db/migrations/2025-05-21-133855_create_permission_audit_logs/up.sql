CREATE TABLE permission_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID NOT NULL REFERENCES api_keys(id),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    permission_key VARCHAR(255) NOT NULL,
    resource_path VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    granted BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

CREATE INDEX idx_permission_audit_logs_api_key_id ON permission_audit_logs(api_key_id);
CREATE INDEX idx_permission_audit_logs_organization_id ON permission_audit_logs(organization_id);
CREATE INDEX idx_permission_audit_logs_created_at ON permission_audit_logs(created_at);