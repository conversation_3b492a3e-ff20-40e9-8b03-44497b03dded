-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    username VA<PERSON>HA<PERSON>(100) NOT NULL UNIQUE,
    password_hash VARCHAR(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    profile_image_url VARCHAR(255),
    preferred_language VARCHAR(10) DEFAULT 'en',
    is_active BOOLEAN DEFAULT true,
    email_verified BOOLEAN DEFAULT false,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name <PERSON><PERSON>HA<PERSON>(255) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    owner_id UUID NOT NULL REFERENCES users(id),
    subscription_tier VARCHAR(50) NOT NULL DEFAULT 'free',
    subscription_status VARCHAR(20) DEFAULT 'active', -- active, cancelled, past_due
    billing_period_start TIMESTAMP WITH TIME ZONE,
    billing_period_end TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Subscription tiers table
CREATE TABLE subscription_tiers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tier_name VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    description TEXT,
    monthly_price DECIMAL(10, 2) NOT NULL DEFAULT 0,
    yearly_price DECIMAL(10, 2),
    limits JSONB NOT NULL DEFAULT '{}',
    features JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Organization members table
CREATE TABLE organization_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id),
    role VARCHAR(50) NOT NULL DEFAULT 'member', -- admin, member, translator, viewer
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    joined_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(organization_id, user_id)
);

-- Projects table
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) NOT NULL,
    description TEXT,
    default_locale VARCHAR(10) DEFAULT 'en',
    is_public BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(organization_id, slug)
);

-- Languages/Locales table
CREATE TABLE locales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(10) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    native_name VARCHAR(100) NOT NULL,
    text_direction VARCHAR(3) DEFAULT 'ltr', -- ltr or rtl
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Project locales (languages enabled for a project)
CREATE TABLE project_locales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    locale_id UUID NOT NULL REFERENCES locales(id),
    is_source BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(project_id, locale_id)
);

-- Translation resources (files, pages, etc.)
CREATE TABLE resources (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL, -- file, page, component, etc.
    path VARCHAR(255),
    description TEXT,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Translation keys
CREATE TABLE translation_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    resource_id UUID NOT NULL REFERENCES resources(id) ON DELETE CASCADE,
    key_name VARCHAR(255) NOT NULL,
    description TEXT,
    context TEXT,
    is_plural BOOLEAN DEFAULT false,
    max_length INTEGER,
    screenshot_url VARCHAR(255),
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(resource_id, key_name)
);

-- Translations
CREATE TABLE translations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key_id UUID NOT NULL REFERENCES translation_keys(id) ON DELETE CASCADE,
    locale_id UUID NOT NULL REFERENCES locales(id),
    content TEXT NOT NULL,
    is_fuzzy BOOLEAN DEFAULT false,
    is_reviewed BOOLEAN DEFAULT false,
    reviewed_by UUID REFERENCES users(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(key_id, locale_id)
);

-- Translation history
CREATE TABLE translation_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    translation_id UUID NOT NULL REFERENCES translations(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    action VARCHAR(50) NOT NULL, -- create, update, review, approve, reject
    performed_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Usage tracking table
CREATE TABLE organization_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL, -- content_keys, api_calls, storage_mb, etc.
    current_value INTEGER NOT NULL DEFAULT 0,
    limit_value INTEGER NOT NULL DEFAULT 0,
    billing_period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    billing_period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, metric_name, billing_period_start)
);

-- API keys table
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL,
    prefix VARCHAR(20) NOT NULL,
    permissions JSONB NOT NULL DEFAULT '[]',
    rate_limit INTEGER DEFAULT 1000, -- requests per hour
    expires_at TIMESTAMP WITH TIME ZONE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP WITH TIME ZONE,
    UNIQUE(key_hash)
);

-- Insert default subscription tiers
INSERT INTO subscription_tiers (tier_name, display_name, description, monthly_price, yearly_price, limits, features)
VALUES
('free', 'Free', 'Perfect for personal projects', 0, 0, 
    '{
        "content_keys": 1000,
        "api_calls_per_hour": 100,
        "projects": 1,
        "team_members": 1,
        "languages": 2,
        "storage_mb": 100,
        "mtranslate_chars": 10000
    }'::jsonb,
    '["Basic translation editor", "2 languages", "API access", "Community support"]'::jsonb),
    
('starter', 'Starter', 'For small teams and startups', 19, 190,
    '{
        "content_keys": 10000,
        "api_calls_per_hour": 1000,
        "projects": 3,
        "team_members": 5,
        "languages": 5,
        "storage_mb": 1000,
        "mtranslate_chars": 100000,
        "version_history": true
    }'::jsonb,
    '["Everything in Free", "Version history", "5 languages", "Team collaboration", "Email support"]'::jsonb),
    
('professional', 'Professional', 'For growing businesses', 99, 990,
    '{
        "content_keys": 50000,
        "api_calls_per_hour": 5000,
        "projects": 10,
        "team_members": 15,
        "languages": 20,
        "storage_mb": 10000,
        "mtranslate_chars": 500000,
        "version_history": true,
        "custom_workflows": true,
        "advanced_analytics": true
    }'::jsonb,
    '["Everything in Starter", "Advanced analytics", "Custom workflows", "Priority support", "SLA guarantee"]'::jsonb),
    
('enterprise', 'Enterprise', 'For large organizations', 499, 4990,
    '{
        "content_keys": -1,
        "api_calls_per_hour": -1,
        "projects": -1,
        "team_members": -1,
        "languages": -1,
        "storage_mb": -1,
        "mtranslate_chars": -1,
        "version_history": true,
        "custom_workflows": true,
        "advanced_analytics": true,
        "sso": true,
        "dedicated_support": true
    }'::jsonb,
    '["Everything in Professional", "Unlimited everything", "SSO", "Custom integrations", "Dedicated support", "Custom SLA"]'::jsonb);

-- Insert default locales
INSERT INTO locales (code, name, native_name, text_direction)
VALUES
    ('en', 'English', 'English', 'ltr'),
    ('es', 'Spanish', 'Español', 'ltr'),
    ('fr', 'French', 'Français', 'ltr'),
    ('de', 'German', 'Deutsch', 'ltr'),
    ('it', 'Italian', 'Italiano', 'ltr'),
    ('pt', 'Portuguese', 'Português', 'ltr'),
    ('ru', 'Russian', 'Русский', 'ltr'),
    ('zh', 'Chinese (Simplified)', '中文 (简体)', 'ltr'),
    ('ja', 'Japanese', '日本語', 'ltr'),
    ('ko', 'Korean', '한국어', 'ltr'),
    ('ar', 'Arabic', 'العربية', 'rtl'),
    ('hi', 'Hindi', 'हिन्दी', 'ltr');
