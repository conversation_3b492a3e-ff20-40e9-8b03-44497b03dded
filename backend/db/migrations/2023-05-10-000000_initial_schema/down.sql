-- Drop tables in reverse order to avoid foreign key constraints
DROP TABLE IF EXISTS translation_history;
DROP TABLE IF EXISTS translations;
DROP TABLE IF EXISTS translation_keys;
DROP TABLE IF EXISTS resources;
DROP TABLE IF EXISTS project_locales;
DROP TABLE IF EXISTS locales;
DROP TABLE IF EXISTS projects;
DROP TABLE IF EXISTS api_keys;
DROP TABLE IF EXISTS organization_usage;
DROP TABLE IF EXISTS organization_members;
DROP TABLE IF EXISTS subscription_tiers;
DROP TABLE IF EXISTS organizations;
DROP TABLE IF EXISTS users;
