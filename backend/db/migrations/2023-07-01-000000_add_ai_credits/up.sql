-- Add AI credits tables

-- Add AI credits fields to organizations table
ALTER TABLE organizations
ADD COLUMN ai_credits_monthly_allowance INTEGER DEFAULT 0,
ADD COLUMN ai_credits_remaining INTEGER DEFAULT 0,
ADD COLUMN ai_credits_reset_date TIMESTAMP WITH TIME ZONE;

-- Create AI credits transactions table
CREATE TABLE ai_credit_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    amount INTEGER NOT NULL, -- Positive for purchases, negative for usage
    transaction_type VARCHAR(50) NOT NULL, -- 'purchase', 'usage', 'subscription_reset', 'refund', etc.
    description TEXT,
    stripe_payment_intent_id VARCHAR(255), -- For purchases
    stripe_invoice_id VARCHAR(255), -- For subscription-based credits
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create AI credits usage table
CREATE TABLE ai_credit_usage (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID REFERENCES users(id),
    credits_used INTEGER NOT NULL,
    operation VARCHAR(50) NOT NULL, -- 'translation', etc.
    source_locale VARCHAR(10),
    target_locale VARCHAR(10),
    text_length INTEGER,
    model_used VARCHAR(50), -- 'gpt-4', etc.
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create AI credits pricing table
CREATE TABLE ai_credit_pricing (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    min_credits INTEGER NOT NULL,
    max_credits INTEGER,
    price_per_credit DECIMAL(10, 6) NOT NULL,
    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Insert default pricing tiers
INSERT INTO ai_credit_pricing (min_credits, max_credits, price_per_credit, currency)
VALUES
    (100, 999, 0.01, 'USD'),
    (1000, 9999, 0.008, 'USD'),
    (10000, NULL, 0.005, 'USD');

-- Create indexes for better performance
CREATE INDEX idx_ai_credit_transactions_org_id ON ai_credit_transactions(organization_id);
CREATE INDEX idx_ai_credit_usage_org_id ON ai_credit_usage(organization_id);
CREATE INDEX idx_ai_credit_usage_user_id ON ai_credit_usage(user_id);
