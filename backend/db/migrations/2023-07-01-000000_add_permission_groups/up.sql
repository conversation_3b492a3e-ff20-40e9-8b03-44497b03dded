-- Create permission groups table
CREATE TABLE permission_groups (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    permissions J<PERSON>NB NOT NULL DEFAULT '{}',
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id, name)
);

-- Add permission_group_id to api_keys table
ALTER TABLE api_keys ADD COLUMN permission_group_id UUID REFERENCES permission_groups(id) ON DELETE SET NULL;

-- Create index on permission_group_id
CREATE INDEX idx_api_keys_permission_group_id ON api_keys(permission_group_id);

-- Create audit log table for permission checks
CREATE TABLE permission_audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    api_key_id UUID REFERENCES api_keys(id) ON DELETE CASCADE,
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    permission_key VARCHAR(255) NOT NULL,
    granted BOOLEAN NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on api_key_id
CREATE INDEX idx_permission_audit_logs_api_key_id ON permission_audit_logs(api_key_id);

-- Create index on organization_id
CREATE INDEX idx_permission_audit_logs_organization_id ON permission_audit_logs(organization_id);
