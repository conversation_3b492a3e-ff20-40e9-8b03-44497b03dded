-- Add email logs table
CREATE TABLE email_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    email_type VARCHAR(50) NOT NULL, -- verification, password_reset, welcome, etc.
    recipient VARCHAR(255) NOT NULL,
    subject <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    body TEXT NOT NULL,
    status VARCHAR(50) NOT NULL, -- sent, failed, etc.
    provider VARCHAR(50) NOT NULL, -- brevo, etc.
    provider_message_id VARCHAR(255),
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    error_message TEXT
);

-- Add index on user_id
CREATE INDEX idx_email_logs_user_id ON email_logs(user_id);

-- Add index on email_type
CREATE INDEX idx_email_logs_type ON email_logs(email_type);

-- Add index on status
CREATE INDEX idx_email_logs_status ON email_logs(status);
