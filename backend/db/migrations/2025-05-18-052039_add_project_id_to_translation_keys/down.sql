-- Drop the unique index
DROP INDEX IF EXISTS translation_keys_project_id_key_name_idx;

-- Make resource_id NOT NULL again
UPDATE translation_keys SET resource_id = (
    SELECT resources.id FROM resources
    WHERE resources.project_id = translation_keys.project_id
    LIMIT 1
) WHERE resource_id IS NULL;

ALTER TABLE translation_keys ALTER COLUMN resource_id SET NOT NULL;

-- Drop the project_id column
ALTER TABLE translation_keys DROP COLUMN project_id;
