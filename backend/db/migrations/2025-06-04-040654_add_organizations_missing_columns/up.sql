-- Add missing columns to organizations table that are expected by the Rust model
ALTER TABLE organizations
ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS stripe_subscription_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS ai_credits_monthly_allowance INTEGER,
ADD COLUMN IF NOT EXISTS ai_credits_remaining INTEGER,
ADD COLUMN IF NOT EXISTS ai_credits_reset_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS description TEXT,
ADD COLUMN IF NOT EXISTS logo_url VARCHAR(255),
ADD COLUMN IF NOT EXISTS website VARCHAR(255),
ADD COLUMN IF NOT EXISTS subscription_tier_id UUID,
ADD COLUMN IF NOT EXISTS subscription_auto_renew BOOLEAN;
