-- Add project_id column to translation_keys table
ALTER TABLE translation_keys ADD COLUMN project_id UUID REFERENCES projects(id) ON DELETE CASCADE;

-- Update existing translation keys to set project_id based on their resource's project_id
UPDATE translation_keys
SET project_id = resources.project_id
FROM resources
WHERE translation_keys.resource_id = resources.id;

-- Make resource_id nullable
ALTER TABLE translation_keys ALTER COLUMN resource_id DROP NOT NULL;

-- Add a unique constraint for project_id and key_name
CREATE UNIQUE INDEX translation_keys_project_id_key_name_idx ON translation_keys (project_id, key_name) 
WHERE resource_id IS NULL AND deleted_at IS NULL;

-- Add an index for faster lookups by project_id
CREATE INDEX translation_keys_project_id_idx ON translation_keys (project_id);
