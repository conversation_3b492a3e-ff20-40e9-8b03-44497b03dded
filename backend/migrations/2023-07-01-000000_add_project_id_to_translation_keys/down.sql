-- Remove the indexes
DROP INDEX IF EXISTS translation_keys_project_id_key_name_idx;
DROP INDEX IF EXISTS translation_keys_project_id_idx;

-- Make resource_id NOT NULL again
UPDATE translation_keys
SET resource_id = (
    SELECT id FROM resources 
    WHERE resources.project_id = translation_keys.project_id 
    AND resources.name = 'Default' 
    LIMIT 1
)
WHERE resource_id IS NULL;

-- Create default resources for any projects that don't have one
INSERT INTO resources (id, project_id, name, type, path, description)
SELECT 
    uuid_generate_v4(), 
    tk.project_id, 
    'Default', 
    'default', 
    NULL, 
    'Default resource created during migration rollback'
FROM translation_keys tk
WHERE tk.resource_id IS NULL
AND NOT EXISTS (
    SELECT 1 FROM resources r 
    WHERE r.project_id = tk.project_id 
    AND r.name = 'Default'
)
GROUP BY tk.project_id;

-- Update any remaining NULL resource_id values
UPDATE translation_keys
SET resource_id = (
    SELECT id FROM resources 
    WHERE resources.project_id = translation_keys.project_id 
    AND resources.name = 'Default' 
    LIMIT 1
)
WHERE resource_id IS NULL;

-- Make resource_id NOT NULL
ALTER TABLE translation_keys ALTER COLUMN resource_id SET NOT NULL;

-- Remove project_id column
ALTER TABLE translation_keys DROP COLUMN project_id;
