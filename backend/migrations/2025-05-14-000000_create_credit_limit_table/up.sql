-- Create credit_limit table
CREATE TABLE credit_limit (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    monthly_limit INTEGER NOT NULL DEFAULT 5000,
    warning_threshold INTEGER NOT NULL DEFAULT 80,
    auto_purchase_enabled BOOLEAN NOT NULL DEFAULT false,
    auto_purchase_amount INTEGER NOT NULL DEFAULT 1000,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(organization_id)
);

-- <PERSON>reate trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_credit_limit_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_credit_limit_updated_at
BEFORE UPDATE ON credit_limit
FOR EACH ROW
EXECUTE FUNCTION update_credit_limit_updated_at();

-- Insert default credit limits for existing organizations
INSERT INTO credit_limit (organization_id, monthly_limit, warning_threshold)
SELECT id, COALESCE(ai_credits_monthly_allowance, 5000), 80
FROM organizations
WHERE deleted_at IS NULL
ON CONFLICT (organization_id) DO NOTHING;
