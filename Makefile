# ADC Multi-Languages Monorepo Makefile
# This Makefile provides commands to run services in the monorepo

.PHONY: all frontend backend dev setup clean help

# Default target
all: help

# Run both frontend and backend servers in parallel
dev:
	@echo "Starting both frontend and backend servers..."
	@make -j 2 frontend backend

# Run frontend server
frontend:
	@echo "Starting frontend server..."
	@cd frontend && bun dev --port 3300

# Run backend server
backend:
	@echo "Starting backend server..."
	@cd backend && cargo watch -x run

# Setup both frontend and backend
setup: setup-frontend setup-backend

# Setup frontend
setup-frontend:
	@echo "Setting up frontend..."
	@cd frontend && bun install

# Setup backend
setup-backend:
	@echo "Setting up backend..."
	@cd backend && cargo build
	@if [ ! -f backend/.env ]; then \
		cp backend/.env.example backend/.env; \
		echo "Created .env file from .env.example. Please update with your configuration."; \
	fi

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	@cd frontend && rm -rf .next
	@cd backend && cargo clean

# Display help information
help:
	@echo "ADC Multi-Languages Monorepo Makefile"
	@echo ""
	@echo "Available commands:"
	@echo "  make dev              - Run both frontend and backend servers"
	@echo "  make frontend         - Run only the frontend server"
	@echo "  make backend          - Run only the backend server"
	@echo "  make setup            - Setup both frontend and backend"
	@echo "  make setup-frontend   - Setup only the frontend"
	@echo "  make setup-backend    - Setup only the backend"
	@echo "  make clean            - Clean build artifacts"
	@echo "  make help             - Display this help message"
	@echo ""
	@echo "Note: The backend requires cargo-watch for auto-reloading."
	@echo "Install it with: cargo install cargo-watch"
